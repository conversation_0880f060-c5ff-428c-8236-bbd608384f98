import React, {useState} from 'react';
import {I18nextProvider} from 'react-i18next';
import RNBootSplash from 'react-native-bootsplash';
import Config from 'react-native-config';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import {Provider as JotaiProvider} from 'jotai';
import {GestureHandlerRootView} from 'react-native-gesture-handler';

import {NavigationContainer} from '@react-navigation/native';

import RootStack from './components/Nav/RootStack';
import {toastConfig} from './components/UI/ToastConfig';
import {SolanaClientProvider} from './hooks/useSolana/useSolanaClientProvider';
import {SolanaOwnerTokenAccountsProvider} from './hooks/useSolana/useSolanaOwnerTokenAccounts';
import {SolanaTokenListProvider} from './hooks/useSolana/useSolanaTokenListProvider';
import {idlFactory} from './libs/icp/service';
import {AccountProvider} from './hooks/useAccountProvider';
import {AuthProvider} from './hooks/useAuthProvider';
import {CardListProvider} from './hooks/useCardListProvider';
import {ScannerProvider} from './hooks/useScannerProvider';
import {IdentityProvider} from './hooks/useSIWPIdentity';
import {SwitchTokenModalProvider} from './hooks/useSwitchTokenModalProvider';
import {SwitchWalletLoadingModalProvider} from './hooks/useSwitchWalletLoadingModalProvider';
import {SwitchWalletModalProvider} from './hooks/useSwitchWalletModalProvider';
import {TokenPriceProvider} from './hooks/useTokenPriceProvider';
import i18n from './i18n/i18n';
import {initActor} from './AnonymousActor';
import {DEFAULT_AUTH_VALIDITY_PERIOD} from './constants';
import {setupDatabase} from './db';
import {StatusBar} from 'react-native';

import {NdefOrderProvider} from './hooks/useNdefOrderProvider';

initActor();

function App() {
  const [isDbReady, setDbReady] = useState(false);

  const initDb = async () => {
    setDbReady(false);
    await setupDatabase();
    setDbReady(true);
  };

  // const readNdefData = async () => {
  //   try {
  //     await NfcManager.requestTechnology(NfcTech.Ndef);
  //     const tag = await NfcManager.getTag();
  //     console.log('NDEF Tag:', tag);
  //     await NfcManager.cancelTechnologyRequest();
  //   } catch (error) {
  //     console.warn('NDEF Read Error:', error);
  //   }
  // };

  React.useEffect(() => {
    initDb();
  }, []);

  if (!isDbReady) {
    return <></>;
  }

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <IdentityProvider
        idlFactory={idlFactory}
        canisterId={Config.APP_CANISTER_ID}
        httpAgentOptions={
          Config.APP_MODE == 'prod'
            ? undefined
            : {
                host: Config.APP_CANISTER_HOST,
                fetch,
              }
        }
        isLocalNetwork={Config.APP_MODE != 'prod'}
        expiration={DEFAULT_AUTH_VALIDITY_PERIOD * 60 * 1000}>
        {/*expiration={60 * 1000}>*/}
        <I18nextProvider i18n={i18n}>
          <SolanaTokenListProvider>
            <SolanaClientProvider>
              <AuthProvider>
                <AccountProvider>
                  <SolanaOwnerTokenAccountsProvider>
                    <TokenPriceProvider>
                      <SafeAreaProvider>
                        <StatusBar
                          translucent
                          backgroundColor="transparent"
                          barStyle="light-content"
                        />
                        <JotaiProvider>
                          <ScannerProvider>
                            <NdefOrderProvider>
                              <SwitchWalletModalProvider>
                                <SwitchTokenModalProvider>
                                  <SwitchWalletLoadingModalProvider>
                                    <CardListProvider>
                                      <NavigationContainer
                                        onReady={() => {
                                          RNBootSplash.hide();
                                        }}>
                                        <RootStack></RootStack>
                                        <Toast config={toastConfig} />
                                      </NavigationContainer>
                                    </CardListProvider>
                                  </SwitchWalletLoadingModalProvider>
                                </SwitchTokenModalProvider>
                              </SwitchWalletModalProvider>
                            </NdefOrderProvider>
                          </ScannerProvider>
                        </JotaiProvider>
                      </SafeAreaProvider>
                    </TokenPriceProvider>
                  </SolanaOwnerTokenAccountsProvider>
                </AccountProvider>
              </AuthProvider>
            </SolanaClientProvider>
          </SolanaTokenListProvider>
        </I18nextProvider>
      </IdentityProvider>
    </GestureHandlerRootView>
  );
}

export default App;
