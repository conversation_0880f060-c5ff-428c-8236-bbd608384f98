import Config from 'react-native-config';

import {ActorSubclass} from '@dfinity/agent';

import {_SERVICE, createActor} from './libs/icp';

const appMode = Config.APP_MODE;
const canisterId = Config.APP_CANISTER_ID;
const canisterHost = Config.APP_CANISTER_HOST;

export let actor: ActorSubclass<_SERVICE>;
export const initActor = function () {
  actor =
    appMode == 'prod'
      ? createActor(canisterId)
      : createActor(canisterId, {
          agentOptions: {
            host: canisterHost,
            fetch,
          },
        });
};
