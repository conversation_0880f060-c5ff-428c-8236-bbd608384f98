import {UserWithCardNumber} from '../../db/models/userModel';
import {UserToken} from '../../db/models/userTokenModel';
import {PaymentOrder, PaymentReceipt} from '../../types/order';

export type BottomTabParamList = {
  Home: undefined;
  Crypto: undefined;
  OntaPay: undefined;
  History: undefined;
  More: undefined;
};

export type RootStackParamList = {
  Main: undefined;
  Home: undefined;
  Crypto: undefined;
  Login: undefined;
  Register: undefined;
  AddCard: undefined;
  Terms: undefined;
  Tokens: {type?: string};
  Send: {tokenInfo: UserToken};
  SendIcrc1: {tokenInfo: UserToken};
  Receive: {tokenInfo?: UserToken | undefined};
  ReceiveMain: {tokenInfo?: UserToken | undefined};
  SendConfirm: {
    toAddress: string;
    amount: string;
    token: UserToken;
  };
  CardList: undefined;
  CreateCardWallet: {cardNumber: string; cardTokenized: any};
  FinishRegister: {cardNumber: string; cardTokenized: any};
  Scanner: undefined;
  Payment: {orderId?: string; paynowId?: string};
  PaymentFromPosScan: {orderId: string; payerWallet: string; timestamp: number};
  PaymentReceipt: {paymentReceipt: PaymentReceipt};
  ManageWallet: undefined;
  WalletDetails: {walletInfo: UserWithCardNumber};
  TokenDetail: {tokenInfo: UserToken};
  History: undefined;
  Notification: undefined;
  NotificationDetail: undefined;
  About: undefined;
  KYC: undefined;
  Invite: undefined;
  Bridge: undefined;
  BridgeSuccess: {amount: string};
  MyAsset: undefined;
  PayWithScan: undefined;
};
