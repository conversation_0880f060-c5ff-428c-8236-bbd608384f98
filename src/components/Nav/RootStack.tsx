import {
  createNativeStackNavigator,
  NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {useAuthProvider} from '../../hooks/useAuthProvider';
import {primaryHeaderBackgroundColor} from '../../theme/default';
import FinishRegisterView from '../Views/Auth/FinishRegister';
import LoginView from '../Views/Auth/Login';
import {RegisterView} from '../Views/Auth/Register';
import {AddCardView} from '../Views/Card/AddCard';
import CardListView from '../Views/Card/CardList';
import CreateCardWalletView from '../Views/Card/CreateCardWallet';
import Confirm from '../Views/Send/confirm';
import {BackButton} from '../UI/BackButton';
import HistoryView from '../Views/History';
import ManageWalletView from '../Views/ManageWallet';
import PaymentView from '../Views/Payment';
import PaymentFromPosScan from '../Views/Payment/PaymentFromPosScan';
import PaymentReceiptView from '../Views/PaymentReceipt';
import ReceiveView from '../Views/Receive';
import ReceiveViewMain from '../Views/Receive/index1';
import ScannerView from '../Views/Scanner';
import SendView from '../Views/Send';
import SendIcrc1View from '../Views/Send/icrc1';
import TokenDetailView from '../Views/TokenDetail';
import Tokens from '../Views/Tokens';
import {WalletDetailsView} from '../Views/WalletDetails';
import {NotificationDetailView} from '../Views/NotificationDetail';
import BottomTabs from './BottomTabs';
import {RootStackParamList} from './routes';
import {NotificationView} from '../Views/Notification';
import {TermsView} from '../Views/Terms';
import AboutView from '../Views/About';
import InviteView from '../Views/Invite';
import BridgeView from '../Views/Bridge';
import BridgeSuccessView from '../Views/Bridge/Success';
import MyAsset from '../Views/MyAsset';
import PayWithScanView from '../Views/PayWithScan';

const Stack = createNativeStackNavigator<RootStackParamList>();

const screenOptions: NativeStackNavigationOptions = {
  animationTypeForReplace: 'pop',
  headerLeft: () => <BackButton></BackButton>,
  headerStyle: {
    backgroundColor: primaryHeaderBackgroundColor,
  },
  headerTintColor: '#fff',
  headerTitleStyle: {
    // fontWeight: 'bold',
    fontSize: 16,
  },
  headerTitleAlign: 'center', // 标题居中显示
  animation: 'ios_from_right',
};

function RootStack() {
  const [isReady, setIsReady] = useState<boolean>(false);
  const {isLoggedIn} = useAuthProvider();
  const {t} = useTranslation();

  useEffect(() => {
    if (isLoggedIn !== undefined) {
      setIsReady(true);
    }
  }, [isLoggedIn]);

  if (!isReady) {
    return null;
  }

  return (
    <Stack.Navigator screenOptions={screenOptions}>
      {isLoggedIn ? (
        <>
          <Stack.Screen
            name="Main"
            component={BottomTabs}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="AddCard"
            component={AddCardView}
            options={{title: t('addCard')}}
          />
          <Stack.Screen
            name="CreateCardWallet"
            options={{
              title: t('confirmTitle'),
            }}
            component={CreateCardWalletView}
          />
          <Stack.Screen
            name="Tokens"
            component={Tokens}
            options={{title: 'Tokens'}}
          />
          <Stack.Screen
            name="Send"
            component={SendView}
            options={{title: 'Send'}}
          />
          <Stack.Screen
            name="SendIcrc1"
            component={SendIcrc1View}
            options={{title: 'Send'}}
          />
          <Stack.Screen
            name="SendConfirm"
            component={Confirm}
            options={{title: t('confirmSend')}}
          />
          <Stack.Screen
            name="CardList"
            options={{
              title: t('cardList'),
            }}
            component={CardListView}
          />
          <Stack.Screen
            name="Scanner"
            options={{
              headerShown: false,
            }}
            component={ScannerView}
          />
          <Stack.Screen
            name="PaymentFromPosScan"
            options={{
              title: t('payTitle'),
            }}
            component={PaymentFromPosScan}
          />
          <Stack.Screen
            name="Payment"
            options={{
              title: t('payTitle'),
            }}
            component={PaymentView}
          />
          <Stack.Screen
            name="PaymentReceipt"
            options={{
              title: t('paymentTitle'),
            }}
            component={PaymentReceiptView}></Stack.Screen>
          <Stack.Screen
            name="ManageWallet"
            options={{
              title: t('mngWallets'),
            }}
            component={ManageWalletView}></Stack.Screen>
          <Stack.Screen
            name="Invite"
            options={{
              title: t('invite'),
            }}
            component={InviteView}
          />
          <Stack.Screen
            name="Receive"
            options={{
              title: '',
            }}
            component={ReceiveView}></Stack.Screen>
          <Stack.Screen
            name="ReceiveMain"
            options={{
              title: '',
            }}
            component={ReceiveViewMain}></Stack.Screen>
          <Stack.Screen
            name="WalletDetails"
            options={{
              title: t('walletDetails'),
            }}
            component={WalletDetailsView}></Stack.Screen>
          <Stack.Screen
            name="TokenDetail"
            options={{
              title: '',
            }}
            component={TokenDetailView}></Stack.Screen>
          <Stack.Screen
            name="History"
            options={{
              title: 'History',
            }}
            component={HistoryView}></Stack.Screen>
          <Stack.Screen
            name="Notification"
            options={{
              title: t('notificationTitle'),
            }}
            component={NotificationView}
          />
          <Stack.Screen
            name="NotificationDetail"
            options={{
              title: t('notificationDetailTitle'),
            }}
            component={NotificationDetailView}
          />
          <Stack.Screen
            name="About"
            options={{
              title: t('aboutUs'),
            }}
            component={AboutView}
          />
          <Stack.Screen
            name="Bridge"
            options={{
              title: t('Top-up'),
            }}
            component={BridgeView}
          />
          <Stack.Screen
            name="BridgeSuccess"
            options={{
              title: t('BridgeSuccess'),
              gestureEnabled: false,
              headerBackVisible: false,
            }}
            component={BridgeSuccessView}
          />
          <Stack.Screen
            name="MyAsset"
            component={MyAsset}
            options={{title: 'My Asset'}}
          />
          <Stack.Screen
            name="PayWithScan"
            component={PayWithScanView}
            options={{title: t('payWithScan')}}
          />
        </>
      ) : (
        <>
          <Stack.Screen
            name="Login"
            options={{
              headerShown: false,
            }}
            component={LoginView}
          />
          <Stack.Screen
            name="Register"
            options={{
              title: t('registerWithCard'),
            }}
            component={RegisterView}
          />
          <Stack.Screen
            name="FinishRegister"
            options={{
              title: t('finishRegister'),
            }}
            component={FinishRegisterView}
          />
          <Stack.Screen
            name="Terms"
            options={{
              title: '',
            }}
            component={TermsView}
          />
        </>
      )}
    </Stack.Navigator>
  );
}

export default RootStack;
