import {primaryHeaderBackgroundColor, primaryColor2} from '../../theme/default';
import React from 'react';

import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {NavigationProp} from '@react-navigation/native';
import TabBar from '../UI/TabBar/TabBar';
import Wallet from '../Views/Wallet';
import History from '../Views/History';
import TabScannerView from '../Views/Scanner/TabScannerView';
import More from '../Views/More';
import Crypto from '../Views/Crypto';
import {BottomTabParamList, RootStackParamList} from './routes';

const Tab = createBottomTabNavigator<BottomTabParamList>();

export type RootStackNavigationProp = NavigationProp<RootStackParamList>;

function BottomTabs() {
  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={{
        tabBarStyle: {
          backgroundColor: 'transparent',
        },
      }}
      tabBar={props => <TabBar {...props} />}>
      <Tab.Screen
        name="Home"
        options={{
          tabBarLabel: 'Home',
          headerTitle: '',
          headerShown: false,
          headerStyle: {
            backgroundColor: primaryHeaderBackgroundColor,
            height: 110,
          },
        }}
        component={Wallet}
      />

      <Tab.Screen
        name="Crypto"
        component={Crypto}
        options={{
          tabBarLabel: 'Crypto',
          headerTitleAlign: 'center',
          headerTitle: '',
          headerShown: false,
          headerTitleStyle: {
            color: primaryColor2,
            fontSize: 16,
          },
          headerStyle: {
            backgroundColor: primaryHeaderBackgroundColor,
          },
        }}
      />
      <Tab.Screen
        name="OntaPay"
        component={TabScannerView}
        options={{
          tabBarLabel: 'OntaPay',
          headerShown: false, // 隐藏头部
        }}
      />
      <Tab.Screen
        name="History"
        component={History}
        options={{
          tabBarLabel: 'History',
          headerTitleAlign: 'center',
          headerTitleStyle: {
            color: primaryColor2,
            fontSize: 16,
          },
          headerStyle: {
            backgroundColor: primaryHeaderBackgroundColor,
          },
        }}
      />
      <Tab.Screen
        name="More"
        component={More}
        options={{
          headerTitle: '',
          headerStyle: {
            backgroundColor: primaryHeaderBackgroundColor,
            height: 30,
          },
        }}
      />
    </Tab.Navigator>
  );
}

export default BottomTabs;
