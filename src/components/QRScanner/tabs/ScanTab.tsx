import React, {useRef, useEffect} from 'react';
import {StyleSheet, View, Animated, Dimensions} from 'react-native';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
} from 'react-native-vision-camera';
import LinearGradient from 'react-native-linear-gradient';
import {useScanResult} from '../../../hooks/useScanResult';

const {height, width} = Dimensions.get('window');

interface ScanTabProps {
  torch: 'off' | 'on';
  onScanResult: (value: string | undefined | null) => void;
  scannerLinePosition: Animated.Value;
}

export default function ScanTab({
  torch,
  onScanResult,
  scannerLinePosition,
}: ScanTabProps) {
  const device = useCameraDevice('back');
  const cameraRef = useRef<Camera>(null);

  // Use custom hook to handle scan results
  const {handleCodeScanned, cleanup} = useScanResult({
    debounceTime: 2000,
    onScanResult: value => {
      console.log(`onCodeScanned value`, value);
      onScanResult(value);
    },
  });

  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: handleCodeScanned,
  });

  // Clean up state when component unmounts
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  if (!device) {
    return null;
  }

  return (
    <>
      <Camera
        torch={torch ?? 'off'}
        ref={cameraRef}
        codeScanner={codeScanner}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        enableZoomGesture
      />
      {/* Semi-transparent mask */}
      <View style={styles.overlay}>
        <View style={styles.overlayTop} />
        <View style={styles.overlayMiddle}>
          <View style={styles.overlayLeft} />
          <View style={styles.scanArea} />
          <View style={styles.overlayRight} />
        </View>
        <View style={styles.overlayBottom} />
      </View>
      {/* Scan frame corners */}
      <View style={styles.scanFrame}>
        <View style={styles.scanFrameCorner} />
        <View style={[styles.scanFrameCorner, styles.topRight]} />
        <View style={[styles.scanFrameCorner, styles.bottomLeft]} />
        <View style={[styles.scanFrameCorner, styles.bottomRight]} />
      </View>
      <Animated.View
        style={[
          styles.scannerLine,
          {
            transform: [
              {
                translateY: scannerLinePosition,
              },
            ],
          },
        ]}>
        <LinearGradient
          colors={['rgba(0, 255, 0, 0)', 'rgba(0, 255, 0, 0.5)']}
          style={styles.gradient}></LinearGradient>
      </Animated.View>
    </>
  );
}

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerLine: {
    position: 'absolute',
    top: height * 0.3,
    left: (width - 250) / 2,
    width: 250,
    height: 2,
    backgroundColor: 'transparent',
  },
  // Overlay styles for mask effect
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 50,
  },
  overlayTop: {
    height: height * 0.35,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlayMiddle: {
    height: 250,
    flexDirection: 'row',
  },
  overlayLeft: {
    width: (width - 250) / 2,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  scanArea: {
    width: 250,
    height: 250,
    backgroundColor: 'transparent',
  },
  overlayRight: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlayBottom: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  // Scan frame styles
  scanFrame: {
    position: 'absolute',
    top: height * 0.35,
    left: (width - 250) / 2,
    width: 250,
    height: 250,
    zIndex: 100,
  },
  scanFrameCorner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#007AFF',
    borderWidth: 4,
    borderTopWidth: 4,
    borderLeftWidth: 4,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: 8,
    top: 0,
    left: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    left: 'auto',
    borderTopWidth: 4,
    borderRightWidth: 4,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: 8,
    borderTopLeftRadius: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    top: 'auto',
    borderBottomWidth: 4,
    borderLeftWidth: 4,
    borderTopWidth: 0,
    borderRightWidth: 0,
    borderBottomLeftRadius: 8,
    borderTopLeftRadius: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    top: 'auto',
    left: 'auto',
    borderBottomWidth: 4,
    borderRightWidth: 4,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderBottomRightRadius: 8,
    borderTopLeftRadius: 0,
  },
});
