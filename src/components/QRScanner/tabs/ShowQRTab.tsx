import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View, Image} from 'react-native';
import QRCode from 'react-qr-code';
import {primaryColor3} from '../../../theme/default';
import {useAccountProvider} from '../../../hooks/useAccountProvider';
import {useAuthProvider} from '../../../hooks/useAuthProvider';
import {toShortAddress} from '../../../utils/index';
import {getPosScanOrder} from '../../../api/backend/kyc';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';

export default function ShowQRTab({isActive}: {isActive: boolean}) {
  const {userInfo} = useAccountProvider();
  const {authToken} = useAuthProvider();
  const [qrCodeValue, setQrCodeValue] = useState<string | null>(null);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const logo = require('../../../assets/appicon/<EMAIL>');

  useEffect(() => {
    if (isActive && userInfo && authToken) {
      // 设置二维码值为用户的 Solana 地址
      setQrCodeValue(userInfo.solanaAddress);
      const scanTime = Date.now();

      const refreshPosScanOrder = () => {
        getPosScanOrder(
          {
            limit: 1,
            payerWallet: userInfo.solanaAddress,
            timestamp: scanTime,
          },
          authToken,
        ).then(result => {
          console.log('getPosScanOrder result', result);
          if (result && result.data && result.data.length > 0) {
            const newOrderData = result.data[0];
            if (newOrderData && newOrderData.orderId) {
              navigation.navigate('PaymentFromPosScan', {
                orderId: newOrderData.orderId,
                payerWallet: userInfo.solanaAddress,
                timestamp: scanTime,
              });
            }
          }
        });
      };

      // 立即执行一次检查
      refreshPosScanOrder();
      // 每秒检查一次是否有新的订单
      const interval = setInterval(refreshPosScanOrder, 1000);

      return () => clearInterval(interval);
    }
  }, [isActive, userInfo, authToken, navigation]);

  return (
    <View style={styles.showQRContainer}>
      <View style={styles.qrWrapper}>
        <View style={styles.qrContainer}>
          {qrCodeValue && (
            <View>
              <QRCode
                size={225}
                style={{
                  height: 'auto',
                  maxWidth: '100%',
                  width: '100%',
                }}
                bgColor={'transparent'}
                fgColor={'#000'}
                value={qrCodeValue}
                viewBox={`0 0 225 225`}
              />
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: '#fff',
                  position: 'absolute',
                  width: 40,
                  height: 40,
                  top: '50%',
                  left: '50%',
                  transform: [{translateX: -20}, {translateY: -20}],
                  zIndex: 1000,
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: primaryColor3,
                }}>
                <Image
                  source={logo}
                  style={{
                    width: 32,
                    height: 32,
                    borderRadius: 8,
                    resizeMode: 'contain',
                  }}
                />
              </View>
            </View>
          )}
        </View>
        <Text style={styles.address}>{toShortAddress(qrCodeValue ?? '')}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  showQRContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  qrWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrContainer: {
    padding: 24,
    backgroundColor: '#fff',
    borderRadius: 24,
    marginBottom: 18,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  address: {
    color: '#aaa',
    fontSize: 16,
    marginTop: 8,
    letterSpacing: 1,
  },
});
