import React, { useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { useNdefOrderProvider } from '../../../hooks/useNdefOrderProvider';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../Nav/routes';

export default function NFCTab() {
  const { iosReadNdefOnce } = useNdefOrderProvider();
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  useEffect(() => {
    iosReadNdefOnce(navigation);
  }, []);

  return (
    <View style={styles.tabPlaceholder}>
      <Text style={styles.placeholderText}>NFC Pay</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  tabPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 18,
    fontWeight: '500',
  },
});
