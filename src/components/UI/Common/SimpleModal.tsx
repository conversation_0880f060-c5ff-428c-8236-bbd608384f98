import {Modal, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  primaryHeaderBackgroundColor,
  secondaryBackgroundColor,
  secondaryHoverBackgroundColor,
  primaryColor2,
  primarHoverBackgroundColor2,
} from '../../../theme/default';

type SimpleModalProp = {
  leftButtonTitle?: string;
  rightButtonTitle?: string;
  title: string;
  content: string;
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
};

import CloseIcon from '../../../assets/icons/close.svg';

export default function SimpleModal({
  leftButtonTitle,
  rightButtonTitle,
  title,
  content,
  visible,
  onConfirm,
  onCancel,
}: Readonly<SimpleModalProp>) {
  //
  return (
    <Modal transparent={true} visible={visible} animationType="slide">
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text
              style={{fontSize: 16, fontWeight: '600', color: primaryColor2}}>
              {title}
            </Text>
            <TouchableOpacity
              onPress={() => {
                onCancel();
              }}
              style={{
                position: 'absolute',
                justifyContent: 'center',
                alignItems: 'center',
                right: 24,
              }}>
              <CloseIcon height={22} width={22} color={primaryColor2} />
            </TouchableOpacity>
          </View>
          <View style={styles.mainContent}>
            <View style={styles.content}>
              <Text style={{color: primaryColor2, fontSize: 16}}>
                {content}
              </Text>
            </View>
            <View style={styles.footer}>
              <TouchableOpacity
                onPress={onCancel}
                style={{
                  width: '48%',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: primaryColor2,
                    fontSize: 16,
                    fontWeight: '600',
                  }}>
                  {leftButtonTitle ?? 'Cancel'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={onConfirm}
                style={{
                  width: '48%',
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderLeftWidth: 1,
                  borderLeftColor: primarHoverBackgroundColor2,
                }}>
                <Text
                  style={{
                    color: primaryColor2,
                    fontSize: 16,
                    fontWeight: '600',
                  }}>
                  {rightButtonTitle ?? 'OK'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: primaryHeaderBackgroundColor,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '30%',
  },
  header: {
    width: '100%',
    height: 50,
    backgroundColor: secondaryBackgroundColor,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderBottomWidth: 1,
    borderBottomColor: secondaryHoverBackgroundColor,
  },
  footer: {
    width: '100%',
    height: 64,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: primarHoverBackgroundColor2,
  },
  mainContent: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    width: '100%',
    padding: 16,
    margin: 16,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
});
