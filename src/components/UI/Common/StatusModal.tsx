import React, {useEffect, useRef} from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Platform,
  ImageBackground,
  Animated,
  Easing,
} from 'react-native';
import SuccessBg from '../../../assets/images/success-bg.png';
import RotateIcon from '../../../assets/icons/rotate.svg';

export type StatusModalProps = {
  visible: boolean;
  status?: 'normal' | 'success';
  onClose: () => void;
  onSuccess?: () => void;
  message?: string;
  successMessage?: string;
  logo?: any;
  logo1?: any;
};

const StatusModal: React.FC<StatusModalProps> = ({
  visible,
  status = 'normal',
  onClose,
  onSuccess,
  message = 'Send USDC',
  successMessage = 'USDC Sent',
  logo,
  logo1,
}) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible && status === 'normal') {
      rotateAnim.setValue(0);
      Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
          easing: Easing.linear,
        }),
      ).start();
    } else {
      rotateAnim.stopAnimation();
      rotateAnim.setValue(0);
    }
  }, [visible, status, rotateAnim]);

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  const Content = (
    <View style={styles.centerContent}>
      {(logo || logo1) && (
        <View style={styles.logoContainer}>
          {logo && (
            <View style={styles.logoWrapper}>
              {typeof logo === 'string' || logo?.uri ? (
                <Image source={logo} style={styles.logo} />
              ) : (
                <View style={styles.logo}>{logo}</View>
              )}
              {status === 'normal' && !logo1 && (
                <Animated.View
                  style={[
                    styles.rotateIconContainer,
                    {transform: [{rotate: spin}]},
                  ]}>
                  <RotateIcon width={20} height={20} fill="#fff" />
                </Animated.View>
              )}
            </View>
          )}
          {logo1 && (
            <View style={styles.logo1Container}>
              {typeof logo1 === 'string' || logo1?.uri ? (
                <Image source={logo1} style={styles.logo1} />
              ) : (
                <View style={styles.logo1}>{logo1}</View>
              )}
              {status === 'normal' && (
                <Animated.View
                  style={[
                    styles.rotateIconContainer,
                    {transform: [{rotate: spin}]},
                  ]}>
                  <RotateIcon width={20} height={20} fill="#fff" />
                </Animated.View>
              )}
            </View>
          )}
        </View>
      )}
      <Text style={styles.text}>
        {status === 'success' ? successMessage : message}
      </Text>
    </View>
  );

  return (
    <Modal
      style={{zIndex: 1000}}
      visible={visible}
      transparent
      animationType="fade">
      {status === 'success' ? (
        <ImageBackground
          source={SuccessBg}
          style={styles.overlay}
          resizeMode="cover">
          {Content}
          <TouchableOpacity
            style={styles.button}
            onPress={onSuccess}
            activeOpacity={0.8}>
            <Text style={styles.buttonText}>Close</Text>
          </TouchableOpacity>
        </ImageBackground>
      ) : (
        <View style={styles.overlay}>
          {Content}
          <TouchableOpacity
            style={styles.button}
            onPress={onClose}
            activeOpacity={0.8}>
            <Text style={styles.buttonText}>Close</Text>
          </TouchableOpacity>
        </View>
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 40 : 24,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  logoWrapper: {
    position: 'relative',
  },
  logo: {
    width: 56,
    height: 56,
    marginRight: 8,
  },
  logo1: {
    width: 60,
    height: 60,
    marginLeft: -30,
    borderWidth: 3,
    borderColor: '#000000',
    borderRadius: 60,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  logo1Container: {
    position: 'relative',
  },
  rotateIconContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 1,
  },
  text: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  button: {
    width: '90%',
    height: 48,
    backgroundColor: '#fff',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: Platform.OS === 'ios' ? 32 : 16,
  },
  buttonText: {
    color: '#000',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default StatusModal;
