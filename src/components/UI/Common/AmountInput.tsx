import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import ArrowIcn1 from '../../../assets/icons/arrow-1.svg';

export interface AmountInputProps {
  value: string;
  onChangeText: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  validationError?: string | null;
  showFiatValue?: boolean;
  fiatValue?: string;
  onSwap?: () => void;
  showSwapButton?: boolean;
  inputStyle?: any;
  containerStyle?: any;
  variant?: 'bridge' | 'send';
  onFocus?: () => void;
  onBlur?: () => void;
}

const AmountInput: React.FC<AmountInputProps> = ({
  value,
  onChangeText,
  placeholder = '0',
  maxLength = 20,
  validationError,
  showFiatValue = false,
  fiatValue,
  onSwap,
  showSwapButton = false,
  inputStyle,
  containerStyle,
  variant = 'bridge',
  onFocus,
  onBlur,
}) => {
  const isBridgeVariant = variant === 'bridge';

  return (
    <View
      style={[
        styles.container,
        isBridgeVariant ? styles.bridgeContainer : styles.sendContainer,
        containerStyle,
      ]}>
      <View style={styles.inputMain}>
        <TextInput
          style={[
            styles.inputNumber,
            isBridgeVariant ? styles.bridgeInput : styles.sendInput,
            inputStyle,
          ]}
          value={value}
          onChangeText={onChangeText}
          keyboardType="numeric"
          placeholder={placeholder}
          placeholderTextColor="rgba(255,255,255,0.3)"
          textAlign="center"
          maxLength={maxLength}
          onFocus={onFocus}
          onBlur={onBlur}
        />
        {/* Warning */}
        {validationError && (
          <Text style={styles.warning}>{validationError}</Text>
        )}
        {!validationError && showFiatValue && (
          <Text style={styles.fiatValue}>
            {fiatValue || `= $${parseFloat(value || '0').toFixed(2)}`}
          </Text>
        )}
      </View>

      {showSwapButton && onSwap && (
        <TouchableOpacity style={styles.swapButton} onPress={onSwap}>
          <ArrowIcn1 width={16} height={14} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    padding: 16,
    marginBottom: 16,
  },
  bridgeContainer: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    paddingHorizontal: 0,
    marginBottom: 8,
    marginTop: 8,
    position: 'relative',
  },
  sendContainer: {},
  inputMain: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  inputNumber: {
    color: '#fff',
    textAlign: 'center',
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingVertical: 0,
    paddingHorizontal: 0,
    margin: 0,
    includeFontPadding: false,
    width: '100%',
  },
  bridgeInput: {
    fontSize: 56,
    fontWeight: '600',
    lineHeight: 72,
  },
  sendInput: {
    fontSize: 56,
    fontWeight: '600',
    lineHeight: 72,
  },
  warning: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  fiatValue: {
    color: 'rgba(255,255,255,0.6)',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  swapButton: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{translateY: -8}],
  },
});

export default AmountInput;
