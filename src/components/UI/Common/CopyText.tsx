import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import CopyIcon from '../../../assets/icons/copy-1.svg';
import SuccessIcon from '../../../assets/icons/success.svg';

interface CopyTextProps {
  value: string;
  textStyle?: TextStyle;
  containerStyle?: ViewStyle;
  iconSize?: number;
  onCopied?: () => void;
}

const CopyText: React.FC<CopyTextProps> = ({
  value,
  textStyle,
  containerStyle,
  iconSize = 24,
  onCopied,
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    Clipboard.setString(value);
    setCopied(true);
    if (onCopied) onCopied();
    setTimeout(() => setCopied(false), 1200);
  };

  return copied ? (
    <View style={styles.copiedContainer}>
      <SuccessIcon width={18} height={18} style={{marginRight: 6}} />
      <Text style={styles.copiedText}>Address copied</Text>
    </View>
  ) : (
    <View style={[styles.row, containerStyle]}>
      <Text
        style={[styles.text, textStyle]}
        numberOfLines={1}
        ellipsizeMode="middle">
        {value}
      </Text>
      <TouchableOpacity
        onPress={handleCopy}
        style={styles.iconBtn}
        activeOpacity={0.7}>
        <CopyIcon width={iconSize} height={iconSize} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '500',
    marginRight: 8,
    maxWidth: '80%',
  },
  iconBtn: {
    padding: 4,
  },
  copiedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 32,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignSelf: 'center',
  },
  copiedText: {
    color: 'rgba(255,255,255,0.4)',
    fontSize: 17,
    fontWeight: '400',
    marginLeft: 4,
  },
});

export default CopyText;
