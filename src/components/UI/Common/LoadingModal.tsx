import {ActivityIndicator, Modal, StyleSheet, View} from 'react-native';
import {
  primaryColor,
  primaryHeaderBackgroundColor,
} from '../../../theme/default';
import LottieView from 'lottie-react-native';
import loadingAnimation from '../../../assets/icons/loading.json';

type LoadingModalProp = {
  visible: boolean;
  borderColor?: string;
};

export default function LoadingModal({
  visible,
  borderColor,
}: Readonly<LoadingModalProp>) {
  return (
    <Modal transparent={true} visible={visible}>
      <View style={styles.modalOverlay}>
        <View
          style={{
            ...styles.modalContent,
            borderColor: '#fff',
          }}>
          <LottieView
            source={loadingAnimation}
            style={styles.loadingIcon}
            autoPlay
            loop
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 10,
    borderWidth: 1,
    width: 96,
    alignItems: 'center',
    justifyContent: 'center',
    height: 96,
  },
  loadingIcon: {
    width: 24,
    height: 24,
  },
});
