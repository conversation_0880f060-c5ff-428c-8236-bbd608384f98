import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import NoticeIcon from '../../assets/icons/notice.svg';
import CloseIcon from '../../assets/icons/close-1.svg';
import {getStatusBarHeight} from '../../utils';

interface ToastProps {
  visible: boolean;
  message: string;
  onClose?: () => void;
}

const Toast: React.FC<ToastProps> = ({visible, message, onClose}) => {
  if (!visible) return null;
  return (
    <View style={{...styles.container, top: getStatusBarHeight() + 10}}>
      <View style={styles.toast}>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeBtn}>
            <CloseIcon width={20} height={20} />
          </TouchableOpacity>
        )}
        <Text style={styles.text}>{message || 'Something went wrong'}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 9999,
    paddingHorizontal: 15,
  },
  toast: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(220, 34, 34, 1)',
    borderRadius: 10,
    paddingVertical: 4,
    paddingHorizontal: 10,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  text: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  closeBtn: {
    marginLeft: 12,
    padding: 4,
  },
});

export default Toast;
