import {
  primaryBackgroundColor2,
  primaryColor,
  secondaryColor,
} from '../../../theme/default';
import {useTranslation} from 'react-i18next';
import {
  TouchableOpacity,
  View,
  Text,
  Dimensions,
  Platform,
  StyleSheet,
  StyleProp,
  ViewStyle,
} from 'react-native';

import QrIcon from '../../../assets/icons/qr.svg';
import SendIcon from '../../../assets/icons/send.svg';
import ReceiveIcon from '../../../assets/icons/receive.svg';
import NfcIcon from '../../../assets/icons/nfc.svg';
import BridgeIcon from '../../../assets/icons/bridge.svg';
import DepositIcon from '../../../assets/icons/deposit.svg';
import ScanIcon from '../../../assets/icons/scan.svg';
import PayWithScanIcon from '../../../assets/icons/pay-with-scan.svg';
import {useNdefOrderProvider} from '../../../hooks/useNdefOrderProvider';

const {width} = Dimensions.get('window');

interface WalletButtonProps {
  icon: React.ReactNode;
  label: string;
  onPress: () => void;
  width: number;
  style?: StyleProp<ViewStyle>;
}

const WalletButton = ({
  icon,
  label,
  onPress,
  width,
  style,
}: WalletButtonProps) => {
  return (
    <TouchableOpacity
      style={[styles.ButtonContainer, {width}, style]}
      onPress={onPress}>
      {icon}
      <Text style={styles.ButtonText}>{label}</Text>
    </TouchableOpacity>
  );
};

export function WalletBar({
  navigation,
  renderItemKey = [
    'send',
    'receive',
    'history',
    'bridge',
    'NFC',
    'deposit',
    'payWithScan',
  ],
  wrapperStyle,
}: {
  navigation: any;
  renderItemKey?: string[];
  wrapperStyle?: StyleProp<ViewStyle>;
}) {
  const {t} = useTranslation();
  const {iosReadNdefOnce} = useNdefOrderProvider();

  // const buttonWidth =
  //   Platform.OS === 'android'
  //     ? (width - 32) / renderItemKey.length
  //     : (width - 32) / renderItemKey.length;

  const buttonConfigs = [
    {
      key: 'send',
      icon: <SendIcon width={32} height={32} color={primaryColor} />,
      label: t('send'),
      onPress: () => navigation.navigate('Tokens', {type: 'Send'}),
      show: renderItemKey.includes('send'),
      width: 40,
    },
    {
      key: 'scan',
      icon: <ScanIcon width={32} height={32} color={primaryColor} />,
      label: t('scan'),
      onPress: () => navigation.navigate('Scanner'),
      show: renderItemKey.includes('scan'),
      width: 40,
    },
    {
      key: 'receive',
      icon: <ReceiveIcon width={32} height={32} color={primaryColor} />,
      label: t('receive'),
      onPress: () => navigation.navigate('ReceiveMain', {tokenInfo: undefined}),
      show: renderItemKey.includes('receive'),
      width: 55,
    },
    {
      key: 'NFC',
      icon: <NfcIcon width={32} height={32} color={primaryColor} />,
      label: t('payWithNFCTitle'),
      onPress: () => iosReadNdefOnce(navigation),
      show: Platform.OS === 'ios' && renderItemKey.includes('NFC'),
      width: 40,
    },
    {
      key: 'pay-with-scan',
      icon: <QrIcon width={32} height={32} color={primaryColor} />,
      label: t('payTitle'),
      onPress: () => navigation.navigate('PayWithScan'),
      show: renderItemKey.includes('payWithScan'),
      width: 55,
    },
    {
      key: 'bridge',
      icon: <BridgeIcon width={32} height={32} color={primaryColor} />,
      label: t('Top-up'),
      onPress: () => navigation.navigate('Bridge'),
      show: renderItemKey.includes('bridge'),
      width: 55,
    },
    {
      key: 'deposit',
      icon: <DepositIcon width={32} height={32} color={primaryColor} />,
      label: t('deposit'),
      onPress: () => {},
      show: renderItemKey.includes('deposit'),
      width: 55,
    },
  ];

  return (
    <View
      style={[
        {
          width: '100%',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexDirection: 'row',
          borderRadius: 16,
          marginBottom: 12,
        },
        wrapperStyle,
      ]}>
      {buttonConfigs
        .filter(config => config.show)
        .map((config, index) => (
          <WalletButton
            key={config.key}
            icon={config.icon}
            label={config.label}
            onPress={config.onPress}
            width={config.width || 55}
            style={{
              justifyContent:
                index === 0
                  ? 'flex-start'
                  : index === buttonConfigs.length - 1
                  ? 'flex-end'
                  : 'center',
            }}
          />
        ))}
    </View>
  );
}

const styles = StyleSheet.create({
  ButtonContainer: {
    alignItems: 'center',
    flexDirection: 'column',
    paddingTop: 12,
    paddingBottom: 12,
  },
  ButtonText: {
    fontSize: 14,
    color: '#fff',
    marginTop: 5,
    fontWeight: '400',
  },
});
