import {useNavigation} from '@react-navigation/native';
import {
  primaryColor3,
  secondaryBackgroundColor,
  secondaryColor,
  secondaryHoverBackgroundColor,
} from '../../../theme/default';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {View, Text, Dimensions, TouchableOpacity, Image} from 'react-native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';

const {width} = Dimensions.get('window');

export function AirdropBar() {
  const {t} = useTranslation();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  return (
    <TouchableOpacity
      onPress={() => {
        navigation.navigate('CardList');
      }}
      style={{
        width: width - 32,
        flexDirection: 'row',
        backgroundColor: secondaryBackgroundColor,
        borderWidth: 1,
        borderColor: secondaryHoverBackgroundColor,
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 16,
        paddingTop: 12,
        paddingBottom: 12,
        paddingLeft: 20,
        paddingRight: 20,
        marginBottom: 18,
      }}>
      <View
        style={{
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'flex-start',
        }}>
        <Text style={{color: secondaryColor, fontSize: 12}}>
          {t('airdrop')}
        </Text>
        <Text style={{color: primaryColor3, fontSize: 14}}>
          {t('airdropDesc')}
        </Text>
      </View>
      <View>
        <Image
          source={require('../../../assets/images/add_card.png')}
          height={32}
          width={32}
        />
      </View>
    </TouchableOpacity>
  );
}
