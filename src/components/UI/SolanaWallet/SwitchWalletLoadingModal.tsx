import {ActivityIndicator, Modal, StyleSheet, View} from 'react-native';
import {useSwitchWalletLoadingModalProvider} from '../../../hooks/useSwitchWalletLoadingModalProvider';
import {
  primaryColor,
  primaryHeaderBackgroundColor,
} from '../../../theme/default';
import {useIdentityProvider} from '../../../hooks/useSIWPIdentity';
import {useEffect} from 'react';

export function SwitchWalletLoadingModal() {
  const {isSwitchWalletLoadingModalVisible, closeSwitchWalletLoadingModal} =
    useSwitchWalletLoadingModalProvider();
  const {loginStatus} = useIdentityProvider();

  useEffect(() => {
    if (loginStatus !== 'logging-in') {
      closeSwitchWalletLoadingModal();
    }
  }, [loginStatus, closeSwitchWalletLoadingModal]);

  console.log(
    'isSwitchWalletLoadingModalVisible 2',
    isSwitchWalletLoadingModalVisible,
  );

  return (
    <Modal transparent={true} visible={isSwitchWalletLoadingModalVisible}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <ActivityIndicator size="large" color={primaryColor} />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: primaryHeaderBackgroundColor,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: primaryColor,
    width: 96,
    alignItems: 'center',
    justifyContent: 'center',
    height: 96,
  },
});
