import {
  primaryBackgroundColor,
  primaryColor,
  primaryColor2,
  secondaryBackgroundColor,
  secondaryHoverBackgroundColor,
} from '../../../theme/default';
import {useTranslation} from 'react-i18next';
import {Modal, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import {BlurView} from '@react-native-community/blur';
import {useIdentity} from '../../../hooks/useIdentity';
import CloseIcon from '../../../assets/icons/close.svg';
import {useIdentityProvider} from '../../../hooks/useSIWPIdentity';
import {useSwitchWalletLoadingModalProvider} from '../../../hooks/useSwitchWalletLoadingModalProvider';
import {useSwitchWalletModalProvider} from '../../../hooks/useSwitchWalletModalProvider';
import {AccountList} from './Account/AccountList';
import {UpNetworkLogo} from '../UpNetworkLogo';

export function SwitchWalletModal() {
  const {isSwitchWalletModalVisible, toggleModal} =
    useSwitchWalletModalProvider();
  const {openSwitchWalletLoadingModal} = useSwitchWalletLoadingModalProvider();
  const {setIdentity} = useIdentityProvider();
  const {switchIdentity} = useIdentity();
  const {t} = useTranslation();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isSwitchWalletModalVisible}
      onRequestClose={toggleModal}>
      <View style={styles.modalContainer}>
        <BlurView
          style={styles.absolute}
          blurType="dark" // You can choose "dark", "light", etc.
          blurAmount={5} // Set the intensity of the blur effect
        />
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text
              style={{fontSize: 16, fontWeight: '600', color: primaryColor2}}>
              {t('switchWallet')}
            </Text>
            <TouchableOpacity
              onPress={() => {
                toggleModal();
              }}
              style={{
                position: 'absolute',
                justifyContent: 'center',
                alignItems: 'center',
                right: 24,
              }}>
              <CloseIcon height={22} width={22} color={primaryColor2} />
            </TouchableOpacity>
          </View>
          <View
            style={{
              width: '100%',
              paddingLeft: 20,
              paddingRight: 20,
              paddingTop: 12,
              paddingBottom: 6,
              marginBottom: 128,
            }}>
            <AccountList
              onSelect={async (e, item) => {
                toggleModal();
                setTimeout(async () => {
                  openSwitchWalletLoadingModal();
                  // login(item.userName).catch(error => {
                  //   console.log(error);
                  // });
                  const identityStore = await switchIdentity(item.userName);
                  if (identityStore) {
                    setIdentity(identityStore);
                  }
                }, 200);
              }}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 32,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <UpNetworkLogo color={primaryColor} />
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  header: {
    width: '100%',
    height: 50,
    backgroundColor: secondaryBackgroundColor,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderBottomWidth: 1,
    borderBottomColor: secondaryHoverBackgroundColor,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background for dimming
  },
  absolute: {
    ...StyleSheet.absoluteFillObject, // Fill the entire screen with the BlurView
  },
  modalContent: {
    backgroundColor: primaryBackgroundColor,
    position: 'absolute',
    height: '65%',
    bottom: 0,
    width: '100%',
    borderRadius: 10,
    alignItems: 'center',
    zIndex: 2,
  },
});
