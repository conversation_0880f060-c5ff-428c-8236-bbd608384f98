import {StyleSheet, View, Text, Dimensions} from 'react-native';
import {UserWithCardNumber} from '../../../../db/models/userModel';
import {
  primaryBackgroundColor,
  primaryColor,
  primaryBackgroundColor3,
  primaryColor2,
  primaryBackgroundColor2,
  primaryHeaderBackgroundColor,
  primaryColor3,
} from '../../../../theme/default';
import {useTranslation} from 'react-i18next';
import {formatTimestamp, parseCardNumberMask} from '../../../../utils';

const {width} = Dimensions.get('window');

type AccountCardProp = {
  userInfo: UserWithCardNumber;
};

export default function AccountCard({userInfo}: Readonly<AccountCardProp>) {
  const {t} = useTranslation();
  const avatarText = userInfo.displayName.substring(0, 1);

  const parseSource = () => {
    if (userInfo.cardNumber == null) {
      return t('commonTitle');
    } else {
      return t('cardTitle');
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.avatar}>
        <Text
          style={{
            fontSize: 18,
            fontWeight: '600',
            color: primaryBackgroundColor3,
          }}>
          {avatarText}
        </Text>
      </View>
      <View style={{marginTop: 8, marginBottom: 32}}>
        <Text style={styles.displayName}>{userInfo.displayName}</Text>
      </View>
      <View style={styles.mainContainer}>
        <View style={styles.itemContainer}>
          <View style={styles.itemTitle}>
            <Text style={styles.titleText}>{t('addressTitle')}</Text>
          </View>
          <View style={styles.itemContent}>
            <Text style={styles.contentText}>{userInfo.solanaAddress}</Text>
          </View>
        </View>
        <View style={{...styles.itemContainer, marginTop: 20}}>
          <View style={styles.itemTitle}>
            <Text style={styles.titleText}>{t('sourceTitle')}</Text>
          </View>
          <View style={styles.itemContent}>
            <Text style={styles.contentText}>{parseSource()}</Text>
          </View>
        </View>
        <View style={{...styles.itemContainer, marginTop: 20}}>
          <View style={styles.itemTitle}>
            <Text style={styles.titleText}>{t('sinceTitle')}</Text>
          </View>
          <View style={styles.itemContent}>
            <Text style={styles.contentText}>
              {formatTimestamp(userInfo.createTime * 1000 * 1000, 'yyyy-MM-dd')}
            </Text>
          </View>
        </View>
      </View>
      {userInfo.cardNumber != null && (
        <View style={{...styles.mainContainer, marginTop: 16}}>
          <View style={{...styles.itemContainer}}>
            <View style={styles.itemTitle}>
              <Text style={styles.titleText}>{t('cardTitle')}</Text>
            </View>
            <View style={styles.itemContent}>
              <Text style={styles.contentText}>
                {parseCardNumberMask(userInfo.cardNumber)}
              </Text>
            </View>
          </View>
        </View>
      )}

      <View style={{...styles.mainContainer, marginTop: 16}}>
        <View style={{...styles.itemContainer}}>
          <View style={styles.itemTitle}>
            <Text style={styles.titleText}>{t('passkeyTitle')}</Text>
          </View>
          <View style={styles.itemContent}>
            <Text style={styles.contentText}>{userInfo.passkeyName}</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: primaryBackgroundColor,
  },
  avatar: {
    marginTop: 32,
    borderRadius: '100%',
    width: 48,
    height: 48,
    backgroundColor: primaryColor,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  displayName: {
    color: primaryColor2,
    fontSize: 16,
    fontWeight: '600',
  },
  mainContainer: {
    width: width - 32,
    borderRadius: 12,
    paddingVertical: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'flex-start',
    backgroundColor: primaryHeaderBackgroundColor,
  },
  itemContainer: {
    width: '100%',
    flexDirection: 'row',
  },
  itemTitle: {
    width: 80,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  itemContent: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  titleText: {
    color: primaryColor3,
  },
  contentText: {
    textAlign: 'right',
    alignItems: 'flex-end',
    color: primaryColor2,
  },
});
