import React, {useCallback, useEffect} from 'react';
import {
  ActivityIndicator,
  FlatList,
  GestureResponderEvent,
  View,
} from 'react-native';

import {User, UserModel} from '../../../../db/models/userModel';
import {useAccounts} from '../../../../hooks/useAccounts';
import {useIdentityProvider} from '../../../../hooks/useSIWPIdentity';
import {primaryColor} from '../../../../theme/default';
import {AccountItem} from './AccountItem';

type AccountListProp = {
  type?: string; // SwitchWallet | ManageWallet
  onSelect: (event: GestureResponderEvent, user: User) => void;
};

export function AccountList({onSelect, type}: Readonly<AccountListProp>) {
  const {identityId} = useIdentityProvider();
  const {loading, users, setLoading} = useAccounts(identityId ?? '');

  const checkIsUserAccomplished = useCallback(async () => {
    const userModel = new UserModel();
    const result = await userModel.getAllWithCardNumber();
    return users.length === result.length;
  }, [users]);

  useEffect(() => {
    checkIsUserAccomplished().then(result => {
      if (result) {
        setLoading(false);
      } else {
        setLoading(true);
      }
    });
  }, [checkIsUserAccomplished]);

  const _type = type ?? 'SwitchWallet';

  const renderAccountItem = ({item}: {item: User}) => (
    <AccountItem
      user={item}
      isSelected={identityId == item.userName && _type == 'SwitchWallet'}
      onPress={e => {
        onSelect(e, item);
      }}
    />
  );

  return (
    <>
      {loading ? (
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size="small" color={primaryColor} />
        </View>
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          style={{width: '100%'}}
          data={users}
          renderItem={renderAccountItem}
          keyExtractor={item => item.solanaAddress}
        />
      )}
    </>
  );
}
