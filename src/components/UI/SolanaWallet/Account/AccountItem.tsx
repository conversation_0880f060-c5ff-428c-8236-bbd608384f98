import {
  GestureResponderEvent,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
} from 'react-native';
import {
  primaryBackgroundColor3,
  primaryColor,
  primaryColor2,
  primaryHeaderBackgroundColor,
  secondaryColor,
} from '../../../../theme/default';
import React from 'react';

import {User} from '../../../../db/models/userModel';
import {toShortAddress} from '../../../../utils';
import Clipboard from '@react-native-clipboard/clipboard';

type AccoutItemProp = {
  user: User;
  isSelected: boolean;
  onPress?: (event: GestureResponderEvent) => void;
};

export function AccountItem({
  user,
  isSelected,
  onPress,
}: Readonly<AccoutItemProp>) {
  const avatarText = user.displayName.substring(0, 1);
  return (
    <TouchableOpacity
      onPress={onPress}
      onLongPress={() => {
        Clipboard.setString(user.solanaAddress);
      }}
      style={{
        ...styles.container,
        backgroundColor: primaryHeaderBackgroundColor,
        borderWidth: 1,
        borderColor: isSelected ? primaryColor : primaryHeaderBackgroundColor,
      }}>
      <View style={styles.avatar}>
        <Text
          style={{
            fontSize: 18,
            fontWeight: '600',
            color: primaryBackgroundColor3,
          }}>
          {avatarText}
        </Text>
      </View>
      <View style={styles.contentContainer}>
        <Text style={{fontSize: 16, color: primaryColor2, fontWeight: '600'}}>
          {user.displayName}
        </Text>
        <Text style={{fontSize: 13, color: secondaryColor}}>
          {toShortAddress(user.solanaAddress)}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    paddingLeft: 16,
    paddingTop: 12,
    paddingRight: 16,
    paddingBottom: 12,
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    marginBottom: 12,
  },
  avatar: {
    borderRadius: '100%',
    width: 40,
    height: 40,
    backgroundColor: primaryColor,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
    flexDirection: 'column',
  },
});
