import {
  primarHoverBackgroundColor2,
  primaryBackgroundColor,
  primaryColor,
  primaryColor2,
  primaryHeaderBackgroundColor,
  secondaryBackgroundColor,
  secondaryHoverBackgroundColor,
} from '../../../../theme/default';
import {Modal, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useTranslation} from 'react-i18next';
import tw from 'twrnc';

import {BlurView} from '@react-native-community/blur';
import CloseIcon from '../../../../assets/icons/close.svg';
import {useSwitchTokenModalProvider} from '../../../../hooks/useSwitchTokenModalProvider';
import {TokenLogo} from '../../TokenLogo';
import {UserToken} from '../../../../db/models/userTokenModel';

export function SwitchTokenModal(props: {
  tokenList: UserToken[];
  selectToken: UserToken;
  setSelectToken: (value: UserToken) => void;
  
}) {
  const {isSwitchTokenModalVisible, toggleModal} =
    useSwitchTokenModalProvider();

  const {t} = useTranslation();
  const {tokenList, selectToken, setSelectToken} = props;

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isSwitchTokenModalVisible}
      onRequestClose={toggleModal}>
      <View style={styles.modalContainer}>
        <BlurView
          style={styles.absolute}
          blurType="dark" // You can choose "dark", "light", etc.
          blurAmount={3} // Set the intensity of the blur effect
        />
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text
              style={{fontSize: 16, fontWeight: '600', color: primaryColor2}}>
              {t('switchToken')}
            </Text>
            <TouchableOpacity
              onPress={() => {
                toggleModal();
              }}
              style={{
                position: 'absolute',
                justifyContent: 'center',
                alignItems: 'center',
                right: 24,
              }}>
              <CloseIcon height={22} width={22} color={primaryColor2} />
            </TouchableOpacity>
          </View>
          <View
            style={{
              width: '100%',
              paddingLeft: 20,
              paddingRight: 20,
              paddingTop: 12,
              paddingBottom: 6,
              marginBottom: 128,
            }}>
            {/* toggleModal(); */}
            <View style={tw.style('flex flex-col gap-3')}>
              {tokenList.map((list: UserToken) => {
                return (
                  <TouchableOpacity
                    key={list.symbol}
                    onPress={() => {
                      setSelectToken(list);
                      toggleModal();
                    }}
                    style={tw.style(
                      `flex flex-row items-center gap-2 border ${
                        selectToken?.address.toLocaleLowerCase() ===
                        list.address.toLocaleLowerCase()
                          ? `border-[${primaryColor}]`
                          : `border-[${primarHoverBackgroundColor2}]`
                      }  bg-[${primaryHeaderBackgroundColor}] p-4 rounded-xl`,
                    )}>
                    <TokenLogo
                      tokenName={list.name}
                      logoUri={list!.logo}
                      size={30}
                      borderRadius={30}
                      backgroundColor="blue"
                      textFontSize={12}></TokenLogo>
                    <Text style={tw.style(`text-[${primaryColor2}]`)}>
                      {list.symbol}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 32,
              justifyContent: 'center',
              alignItems: 'center',
            }}></View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  header: {
    width: '100%',
    height: 50,
    backgroundColor: secondaryBackgroundColor,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderBottomWidth: 1,
    borderBottomColor: secondaryHoverBackgroundColor,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  absolute: {
    ...StyleSheet.absoluteFillObject,
  },
  modalContent: {
    backgroundColor: primaryBackgroundColor,
    position: 'absolute',
    height: '65%',
    bottom: 0,
    width: '100%',
    borderRadius: 10,
    alignItems: 'center',
    zIndex: 2,
  },
});
