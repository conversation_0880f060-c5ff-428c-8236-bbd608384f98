import {
  ActivityIndicator,
  DimensionValue,
  FlatList,
  View,
  Text,
  RefreshControl,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {UserToken} from '../../../../db/models/userTokenModel';
import {userSolanaTransactionHistory} from '../../../../hooks/useSolana/useSolanaTransactionHistory';
import {useCallback, useEffect, useState} from 'react';
import {ParsedTransactionWithMeta} from '@solana/web3.js';
import TokenTxHistoryItem, {TransactionType} from './TokenTxHistoryItem';
import tw from 'twrnc';
import {
  primaryColor,
  primaryColor2,
  secondaryBackgroundColor,
  secondaryBorderColor,
} from '../../../../theme/default';
import {getICRC1Transactions} from '../../../../api/backend/explore';
import {useAccountProvider} from '../../../../hooks/useAccountProvider';
import {HistoryTransaction} from '../../../../types/history';
import {formatNumber, parseValue} from '../../../../utils';
import {useTokenPriceProvider} from '../../../../hooks/useTokenPriceProvider';
import {TokenPrice} from '../../../../types/token';

type TokenTxHisoryListProp = {
  tokenInfo?: UserToken;
  height?: DimensionValue | undefined;
};

const limit = 20;

export default function TokenTxHistoryList({
  tokenInfo,
  height,
}: Readonly<TokenTxHisoryListProp>) {
  const {
    getSOLTransferHistory,
    getSPLTokenTransferHistory,
    getSPLTransferInstruction,
    getSOLTransferInstruction,

    loading,
    beforeSignature,
    hasMore,
  } = userSolanaTransactionHistory();
  const [tansferHistoryList, setTransferHistoryList] = useState<
    (ParsedTransactionWithMeta | null)[]
  >([]);
  const [associatedTokenAddress, setAssociatedTokenAddress] =
    useState<string>('');
  const [icrc1HistoryList, setICRC1HistoryList] = useState<
    (HistoryTransaction | null)[]
  >([]);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const {t} = useTranslation();
  const {userInfo} = useAccountProvider();
  const {getTokenPrice, tokenPrices} = useTokenPriceProvider();

  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View
        style={{
          padding: 10,
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
        }}>
        <ActivityIndicator size="small" color={primaryColor} />
        <Text style={{color: primaryColor2}}>Loading...</Text>
      </View>
    );
  };

  const handleLoadMore = useCallback(async () => {
    if (!hasMore) {
      return;
    }
    setLoadingMore(true);
    if (tokenInfo?.type == 'icrc1') {
      const _txHistoryList = await getICRC1Transactions({
        account: tokenInfo!.owner,
        token: tokenInfo!.name.toUpperCase(),
        limit: limit,
      }).then(res => {
        const transactions = (res?.transactions || []) as HistoryTransaction[];
        // transform to ParsedTransactionWithMeta
        const parsedTransactions = transactions?.map(item => {
          return {
            ...item,
            timestamp: item.blockTime,
          };
        });
        return parsedTransactions;
      });
      setICRC1HistoryList(_txHistoryList);
    } else {
      if (tokenInfo?.type == 'native') {
        const nativeTxs = await getSOLTransferHistory(tokenInfo.owner, {
          before: beforeSignature,
          limit: limit,
        });

        console.log('setTransferHistoryList 1 ');
        setTransferHistoryList(tansferHistoryList.concat(nativeTxs));
      } else {
        const {transactions, associatedTokenAddress} =
          await getSPLTokenTransferHistory(
            tokenInfo!.owner,
            tokenInfo!.address,
            {
              before: beforeSignature,
              limit: limit,
            },
          );
        if (associatedTokenAddress) {
          setAssociatedTokenAddress(associatedTokenAddress);
        }
        console.log('setTransferHistoryList 2 ');
        setTransferHistoryList(tansferHistoryList.concat(transactions));
      }
    }

    setLoadingMore(false);
  }, [tansferHistoryList, beforeSignature]);

  const getTxHistory = async () => {
    console.log(2222, tokenInfo);

    if (tokenInfo?.type == 'icrc1') {
      const _txHistoryList = await getICRC1Transactions({
        account: tokenInfo!.owner,
        token: tokenInfo!.name.toUpperCase(),
        limit: limit,
      }).then(res => {
        const transactions = (res?.transactions || []) as HistoryTransaction[];
        // transform to ParsedTransactionWithMeta
        const parsedTransactions = transactions?.map(item => {
          return {
            ...item,
            timestamp: item.blockTime,
          };
        });
        return parsedTransactions;
      });
      setICRC1HistoryList(_txHistoryList);
    } else {
      if (tokenInfo?.type == 'native') {
        const nativeTxs = await getSOLTransferHistory(tokenInfo.owner, {
          limit: limit,
        });
        console.log('setTransferHistoryList 3 ', nativeTxs.length);
        setTransferHistoryList(nativeTxs);
      } else {
        const {transactions, associatedTokenAddress} =
          await getSPLTokenTransferHistory(
            tokenInfo!.owner,
            tokenInfo!.address,
            {
              limit: limit,
            },
          );
        setAssociatedTokenAddress(associatedTokenAddress);
        console.log('setTransferHistoryList 4 ');
        setTransferHistoryList(transactions);
      }
    }
  };

  useEffect(() => {
    getTxHistory();
  }, [tokenInfo]);

  useEffect(() => {
    if (!loading) {
      setRefreshing(false);
    }
  }, [loading]);

  const onRefresh = () => {
    setRefreshing(true);
    getTxHistory();
  };

  // Process Solana transaction data into the format expected by TokenTxHistoryItem
  const processSolanaTransaction = useCallback(
    (item: ParsedTransactionWithMeta, isNative: boolean) => {
      if (!item) {
        return null;
      }

      const signature = item.transaction.signatures[0] || '';

      try {
        const parsedTx = isNative
          ? getSOLTransferInstruction(item)
          : getSPLTransferInstruction(item);

        if (!parsedTx) {
          return null;
        }

        const parsed = (parsedTx as any)?.parsed;
        const info = parsed?.info;

        if (!info) {
          console.log(
            'processSolanaTransaction: No info in parsed transaction',
            {isNative, signature},
          );
          return null;
        }

        let type: TransactionType = 'Fee';
        let fromAddress = '';
        let toAddress = '';
        let amount = '0';

        if (isNative) {
          // SOL transfer
          fromAddress = info.source || '';
          toAddress = info.destination || '';

          // Handle different SOL transfer formats
          const lamports = info.lamports || info.amount || 0;
          amount = formatNumber(
            parseValue(lamports.toString(), tokenInfo?.decimals || 9),
            2,
            6,
          );

          const userAddress = userInfo?.solanaAddress?.toLowerCase() || '';
          if (fromAddress.toLowerCase() === userAddress) {
            type = 'Send';
          } else if (toAddress.toLowerCase() === userAddress) {
            type = 'Receive';
          }
        } else {
          // SPL token transfer
          console.log('Processed SPL transfer', {
            parsed,
            associatedTokenAddress,
          });
          fromAddress = info.source || '';
          toAddress = info.destination || '';
          amount = formatNumber(info.tokenAmount?.uiAmount || 0, 2, 6);

          // Use associatedTokenAddress to determine if this is a Send or Receive
          if (associatedTokenAddress) {
            type = fromAddress === associatedTokenAddress ? 'Send' : 'Receive';
          } else {
            // Fallback to basic check if no associatedTokenAddress is available
            type =
              fromAddress.toLowerCase() ===
              userInfo?.solanaAddress?.toLowerCase()
                ? 'Send'
                : 'Receive';
          }
        }

        return {
          type,
          fromAddress,
          toAddress,
          amount,
          timestamp: item.blockTime ? item.blockTime * 1000 : Date.now(),
          signature,
          isFee: false,
        };
      } catch (error) {
        console.error('Error processing Solana transaction:', error);
        return null;
      }
    },
    [tokenInfo, userInfo, associatedTokenAddress],
  );

  // Process ICRC1 transaction data
  const processICRC1Transaction = useCallback(
    (item: HistoryTransaction) => {
      if (!item) return null;

      try {
        const isReceive =
          item.to.toLowerCase() === tokenInfo?.owner?.toLowerCase();

        // Clean amount string (remove underscores and convert to number)
        const cleanAmount = item.amount
          ? parseFloat(item.amount.replace(/_/g, ''))
          : 0;
        const decimals = tokenInfo?.decimals || 9;

        // Format amount with proper decimal places
        const formattedAmount = formatNumber(
          cleanAmount / Math.pow(10, decimals),
          2,
          6,
        );

        // Use current time as fallback if timestamp is not available
        const timestamp = item.timestamp ? item.timestamp * 1000 : Date.now();

        // Use index as fallback signature if txid is not available
        const signature = item.txid || `icrc1-${item.index}`;

        console.log('Processing ICRC1 transaction:', {
          originalAmount: item.amount,
          cleanAmount,
          formattedAmount,
          isReceive,
          timestamp,
          signature,
        });

        return {
          type: isReceive ? ('Receive' as const) : ('Send' as const),
          fromAddress: item.from,
          toAddress: item.to,
          amount: formattedAmount,
          timestamp,
          signature,
        };
      } catch (error) {
        console.error('Error processing ICRC1 transaction:', error, item);
        return null;
      }
    },
    [tokenInfo],
  );

  const [currentTokenPrice, setCurrentTokenPrice] = useState<TokenPrice | null>(
    null,
  );

  useEffect(() => {
    if (tokenInfo) {
      let currentTokenPrice = tokenPrices.find(
        item => item.symbol.toLowerCase() === tokenInfo.symbol.toLowerCase(),
      );

      if (currentTokenPrice) {
        setCurrentTokenPrice(currentTokenPrice);
      } else {
        getTokenPrice(tokenInfo.symbol).then(price => {
          setCurrentTokenPrice(price);
        });
      }
    } else {
      setCurrentTokenPrice(null);
    }
  }, [tokenInfo]);

  const renderTokenTxHistoryItem = useCallback(
    ({item}: {item: HistoryTransaction | ParsedTransactionWithMeta | null}) => {
      if (!item) {
        console.log('renderTokenTxHistoryItem item is null');
        return null;
      }

      let transactionData;

      if (!tokenInfo) {
        return (
          <View key={item.blockTime}>
            <Text style={{color: primaryColor2}}>tokenInfo is null</Text>
          </View>
        );
      }

      if (tokenInfo.type === 'icrc1') {
        console.log('icrc1 item: ', item);
        transactionData = processICRC1Transaction(item as HistoryTransaction);
        console.log('icrc1 transactionData: ', transactionData);
      } else {
        transactionData = processSolanaTransaction(
          item as ParsedTransactionWithMeta,
          tokenInfo.type === 'native',
        );
      }

      if (!transactionData) return null;

      return (
        <TokenTxHistoryItem
          key={`${transactionData.signature || transactionData.timestamp}`}
          tokenInfo={{
            name: tokenInfo.name || '',
            symbol: tokenInfo.symbol || '',
            logo: tokenInfo.logo,
            decimals: tokenInfo.decimals || 9,
          }}
          transaction={transactionData}
          tokenPrice={currentTokenPrice?.price}
          onPress={() => {
            // Handle transaction press (e.g., open in explorer)
            // const url = `https://explorer.solana.com/tx/${transactionData.signature}`;
            // Linking.openURL(url);
          }}
        />
      );
    },
    [
      tokenInfo,
      processSolanaTransaction,
      processICRC1Transaction,
      currentTokenPrice,
    ],
  );

  return loading && !loadingMore && !refreshing ? (
    <View
      style={{
        minHeight: 180,
        justifyContent: 'center',
        alignItems: 'center',
        maxHeight: height,
        ...tw.style('flex flex-col gap-5'),
        ...tw.style(
          `border border-[${secondaryBorderColor}] mt-[10px] p-4 rounded-xl bg-[${secondaryBackgroundColor}]`,
        ),
      }}>
      <ActivityIndicator
        size={'large'}
        color={primaryColor}></ActivityIndicator>
    </View>
  ) : tansferHistoryList.length == 0 ? (
    <View
      style={{
        minHeight: 180,
        justifyContent: 'center',
        alignItems: 'center',
        maxHeight: height,
        ...tw.style('flex flex-col gap-5'),
        ...tw.style(
          `border border-[${secondaryBorderColor}] mt-[10px] p-4 rounded-xl bg-[${secondaryBackgroundColor}]`,
        ),
      }}>
      <Text style={{fontSize: 16, color: primaryColor2}}>{t('noData')}</Text>
    </View>
  ) : (
    <FlatList
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={<View style={{height: 0}} />}
      contentContainerStyle={{
        paddingTop: 0,
        flexGrow: 1,
      }}
      contentInset={{top: 0}}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[primaryColor2]} // Android: Circle colors
          tintColor={primaryColor2}
        />
      }
      style={{
        maxHeight: height,
        ...tw.style('flex flex-col gap-5'),
        ...tw.style(
          `border border-[${secondaryBorderColor}] mt-[12px] pl-4 pr-4 pb-6 rounded-xl bg-[${secondaryBackgroundColor}]`,
        ),
      }}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      ListFooterComponent={renderFooter}
      data={tokenInfo?.type === 'icrc1' ? icrc1HistoryList : tansferHistoryList}
      keyExtractor={(item, index) => {
        if (!item) return `item-${index}`;
        if (tokenInfo?.type === 'icrc1') {
          const tx = item as HistoryTransaction;
          return tx.txid || `tx-${tx.timestamp}-${index}`;
        } else {
          const tx = item as ParsedTransactionWithMeta;
          return (
            tx.transaction.signatures[0] || `sol-tx-${tx.blockTime}-${index}`
          );
        }
      }}
      renderItem={renderTokenTxHistoryItem}></FlatList>
  );
}
