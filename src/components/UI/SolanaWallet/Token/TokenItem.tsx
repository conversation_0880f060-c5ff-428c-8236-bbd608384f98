import {
  View,
  ViewStyle,
  TouchableOpacity,
  GestureResponderEvent,
  Text,
} from 'react-native';
import {UserToken} from '../../../../db/models/userTokenModel';
import {primaryColor2, secondaryColor} from '../../../../theme/default';
import {TokenLogo} from '../../TokenLogo';
import {useTokenPriceProvider} from '../../../../hooks/useTokenPriceProvider';
import {calTokenValue, formatNumber} from '../../../../utils';
import {useAssetVisible} from '../../../../hooks/useAssetVisible';

type TokenItemProp = {
  userToken: UserToken;
  customStyle?: ViewStyle;
  onPress?: (event: GestureResponderEvent) => void;
};

export function TokenItem({
  userToken,
  customStyle,
  onPress,
}: Readonly<TokenItemProp>) {
  const {tokenPrices} = useTokenPriceProvider();
  const handleTokenValue = () => {
    const tokenPrice = tokenPrices.find(item => {
      return item.symbol.toLowerCase() == userToken.symbol.toLowerCase();
    });
    if (tokenPrice) {
      return calTokenValue(userToken.uiAmount!, tokenPrice.price, 4);
    } else {
      return 'SGD 0.00';
    }
  };
  const [showAsset] = useAssetVisible();

  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        backgroundColor: customStyle?.backgroundColor ?? 'transparent',
        borderRadius: customStyle?.borderRadius ?? 0,
        borderColor: customStyle?.borderBlockColor ?? 'transparent',
        borderWidth: customStyle?.borderWidth ?? 0,
        padding: 16,
        alignItems: 'center',
        flex: 1,
        marginBottom: 16,
      }}>
      <View
        style={{flex: 1, flexDirection: 'row', justifyContent: 'flex-start'}}>
        <View style={{width: 50}}>
          <TokenLogo
            tokenName={userToken.name}
            logoUri={userToken.logo}
            size={42}
            borderRadius={42}
            backgroundColor="blue"
            textFontSize={12}></TokenLogo>
        </View>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View
            style={{
              flex: 1,
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'flex-start',
            }}>
            <Text
              style={{color: primaryColor2, fontSize: 17, fontWeight: '500'}}>
              {userToken.name}
            </Text>
            <Text
              style={{
                color: '#fff',
                fontSize: 15,
                fontWeight: '400',
                opacity: 0.4,
              }}>
              {userToken.symbol}
            </Text>
          </View>
          <View
            style={{
              flex: 1,
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'flex-end',
            }}>
            <Text
              style={{color: primaryColor2, fontSize: 17, fontWeight: '500'}}>
              {showAsset ? handleTokenValue() : '****'}
            </Text>
            <Text style={{color: '#fff', fontSize: 14, fontWeight: '400'}}>
              {showAsset ? formatNumber(userToken.uiAmount!, 2, 6) : '****'}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}
