import {
  View,
  Text,
  GestureResponderEvent,
  TouchableOpacity,
} from 'react-native';
import tw from 'twrnc';
import {primaryColor2, primaryColor3} from '../../../../theme/default';
import {TokenLogo} from '../../TokenLogo';
import ReceiveIcon from '../../../../assets/icons/receive_icon.svg';
import SendIcon from '../../../../assets/icons/send_icon.svg';
import {calTokenValue} from '../../../../utils';

export type TransactionType = 'Send' | 'Receive' | 'Fee';

export type TokenTxHistoryItemProps = {
  tokenInfo: {
    name: string;
    symbol: string;
    logo?: string;
    decimals: number;
  };
  transaction: {
    type: TransactionType;
    fromAddress: string;
    toAddress: string;
    amount: string;
    timestamp: number;
    signature?: string;
  };
  tokenPrice?: string;
  onPress?: (event: GestureResponderEvent) => void;
};

export default function TokenTxHistoryItem({
  tokenInfo,
  transaction,
  tokenPrice,
  onPress,
}: Readonly<TokenTxHistoryItemProps>) {
  const {type, fromAddress, toAddress, amount, timestamp, signature} =
    transaction;
  const isReceive = type === 'Receive';

  // Format address for display
  const formatAddress = (address: string) => {
    if (!address) return '';
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  // Format timestamp to readable date
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  // Handle opening transaction in explorer
  const handleOpenExplorer = () => {
    if (!signature) return;
    // Uncomment and implement when you have the explorer URL pattern
    // const url = `https://explorer.solana.com/tx/${signature}`;
    // Linking.openURL(url);
  };

  // Render token icon with direction indicator
  const renderTokenIcon = () => {
    return (
      <View style={{position: 'relative', marginRight: 12}}>
        {/* Token icon */}
        <View
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: '#262627',
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 1,
            borderColor: '#3C315B',
          }}>
          {tokenInfo.logo ? (
            <TokenLogo
              tokenName={tokenInfo.name}
              logoUri={tokenInfo.logo}
              size={42}
              borderRadius={42}
              backgroundColor="blue"
              textFontSize={12}
            />
          ) : (
            <Text
              style={{color: primaryColor2, fontSize: 14, fontWeight: 'bold'}}>
              {tokenInfo.symbol?.charAt(0) || 'T'}
            </Text>
          )}
        </View>

        {/* Top-right indicator */}
        {type === 'Fee' ? (
          // Fee indicator (red circle with lightning bolt)
          <View
            style={{
              position: 'absolute',
              top: -2,
              right: -2,
              width: 16,
              height: 16,
              borderRadius: 8,
              backgroundColor: '#FF6B6B',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text style={{color: 'white', fontSize: 8, fontWeight: 'bold'}}>
              ⚡
            </Text>
          </View>
        ) : isReceive ? (
          // Receive indicator
          <ReceiveIcon
            style={{
              position: 'absolute',
              top: -2,
              right: -2,
              width: 16,
              height: 16,
              borderRadius: 8,
            }}
          />
        ) : (
          // Send indicator
          <SendIcon
            style={{
              position: 'absolute',
              top: -2,
              right: -2,
              width: 16,
              height: 16,
              borderRadius: 8,
            }}
          />
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      onPress={onPress || handleOpenExplorer}
      style={{paddingVertical: 8, paddingHorizontal: 0}}>
      <View
        style={{
          ...tw.style('flex flex-row items-center'),
          minHeight: 60,
        }}>
        {/* 左侧图标 */}
        {renderTokenIcon()}

        {/* 中间内容 */}
        <View
          style={{
            ...tw.style('flex flex-col gap-1'),
            flex: 1,
            justifyContent: 'center',
          }}>
          <Text
            style={tw.style(`text-[${primaryColor2}] text-[16px] font-medium`)}>
            {type} {tokenInfo.symbol}
          </Text>
          <Text style={tw.style(`text-[${primaryColor3}] text-[12px]`)}>
            {!(type === 'Fee') &&
              (isReceive
                ? `From: ${formatAddress(fromAddress)}`
                : `To: ${formatAddress(toAddress)}`)}
          </Text>
        </View>

        {/* 右侧金额 */}
        <View
          style={{
            ...tw.style('flex flex-col gap-1'),
            alignItems: 'flex-end',
            justifyContent: 'center',
          }}>
          {/* 主要金额 - 大字体 */}
          <Text
            style={{
              fontSize: 16,
              fontWeight: '500',
              textAlign: 'right',
              color: isReceive ? primaryColor2 : primaryColor3,
            }}>
            {isReceive
              ? `+${amount} ${tokenInfo.symbol}`
              : `${amount} ${tokenInfo.symbol}`}
          </Text>
          {/* 次要信息 - 小字体 */}
          {tokenPrice && (
            <Text
              style={{
                fontSize: 12,
                textAlign: 'right',
                color: primaryColor3,
              }}>
              {calTokenValue(amount, tokenPrice)}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
}
