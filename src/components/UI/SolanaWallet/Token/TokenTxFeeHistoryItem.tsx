import { ParsedTransactionWithMeta } from '@solana/web3.js';
import {
  View,
  Text,
  GestureResponderEvent,
  TouchableOpacity,
  Linking,
  Image,
} from 'react-native';
import tw from 'twrnc';

import { primaryColor2, primaryColor3, primaryColor } from '../../../../theme/default';
import { useCallback, useEffect, useState } from 'react';
import {
  formatNumber,
  formatTimestamp,
  parseSolanaScanTxUrl,
  parseValue,
  toShortAddress,
} from '../../../../utils';
import { UserToken } from '../../../../db/models/userTokenModel';
import { useAccountProvider } from '../../../../hooks/useAccountProvider';
import { useTranslation } from 'react-i18next';
import { useSolanaClientProvider } from '../../../../hooks/useSolana/useSolanaClientProvider';
import { TokenLogo } from '../../TokenLogo';

type TokenTxFeeHistoryItemProp = {
  tokenInfo: UserToken;
  txHistory?: ParsedTransactionWithMeta;
  onPress?: (event: GestureResponderEvent) => void;
};

export default function TokenTxFeeHistoryItem({
  tokenInfo,
  txHistory,
  onPress,
}: Readonly<TokenTxFeeHistoryItemProp>) {
  const { solanaClient } = useSolanaClientProvider();
  const [signature, setSignature] = useState<string>();
  const [fee, setFee] = useState<string>();
  const { t } = useTranslation();
  const { userInfo } = useAccountProvider();
  const parseFeeData = async () => {
    const _fee = (txHistory as ParsedTransactionWithMeta).meta?.fee;
    const _signature = (txHistory as ParsedTransactionWithMeta).transaction
      .signatures[0];
    setFee(formatNumber(parseValue(_fee ?? 0, 9), 2, 6));
    setSignature(_signature);
  };

  const handleOpenSolanaScan = useCallback(async () => {
    if (signature !== undefined) {
      const url = parseSolanaScanTxUrl(signature);

      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      }
    }
  }, [signature]);

  useEffect(() => {
    parseFeeData();
  }, [tokenInfo, txHistory, userInfo, solanaClient]);

  // 渲染费用图标 - 使用 token 图标和费用标识
  const renderFeeIcon = () => {
    return (
      <View style={{ position: 'relative', marginRight: 12 }}>
        {/* Token 图标 */}
        <View
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: '#262627',
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 1,
            borderColor: '#3C315B',
          }}>
          {tokenInfo.logo ? (
            <TokenLogo
              tokenName={tokenInfo.name}
              logoUri={tokenInfo.logo}
              size={42}
              borderRadius={42}
              backgroundColor="blue"
              textFontSize={12} />
          ) : (
            <Text style={{ color: primaryColor2, fontSize: 14, fontWeight: 'bold' }}>
              {tokenInfo.symbol?.charAt(0) || 'S'}
            </Text>
          )}
        </View>

        {/* 右上角费用标识 */}
        <View
          style={{
            position: 'absolute',
            top: -2,
            right: -2,
            width: 16,
            height: 16,
            borderRadius: 8,
            backgroundColor: '#FF6B6B',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text style={{ color: 'white', fontSize: 8, fontWeight: 'bold' }}>
            ⚡
          </Text>
        </View>
      </View>
    );
  };

  return (
    <TouchableOpacity
      onPress={handleOpenSolanaScan}
      style={{ paddingVertical: 8, paddingHorizontal: 0 }}>
      <View
        style={{
          ...tw.style('flex flex-row items-center'),
          minHeight: 60,
        }}>
        {/* 左侧图标 */}
        {renderFeeIcon()}

        {/* 中间内容 */}
        <View
          style={{
            ...tw.style('flex flex-col gap-1'),
            flex: 1,
            justifyContent: 'center',
          }}>
          <Text style={tw.style(`text-[${primaryColor2}] text-[16px] font-medium`)}>
            Transaction Fee
          </Text>
          <Text style={tw.style(`text-[${primaryColor3}] text-[14px]`)}>
            {signature ? `${toShortAddress(signature, 8)}` : 'Network fee'}
          </Text>
        </View>

        {/* 右侧金额 */}
        <View
          style={{
            ...tw.style('flex flex-col gap-1'),
            alignItems: 'flex-end',
            justifyContent: 'center',
          }}>
          {/* 主要金额 - 参考图2小字体样式 */}
          <Text
            style={{
              fontSize: 14,
              fontWeight: '500',
              textAlign: 'right',
              color: primaryColor2,
            }}>
            {fee ? `${fee} SOL` : '--'}
          </Text>
          {/* 次要信息 - 更小字体 */}
          <Text
            style={{
              fontSize: 12,
              textAlign: 'right',
              color: primaryColor3,
            }}>
            {fee ? `-${fee} SOL` : '$0.00'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}
