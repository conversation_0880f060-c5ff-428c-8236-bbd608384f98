import Toast, {
  BaseToast,
  ErrorToast,
  ToastShowParams,
} from 'react-native-toast-message';
import {View, Text, StyleSheet} from 'react-native';
import {
  primaryBackgroundColor,
  primaryColor,
  primaryHeaderBackgroundColor,
} from '../../theme/default';

export const toastConfig = {
  success: (props: ToastShowParams) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: 'pink',
        zIndex: 99999,
        elevation: 99999,
      }}
      contentContainerStyle={{paddingHorizontal: 15}}
      text1Style={{
        fontSize: 15,
        fontWeight: '400',
      }}
    />
  ),

  info: (props: ToastShowParams) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: primaryColor,
        zIndex: 99999,
        elevation: 99999,
      }}
      text1Style={{
        fontSize: 14,
        color: primaryBackgroundColor,
      }}
      text2Style={{
        fontSize: 13,
        color: primaryHeaderBackgroundColor,
      }}
    />
  ),

  error: (props: ToastShowParams) => (
    <ErrorToast
      {...props}
      style={{
        zIndex: 99999,
        elevation: 99999,
      }}
      text1Style={{
        fontSize: 14,
        color: primaryBackgroundColor,
      }}
      text2Style={{
        fontSize: 13,
        color: primaryHeaderBackgroundColor,
      }}
    />
  ),
};

const styles = StyleSheet.create({});
