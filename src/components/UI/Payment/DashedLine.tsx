import React from 'react';
import {StyleSheet, View} from 'react-native';

interface DashedLineProps {
  dashWidth?: number;
  dashHeight?: number;
  dashColor?: string;
  dashGap?: number;
  style?: any;
}

const DashedLine: React.FC<DashedLineProps> = ({
  dashWidth = 8,
  dashHeight = StyleSheet.hairlineWidth,
  dashColor = '#fff',
  dashGap = 8,
  style,
}) => {
  // Default line width is 100%, can be overridden by style
  const lineStyle = [
    {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    style,
  ];
  // Calculate the number of dashes based on the width
  const totalWidth =
    style?.width && typeof style.width === 'number' ? style.width : 300;
  const dashCount = Math.floor(totalWidth / (dashWidth + dashGap));
  return (
    <View style={lineStyle}>
      {Array.from({length: dashCount}).map((_, i) => (
        <View
          key={i}
          style={{
            width: dashWidth,
            height: dashHeight,
            backgroundColor: dashColor,
            marginRight: i === dashCount - 1 ? 0 : dashGap,
          }}
        />
      ))}
    </View>
  );
};

export default DashedLine;
