import React, {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useState, useEffect} from 'react';
import {PaymentReceipt} from '../../../types/order';
import Svg, {Defs, RadialGradient, Stop, Rect} from 'react-native-svg';

import CheckIcon from '../../../assets/icons/round_check.svg';
import {formatTimestamp} from '../../../utils';
import KYCLimitModal from '../Modal/KYCLimitModal';
import {useNavigation} from '@react-navigation/native';
import {PaymentCardLayout} from './PaymentCardLayout';

type PaymentReceiptProp = {
  receipt: PaymentReceipt;
};

export default function PaymentReceiptComponent({
  receipt,
}: Readonly<PaymentReceiptProp>) {
  const [modalVisible, setModalVisible] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  const handleGoKYC = () => {
    setModalVisible(false);
  };

  const handleClose = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      {/* KYC Modal */}
      <KYCLimitModal
        visible={modalVisible}
        onClose={handleCloseModal}
        onKYC={handleGoKYC}
        usedAmount={receipt.transaction_total || 0}
        totalLimit={receipt.transaction_limit || 1000}
        currency="VUSD"
      />

      {/* Receipt card */}
      <PaymentCardLayout
        titleContainer={
          <View style={styles.titleContainer}>
            <CheckIcon width={24} height={24} color="#00FF00" />
            <Text style={styles.titleText}>Order paid</Text>
          </View>
        }
        merchantName={receipt.merchantName}
        merchantLogo={
          <View style={styles.merchantLogo}>
            <Text style={styles.merchantLogoText}>🍔</Text>
          </View>
        }
        currencyAmount={String(receipt.orderValue || '--')}
        cryptoAmount={receipt.amount}
        cryptoSymbol={receipt.tokenSymbol}
        orderId={receipt.orderId}
        orderTime={formatTimestamp(receipt.timestamp)}
        buttonText="Close"
        onButtonPress={handleClose}
        backgroundColor="#000000"
        cardBackgroundColor="#333333"
        status="paid"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    backgroundColor: '#1C1C1D',
    width: '100%',
    height: '100%',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '600',
    marginLeft: 8,
  },
  merchantLogo: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: '#555555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  merchantLogoText: {
    fontSize: 24,
  },
});
