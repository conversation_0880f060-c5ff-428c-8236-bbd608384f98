import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {usePayment} from '../../../hooks/usePayment';
import {
  ActivityIndicator,
  Dimensions,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  primaryBackgroundColor,
  primaryColor,
  primaryColor2,
  primaryColor3,
  primaryBackgroundColor2,
} from '../../../theme/default';
import {PaymentInfo, PaymentReceipt, SupportToken} from '../../../types/order';
import {useSolanaOwnerTokenAccountsProvider} from '../../../hooks/useSolana/useSolanaOwnerTokenAccounts';
import {UserToken} from '../../../db/models/userTokenModel';
import {
  calTokenValue,
  formatUnits,
  parseUnits,
  parseValue,
} from '../../../utils';
import {useAccountProvider} from '../../../hooks/useAccountProvider';
import {useSolanaTransaction} from '../../../hooks/useSolana/useSolanaTransaction';
import {LAMPORTS_PER_SOL, Transaction} from '@solana/web3.js';
import {useSolanaClientProvider} from '../../../hooks/useSolana/useSolanaClientProvider';
import LoadingModal from '../Common/LoadingModal';
import {useTranslation} from 'react-i18next';
import {PasskeyAuthFailed} from '../../../types/errors';
import {usePasskeyAuth} from '../../../hooks/usePasskeyAuth';
import {useTokenPriceProvider} from '../../../hooks/useTokenPriceProvider';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import tw from 'twrnc';
import ArrowDown from '../../../assets/icons/arrow_down.svg';
import FaceIcon from '../../../assets/icons/face.svg';
import {TokenLogo} from '../TokenLogo';
import {useSwitchTokenModalProvider} from '../../../hooks/useSwitchTokenModalProvider';
import {SwitchTokenModal} from '../SolanaWallet/Token/SwitchToken';
import Toast from 'react-native-toast-message';
import BigNumber from 'bignumber.js';
import {useIcrc1TokenTransfer} from '../../../hooks/useICP/useIcrc1TokenTransfer';
import {MMKV} from 'react-native-mmkv';
import {useKYC} from '../../../hooks/useKYC';
import {useAuthProvider} from '../../../hooks/useAuthProvider';
import {ComputeBudgetProgram, Connection} from '@solana/web3.js';
import {forceUpdateOrder} from '../../../api/backend/kyc';
import {PaymentCardLayout} from './PaymentCardLayout';

export const storage = new MMKV();
type PaymentcardProp = {
  orderId: string;
  payerWallet: string;
  timestamp: number;
  onReceipt: (receipt: PaymentReceipt) => void;
};

export default function PaymentCardFromPosScan({
  orderId,
  payerWallet,
  timestamp,
  onReceipt,
}: Readonly<PaymentcardProp>) {
  const {
    orderLoading,
    orderInfo,
    getPosScanOrderInfo,
    updateOrderPaymentInfo,
    getPosScanPaymentInfo,
  } = usePayment();
  console.log('orderInfo', orderInfo);
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo>();
  console.log('paymentInfo', paymentInfo);
  const [currentPaymentToken, setCurrentPaymentToken] =
    useState<SupportToken>();
  const [currentPaymentTokenList, setCurrentPaymentTokenList] = useState<
    SupportToken[]
  >([]);
  const [currentUserToken, setCurrentUserToken] = useState<UserToken>();
  const [currentUserTokenList, setCurrentUserTokenList] = useState<UserToken[]>(
    [],
  );
  const {userInfo} = useAccountProvider();
  const [rawTx, setRawTx] = useState<Transaction>();
  const [rawFee, setRawFee] = useState<string>('');
  const [feeValue, setFeeValue] = useState<string>('');
  const {solanaClient} = useSolanaClientProvider();
  const [paying, setPaying] = useState<boolean>(false);
  const {getTokenPrice, tokenPrices} = useTokenPriceProvider();
  const {passkeyAuth} = usePasskeyAuth();
  const [paymentInfoLoading, setPaymentInfoLoading] = useState<boolean>(false);
  const [hasTriedPayment, setHasTriedPayment] = useState(false);
  const {authToken} = useAuthProvider();

  const {t} = useTranslation();
  const {toggleModal} = useSwitchTokenModalProvider();
  const {loading, icrc1TokenTransfer} = useIcrc1TokenTransfer();
  const {userTokens, getTokenAccounts} = useSolanaOwnerTokenAccountsProvider();
  const {createTransferSOLTx, createTransferSPLTx, signTx, sendRawTx} =
    useSolanaTransaction();

  const stringifyWithBigInt = (data: unknown): string => {
    return JSON.stringify(data, (_key, value) =>
      typeof value === 'bigint' ? value.toString() : value,
    );
  };
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const {launchSNSMobileSDK, getToken} = useKYC();

  const startKYC = async () => {
    const token = await getToken();
    if (token) {
      launchSNSMobileSDK(() => {
        console.log('kyc callback: start reload order info');
        if (authToken) {
          getPosScanOrderInfo(authToken, orderId, payerWallet, timestamp);
        }
      });
    }
  };

  useEffect(() => {
    navigation.setOptions({
      headerTitle: () => (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <Text style={{color: primaryColor2, fontSize: 18, fontWeight: '600'}}>
            {t('payTitle')}
          </Text>
          {(orderLoading || paymentInfoLoading) && (
            <ActivityIndicator
              size="small"
              color={primaryColor}
              style={{marginLeft: 6}}></ActivityIndicator>
          )}
        </View>
      ),
    });
  }, [orderLoading, paymentInfoLoading]);

  useEffect(() => {
    getTokenAccounts();
    if (authToken) {
      getPosScanOrderInfo(authToken, orderId, payerWallet, timestamp);
    }
  }, [authToken, orderId, payerWallet, timestamp]);

  useEffect(() => {
    if (currentPaymentToken != undefined) {
      const userToken = userTokens.find(item =>
        currentPaymentToken.isNative
          ? item.symbol.toLowerCase() ===
            currentPaymentToken.symbol.toLowerCase()
          : item.symbol.toLowerCase() ===
            currentPaymentToken.symbol.toLowerCase(),
      );

      setCurrentUserToken(userToken);
    }
  }, [userTokens, currentPaymentToken]);

  const handleSelectToken = (value: UserToken) => {
    const selectPaymentToken = currentPaymentTokenList.find(item => {
      if (item.isNative) {
        return item.symbol.toLowerCase() === value.symbol.toLowerCase();
      } else {
        return (
          item.symbol.toLowerCase() === value.symbol.toLowerCase() &&
          item.address?.toLowerCase() === value.address.toLowerCase()
        );
      }
    });

    if (selectPaymentToken) {
      setCurrentPaymentToken(selectPaymentToken);
    } else {
      Toast.show({
        type: 'error',
        text1: t('paymentTitle'),
        text2: t('invalidToken'),
        position: Platform.OS === 'android' ? 'top' : 'bottom',
      });
    }
  };

  useEffect(() => {
    if (currentPaymentTokenList.length > 0) {
      const tokens = userTokens.filter(item => {
        const pToken = currentPaymentTokenList.find(pItem => {
          if (pItem.isNative) {
            return pItem.symbol.toLowerCase() === item.symbol.toLowerCase();
          } else {
            return pItem.symbol.toLowerCase() === item.symbol.toLowerCase();
          }
        });
        return pToken !== undefined;
      });

      setCurrentUserTokenList(tokens);
    }
  }, [userTokens, currentPaymentTokenList]);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () =>
        currentUserTokenList.length > 0 && currentUserToken ? (
          <TouchableOpacity
            onPress={toggleModal}
            disabled
            style={tw.style(
              `flex flex-row items-center gap-2 bg-[${primaryBackgroundColor2}] p-2 rounded-xl`,
            )}>
            <TokenLogo
              tokenName={currentUserToken.name}
              logoUri={currentUserToken.logo}
              size={20}
              borderRadius={20}
              backgroundColor="blue"
              textFontSize={12}
            />
            <Text style={tw.style(`text-[${primaryColor2}]`)}>
              {currentUserToken.symbol}
            </Text>
            <ArrowDown color="#AAA" width={20} height={20} />
          </TouchableOpacity>
        ) : undefined,
    });
  }, [currentUserTokenList, currentUserToken, navigation]);

  useEffect(() => {
    if (!orderLoading && orderInfo != undefined) {
      setCurrentPaymentTokenList(orderInfo.supportTokenList);
      const vUSDToken = orderInfo.supportTokenList.find(
        token => token.symbol.toLowerCase() === 'vusd',
      );

      setCurrentPaymentToken(vUSDToken || orderInfo.supportTokenList[0]);
    }
  }, [orderLoading, orderInfo]);

  useEffect(() => {
    const getCurrentPaymentInfo = async () => {
      setPaymentInfoLoading(true);
      const info = await getPosScanPaymentInfo(
        orderId,
        currentPaymentToken!.symbol,
        currentPaymentToken?.tokenAddress ?? '',
      );
      console.log('payment info', info);
      if (info == null) {
        Toast.show({
          type: 'error',
          text1: t('paymentTitle'),
          text2: t('getPaymentInfoFailed'),
          position: Platform.OS === 'android' ? 'top' : 'bottom',
        });
      } else {
        setPaymentInfo(info);
      }
      setPaymentInfoLoading(false);
    };

    if (currentPaymentToken != undefined) {
      getCurrentPaymentInfo();
    }

    const interval = setInterval(() => {
      if (currentPaymentToken != undefined) {
        console.log('refresh payment info....2');
        getCurrentPaymentInfo();
      }
    }, 20 * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [currentPaymentToken, orderId, userTokens]);

  useEffect(() => {
    const handleFeeValue = async () => {
      if (rawFee == '') {
        setFeeValue('SGD 0');
        return;
      }

      const currentTokenPrice = tokenPrices.find(
        item =>
          item.symbol.toLowerCase() ===
          currentPaymentToken?.symbol.toLowerCase(),
      );

      if (currentTokenPrice) {
        const feeValue = calTokenValue(
          parseValue(rawFee, 9),
          currentTokenPrice.price,
        );

        setFeeValue(feeValue);
      } else {
        const tokenPrice = await getTokenPrice(
          currentPaymentToken?.symbol ?? 'SOL',
        );

        const feeValue = calTokenValue(parseValue(rawFee, 9), tokenPrice.price);
        setFeeValue(feeValue);
      }
    };
    handleFeeValue();
  }, [rawFee, tokenPrices, currentPaymentToken]);

  // 优化自动触发支付的 useEffect
  useEffect(() => {
    // 防止重复支付
    if (hasTriedPayment) {
      return;
    }
    // 添加参数检查日志
    console.log('Payment conditions:', {
      loading,
      paying,
      hasPaymentInfo: !!paymentInfo?.payTokenAmount,
      hasTokenInfo: !!currentUserToken?.decimals,
      merchantAddress: orderInfo?.merchantSolanaAddress,
      isVUSD: currentPaymentToken?.symbol.toLowerCase() === 'vusd',
    });

    // 基本条件检查
    if (
      !loading &&
      !paying &&
      orderInfo?.merchantSolanaAddress &&
      currentPaymentToken?.symbol.toLowerCase() === 'vusd' &&
      paymentInfo?.payTokenAmount && // 确保有支付金额
      currentUserToken?.decimals && // 确保有代币精度
      currentUserToken?.address // 确保有代币地址
    ) {
      console.log('All conditions met, triggering payment');
      setHasTriedPayment(true); // 标记已尝试支付
      handleTransfer();
    }
  }, [
    loading,
    paying,
    orderInfo,
    currentPaymentToken,
    paymentInfo, // 添加这些依赖
    currentUserToken, // 添加这些依赖
  ]);

  // 保持原有的 handleTransfer 方法不变
  const handleTransfer = async () => {
    if (overLimit) {
      startKYC();
      return;
    }
    if (loading) {
      return;
    }
    setPaying(true);
    try {
      // 1. 先进行二次验证，并等待其完全完成
      if (orderInfo?.secondVerification) {
        try {
          const result = await passkeyAuth();
          console.log('Passkey auth result:', result); // 添加日志

          // 确保验证完全完成后再继续
          await new Promise(resolve => setTimeout(resolve, 500)); // 增加延时
        } catch (error) {
          if (error instanceof PasskeyAuthFailed) {
            Toast.show({
              type: 'error',
              text1: t('paymentTitle'),
              text2: t('authFailed'),
              position: Platform.OS === 'android' ? 'top' : 'bottom',
            });
            setPaying(false); // 确保重置 paying 状态
            return; // 验证失败直接返回
          }
          throw error; // 其他错误继续抛出
        }
      }

      // 2. 验证通过后再进行转账
      if (
        paymentInfo?.payTokenAmount &&
        currentUserToken?.decimals &&
        orderInfo?.merchantSolanaAddress
      ) {
        console.log('create tx....');
        let tx;
        const amountUnits = BigInt(parseInt(paymentInfo?.payTokenAmount));
        const memo = JSON.stringify({
          webpay: {
            orderId: orderId,
          },
        });

        // calculate priority fee
        const priorityFeeInSol = 0.00005;
        const priorityFeeInLamports = priorityFeeInSol * 1e9;
        const microLamportsPerUnit = priorityFeeInLamports * 1000;
        const priorityFee = Math.floor(microLamportsPerUnit / 1000);

        tx = await createTransferSPLTx(
          userInfo!.solanaAddress,
          orderInfo?.merchantSolanaAddress,
          currentUserToken?.address,
          amountUnits,
          currentPaymentToken?.decimal ?? 6,
          memo,
          priorityFee,
        );

        // const connection = new Connection(
        //   process.env.NEXT_PUBLIC_SOLANA_RPC_URL ||
        //     'https://api.mainnet-beta.solana.com',
        // );
        // const blockhash = await connection.getLatestBlockhash();
        // tx.recentBlockhash = blockhash.blockhash;

        const signedTx = await signTx(tx);
        const rs = await sendRawTx(signedTx);
        // send success, force update order status
        console.log('send success', rs);
        if (authToken) {
          console.log('force update order status');
          forceUpdateOrder(authToken, {
            orderId: orderId,
          });
        }

        // const result = await icrc1TokenTransfer(
        //   currentUserToken?.address,
        //   orderInfo?.merchantSolanaAddress,
        //   amountUnits,
        // );

        // console.log('Transfer result:', result); // 添加结果日志

        // if (result && 'Ok' in result) {
        console.log('Transfer successful, updating order info');
        const timestamp = new Date().getTime();
        // const txHashString = result.Ok.toString();
        const txHashString = rs.toString();

        console.log('Updating order payment info');
        const receipt = {
          signature: txHashString,
          toAddress: orderInfo!.merchantSolanaAddress,
          merchantName: orderInfo!.merchantName,
          tokenSymbol: paymentInfo!.payTokenSymbol,
          fees: '0',
          timestamp: timestamp,
          amount: paymentInfo
            ? formatUnits(
                paymentInfo.payTokenAmount,
                currentPaymentToken?.decimal ?? 6,
              )
            : '--',
          orderValue: orderInfo
            ? `${formatUnits(orderInfo.orderValue + '', 2)} ${
                orderInfo.currency
              }`
            : '--',
          orderId: orderId,
          transaction_total: orderInfo!.transaction_total,
          transaction_limit: orderInfo!.transaction_limit,
        };
        console.log('Calling onReceipt with:', receipt);
        onReceipt(receipt);
        // } else {
        //   Toast.show({
        //     type: 'error',
        //     text1: t('send1'),
        //     text2: stringifyWithBigInt(result),
        //     position: Platform.OS === 'android' ? 'top' : 'bottom',
        //   });
        // }
      }
    } catch (error: any) {
      console.warn('Transfer error:', {
        error: error.toString(),
        stack: error.stack,
      });
      Toast.show({
        type: 'error',
        text1: t('send'),
        text2: error.toString(),
        position: Platform.OS === 'android' ? 'top' : 'bottom',
      });
    } finally {
      setPaying(false);
    }
  };

  const overLimit = useMemo(() => {
    return (
      orderInfo?.transaction_limit &&
      orderInfo?.transaction_limit > 0 &&
      (orderInfo.transaction_total || 0) >= orderInfo.transaction_limit
    );
  }, [orderInfo]);

  return (
    <>
      <PaymentCardLayout
        titleContainer={
          <View style={styles.titleContainer}>
            <Text style={styles.titleText}>Confirmation</Text>
          </View>
        }
        merchantLogo={
          <View style={styles.merchantLogo}>
            <Text style={styles.merchantLogoText}>🍔</Text>
          </View>
        }
        merchantName={orderInfo?.merchantName}
        currencyAmount={
          orderInfo
            ? `${formatUnits(orderInfo.orderValue + '', 2)} ${
                orderInfo.currency
              }`
            : '--'
        }
        cryptoAmount={
          paymentInfo
            ? formatUnits(
                paymentInfo.payTokenAmount,
                currentPaymentToken?.decimal ?? 6,
              )
            : '--'
        }
        cryptoSymbol={paymentInfo?.payTokenSymbol || '--'}
        orderId={orderId}
        buttonText={overLimit ? t('verifyIdentity') : t('confirmTitle')}
        onButtonPress={handleTransfer}
        buttonIcon={<FaceIcon width={20} height={20} fill="#000000" />}
        loading={paying || loading}
        backgroundColor={primaryBackgroundColor}
        cardBackgroundColor={'#1C1C1D'}
      />

      <LoadingModal
        borderColor={primaryBackgroundColor}
        visible={paying || loading}
      />

      {currentUserToken ? (
        <SwitchTokenModal
          tokenList={userTokens}
          selectToken={currentUserToken!}
          setSelectToken={value => {
            handleSelectToken(value);
          }}
        />
      ) : null}
    </>
  );
}

const styles = StyleSheet.create({
  merchantLogo: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: '#555555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  merchantLogoText: {
    fontSize: 24,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '600',
    marginLeft: 8,
  },
});
