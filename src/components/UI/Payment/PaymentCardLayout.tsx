import React from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import VectorLogo from '../../../assets/icons/Vector.svg';
import DashedLine from './DashedLine';
import successBg from '../../../assets/images/success-bg.png';

const {width} = Dimensions.get('window');
const {height} = Dimensions.get('screen');

export interface PaymentCardLayoutProps {
  // Title section
  titleContainer?: React.ReactNode;

  // Merchant information
  merchantName?: string;
  merchantLogo?: React.ReactNode;
  placeText?: string;

  // Payment information
  currencyAmount?: string;
  cryptoAmount?: string;
  cryptoSymbol?: string;

  // Order information
  orderId?: string;
  orderTime?: string;

  // Bottom button
  buttonText?: string;
  onButtonPress?: () => void;
  buttonDisabled?: boolean;
  buttonIcon?: React.ReactNode;

  // Loading state
  loading?: boolean;

  // Custom content
  children?: React.ReactNode;

  // Style customization
  containerStyle?: any;
  cardStyle?: any;
  buttonStyle?: any;

  // Whether to show bottom logo
  showBottomLogo?: boolean;

  // Background color customization
  backgroundColor?: string;
  cardBackgroundColor?: string;

  // Status
  status?: 'pending' | 'paid' | 'failed';
}

export const PaymentCardLayout: React.FC<PaymentCardLayoutProps> = ({
  titleContainer,
  merchantName = "Larry's Vegan Chicken",
  merchantLogo,
  placeText = 'Place',
  currencyAmount = '0',
  cryptoAmount = '0',
  cryptoSymbol = 'vUSD',
  orderId = '--',
  orderTime,
  buttonText = 'Confirm',
  onButtonPress,
  buttonDisabled = false,
  buttonIcon,
  loading = false,
  children,
  containerStyle,
  cardStyle,
  buttonStyle,
  showBottomLogo = true,
  backgroundColor = '#000000',
  cardBackgroundColor = '#333333',
  status = 'pending',
}) => {
  // If no order time provided, use current time
  const displayOrderTime =
    orderTime ||
    (() => {
      const now = new Date();
      const timeStr = now.toLocaleTimeString('en-GB', {
        hour12: false,
      });
      const dateStr = now.toLocaleDateString('en-GB');
      return `${timeStr}; ${dateStr}`;
    })();

  return (
    <View style={[styles.container, {backgroundColor}, containerStyle]}>
      {status === 'paid' && (
        <Image
          source={successBg}
          style={{
            ...styles.successBg,
            width: width,
            height: height,
          }}
        />
      )}
      {/* Merchant card */}
      <View style={styles.merchantCardContainer}>
        {/* Title section */}
        {titleContainer && (
          <View style={styles.titleSection}>{titleContainer}</View>
        )}
        <View
          style={[
            styles.merchantCard,
            {backgroundColor: cardBackgroundColor},
            cardStyle,
          ]}>
          {/* Loading indicator in top right corner */}
          {loading && <View style={styles.loadingIndicator}></View>}
          {/* header  */}
          <View style={styles.merchantHeader}>
            <View style={styles.merchantLogoContainer}>
              {merchantLogo || (
                <View style={styles.merchantLogo}>
                  {/* Default merchant logo placeholder */}
                </View>
              )}
            </View>
            <View style={styles.merchantInfo}>
              <Text style={styles.placeText}>{placeText}</Text>
              <Text style={styles.merchantName}>{merchantName}</Text>
            </View>
          </View>

          <View style={styles.ticketDivider}>
            {status === 'pending' && (
              <View style={[styles.leftSemicircle, {backgroundColor}]} />
            )}
            <DashedLine style={{width: width - 90}} />
            {status === 'pending' && (
              <View style={[styles.rightSemicircle, {backgroundColor}]} />
            )}
          </View>

          {/* Payment amount section */}
          <View style={styles.paymentSection}>
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Pay in currency</Text>
              <Text style={styles.currencyAmount}>${currencyAmount}</Text>
            </View>
            <View style={styles.paymentRow}>
              <Text style={styles.cryptoLabel}>In crypto</Text>
              <Text style={styles.cryptoAmount}>
                {cryptoAmount} {cryptoSymbol}
              </Text>
            </View>
          </View>

          <View style={styles.ticketDivider}>
            {status === 'pending' && (
              <View style={[styles.leftSemicircle, {backgroundColor}]} />
            )}

            <DashedLine style={{width: width - 90}} />
            {status === 'pending' && (
              <View style={[styles.rightSemicircle, {backgroundColor}]} />
            )}
          </View>

          {/* Order details */}
          <View style={styles.orderDetails}>
            <View style={styles.orderRow}>
              <Text style={styles.orderLabel}>Order ID</Text>
            </View>
            <View style={styles.orderRow}>
              <Text style={styles.orderValue}>{orderId}</Text>
            </View>
            <View style={styles.orderRow}>
              <Text style={styles.orderLabel}>Order Time</Text>
            </View>
            <View style={styles.orderRow}>
              <Text style={styles.orderTime}>{displayOrderTime}</Text>
            </View>
          </View>

          {/* Custom children content */}
          {children}

          {/* Bottom right logo */}
          {showBottomLogo && (
            <View style={styles.bottomLogo}>
              <VectorLogo width={32} height={32} fill="#FFFFFF" />
            </View>
          )}
        </View>
      </View>

      {/* Confirm button */}
      {buttonText && onButtonPress && (
        <TouchableOpacity
          style={[
            styles.confirmButton,
            buttonDisabled && styles.confirmButtonDisabled,
            buttonStyle,
          ]}
          onPress={onButtonPress}
          disabled={buttonDisabled}>
          <View style={styles.confirmButtonContent}>
            {buttonIcon && (
              <View style={styles.buttonIconContainer}>{buttonIcon}</View>
            )}
            <Text
              style={[
                styles.confirmButtonText,
                buttonDisabled && styles.confirmButtonTextDisabled,
              ]}>
              {buttonText}
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 70,
    position: 'relative',
  },
  successBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 1,
  },
  merchantCardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
    position: 'relative',
  },
  merchantCard: {
    borderRadius: 16,
    padding: 20,
    width: width - 40,
    marginBottom: 40,
    position: 'relative',
  },
  titleSection: {
    marginBottom: 69,
    alignItems: 'center',
  },
  loadingIndicator: {
    position: 'absolute',
    top: 15,
    right: 15,
    zIndex: 10,
  },
  merchantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  merchantLogoContainer: {
    marginRight: 12,
  },
  merchantLogo: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: '#555555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  merchantInfo: {
    flex: 1,
  },
  placeText: {
    color: '#999999',
    fontSize: 14,
    marginBottom: 4,
  },
  merchantName: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  ticketDivider: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 16,
    marginHorizontal: -20, // Extend to card edges
    position: 'relative',
  },
  leftSemicircle: {
    width: 10,
    height: 20,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    position: 'absolute',
    left: 0,
    top: -10,
    zIndex: 2,
  },
  rightSemicircle: {
    width: 10,
    height: 20,
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    position: 'absolute',
    right: 0,
    top: -10,
    zIndex: 2,
  },
  paymentSection: {
    marginBottom: 16,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentLabel: {
    color: '#999999',
    fontSize: 16,
  },
  currencyAmount: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  cryptoLabel: {
    color: '#999999',
    fontSize: 16,
  },
  cryptoAmount: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  orderDetails: {
    marginBottom: 16,
  },
  orderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderLabel: {
    color: '#999999',
    fontSize: 14,
  },
  orderValue: {
    color: '#999999',
    fontSize: 14,
    fontWeight: '500',
  },
  orderTime: {
    color: '#999999',
    fontSize: 14,
    fontWeight: '500',
  },
  confirmButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    paddingVertical: 16,
    paddingHorizontal: 40,
    width: width - 40,
    alignItems: 'center',
    zIndex: 3,
    position: 'relative',
  },
  confirmButtonDisabled: {
    backgroundColor: '#666666',
  },
  confirmButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIconContainer: {
    marginRight: 8,
  },
  confirmButtonText: {
    color: '#000000',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonTextDisabled: {
    color: '#999999',
  },
  bottomLogo: {
    position: 'absolute',
    bottom: 48,
    right: 30,
    zIndex: 10,
  },
});
