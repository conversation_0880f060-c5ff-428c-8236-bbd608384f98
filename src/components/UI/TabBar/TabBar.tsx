import React, { useEffect, useRef } from 'react';
import { View, Pressable, Dimensions, StyleSheet, Text, Animated } from 'react-native';

import {
  primaryHeaderBackgroundColor,
  primaryColor,
  secondaryColor,
} from '../../../theme/default';
import NavigationIcon from './NavigationIcon';

const { width } = Dimensions.get('window');

const TabBar = ({ state, descriptors, navigation }: any) => {
  // 检查当前是否在 OntaPay 页面，如果是则隐藏 TabBar
  const currentRoute = state.routes[state.index];
  const isOntaPayActive = currentRoute.name === 'OntaPay';

  // 动画值：0 表示显示（正常位置），1 表示隐藏（向下移动）
  const translateY = useRef(new Animated.Value(isOntaPayActive ? 1 : 0)).current;

  useEffect(() => {
    if (isOntaPayActive) {
      // 向下退场动画
      Animated.timing(translateY, {
        toValue: 1, // 向下移动到完全隐藏
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // 从底部向上进场动画
      Animated.timing(translateY, {
        toValue: 0, // 回到正常位置
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isOntaPayActive, translateY]);

  return (
    <Animated.View
      style={[
        styles.mainContainer,
        {
          transform: [
            {
              translateY: translateY.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 110], // 0: 正常位置, 110: 向下移动110px（完全隐藏）
              }),
            },
          ],
        },
      ]}>
      {state.routes.map((route: any, index: number) => {
        const { options } = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
              ? options.title
              : route.name;

        const isFocused = state.index === index;
        const isOntaPay = route.name === 'OntaPay';

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        return (
          <View key={index} style={[styles.mainItemContainer]}>
            <Pressable onPress={onPress}>
              {isOntaPay ? (
                <View style={styles.ontaPayContainer}>
                  <View style={styles.ontaPayIconContainer}>
                    <NavigationIcon
                      route={label}
                      tabBarLabel=""
                      isFocused={isFocused}
                      isOntaPay={true}
                    />
                  </View>
                  <Text
                    style={[
                      styles.tabLabel,
                      { color: isFocused ? primaryColor : secondaryColor },
                    ]}>
                    {label}
                  </Text>
                </View>
              ) : (
                <View style={styles.normalTabContainer}>
                  <NavigationIcon
                    route={label}
                    tabBarLabel=""
                    isFocused={isFocused}
                    isOntaPay={false}
                  />
                  <Text
                    style={[
                      styles.tabLabel,
                      { color: isFocused ? primaryColor : secondaryColor },
                    ]}>
                    {label}
                  </Text>
                </View>
              )}
            </Pressable>
          </View>
        );
      })}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 0,
    backgroundColor: primaryHeaderBackgroundColor,
    paddingTop: 8,
    paddingBottom: 8,
    justifyContent: 'space-between',
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: '#393939',
    height: 76
  },
  mainItemContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  normalTabContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 6,
  },
  ontaPayContainer: {
    position: 'relative',
    justifyContent: 'flex-start',
    alignItems: 'center',
    top: -12, // 图标向上突出
  },
  ontaPayIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4
  },
  tabLabel: {
    fontSize: 11,
    textAlign: 'center',
    marginTop: 4,
  },
});

export default TabBar;
