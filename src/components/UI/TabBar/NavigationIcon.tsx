import React from 'react';
import { StyleSheet, View, Image } from 'react-native';

import HomeIconFill from '../../../assets/icons/home_fill.svg';
import HomeIcon from '../../../assets/icons/home.svg';
import CryptoIconFill from '../../../assets/icons/crypto_fill.svg';
import CryptoIcon from '../../../assets/icons/crypto.svg';
import HistoryIconFill from '../../../assets/icons/history_fill.svg';
import HistoryIcon from '../../../assets/icons/history.svg';
import MoreIconFill from '../../../assets/icons/setting_fill.svg';
import MoreIcon from '../../../assets/icons/setting.svg';
import { primaryColor, secondaryColor } from '../../../theme/default';

const PayIcon = require('../../../assets/icons/onta_pay.png');

interface NavigationIconProps {
  route: string;
  tabBarLabel?: string;
  isFocused: boolean;
  isOntaPay?: boolean;
}

const NavigationIcon = ({
  route,
  tabBarLabel,
  isFocused,
  isOntaPay = false,
}: NavigationIconProps) => {
  const renderIcon = (route: string, isFocues: boolean) => {
    let height: number = isOntaPay ? 72 : 32;
    let width: number = isOntaPay ? 72 : 32;

    const iconColor = isOntaPay
      ? undefined
      : isFocues
        ? primaryColor
        : secondaryColor;

    switch (route) {
      case 'Home':
        return isFocues ? (
          <HomeIconFill height={height} width={width} color={iconColor} />
        ) : (
          <HomeIcon height={height} width={width} color={iconColor} />
        );
      case 'Crypto':
        return isFocues ? (
          <CryptoIconFill height={height} width={width} color={iconColor} />
        ) : (
          <CryptoIcon height={height} width={width} color={iconColor} />
        );
      case 'OntaPay':
        return (
          <Image
            source={PayIcon}
            style={{
              width: width,
              height: height,
              resizeMode: 'contain',
            }}
          />
        );
      case 'History':
        return isFocues ? (
          <HistoryIconFill height={height} width={width} color={iconColor} />
        ) : (
          <HistoryIcon height={height} width={width} color={iconColor} />
        );
      case 'More':
        return isFocues ? (
          <MoreIconFill height={height} width={width} color={iconColor} />
        ) : (
          <MoreIcon height={height} width={width} color={iconColor} />
        );
      default:
        break;
    }
  };

  return <View>{renderIcon(route, isFocused)}</View>;
};

const styles = StyleSheet.create({});

export default NavigationIcon;
