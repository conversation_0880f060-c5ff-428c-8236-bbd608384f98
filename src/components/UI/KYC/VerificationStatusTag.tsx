import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {primaryColor3} from '../../../theme/default';

export type VerificationStatus = 0 | 1 | 2 | 3;

interface VerificationStatusTagProps {
  verified?: VerificationStatus;
  showFallback?: boolean;
  fallbackText?: string;
}

const VerificationStatusTag: React.FC<VerificationStatusTagProps> = ({
  verified = 0,
  showFallback,
  fallbackText = '--',
}) => {
  const userVerifiedMap: Record<VerificationStatus, string> = {
    0: 'Not Verified',
    1: 'Verifying',
    2: 'Verified',
    3: 'Failed',
  };

  const verifiedStyleMap: Record<VerificationStatus, any> = {
    0: styles.unverifiedTag, // 未验证：黄色
    1: styles.verifyingTag, // 验证中：灰色
    2: styles.verifiedSuccessTag, // 已验证：绿色
    3: styles.failedTag, // 验证失败：红色
  };

  if (verified === undefined || verified === null) {
    return showFallback ? (
      <Text style={styles.fallbackText}>{fallbackText}</Text>
    ) : null;
  }

  return (
    <View style={[styles.verifiedTag, verifiedStyleMap[verified]]}>
      <Text style={styles.verifiedTagText}>{userVerifiedMap[verified]}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  // 验证状态标签的基本样式
  verifiedTag: {
    paddingVertical: 3,
    paddingHorizontal: 8,
    borderRadius: 6,
    marginTop: 2,
  },
  // 不同验证状态的颜色样式
  unverifiedTag: {
    backgroundColor: '#FF005E', // 红色
  },
  verifyingTag: {
    backgroundColor: '#808080', // 灰色
  },
  verifiedSuccessTag: {
    backgroundColor: '#4CAF50', // 绿色
  },
  failedTag: {
    backgroundColor: '#F44336', // 红色
  },
  verifiedTagText: {
    color: '#FFFFFF',
    fontSize: 8,
    fontWeight: '600',
  },
  fallbackText: {
    color: primaryColor3,
    fontSize: 12,
  },
});

export default VerificationStatusTag;
