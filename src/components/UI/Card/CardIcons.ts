import {ImageSourcePropType} from 'react-native';

export const CardIcons: Record<string, ImageSourcePropType> = {
  visa: require('../../../assets/cards/visa.png'),
  mastercard: require('../../../assets/cards/mastercard.png'),
  'american express': require('../../../assets/cards/amex.png'),
  discover: require('../../../assets/cards/discover.png'),
  jcb: require('../../../assets/cards/jcb.png'),
  'diners club': require('../../../assets/cards/dinersclub.png'),
  maestro: require('../../../assets/cards/maestro.png'),
  mada: require('../../../assets/cards/mada.png'),
};

export type IconKey = keyof typeof CardIcons;

export const getCardIcon = (
  cardType: IconKey,
): ImageSourcePropType | undefined => {
  return CardIcons[cardType.toLowerCase()] || undefined;
};
