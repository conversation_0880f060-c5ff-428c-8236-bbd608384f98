import {
  Frames,
  CardNumber,
  ExpiryDate,
  Cvv,
  SubmitButton,
} from 'frames-react-native';
import {
  primaryBackgroundColor,
  primaryColor2,
  secondaryBorderColor,
  secondaryColor,
  inputBackgroundColor,
  primaryColor3,
} from '../../../theme/default';
import {useEffect, useState} from 'react';
import React from 'react';
import {View, StyleSheet, Text, Platform} from 'react-native';
import Config from 'react-native-config';
import {useTranslation} from 'react-i18next';
import Toast from 'react-native-toast-message';
import {getCheckoutConfiguration} from '../../../api/backend';

type CardCheckProp = {
  onCheck: (isValid: boolean, cardNumber: string, cardTokenized: any) => void;
};

export function CardCheck({onCheck}: Readonly<CardCheckProp>) {
  const [cardNumber, setCardNumber] = useState<string>('');
  const [publicKey, setPublicKey] = useState<string>();

  const {t} = useTranslation();

  useEffect(() => {
    const getCheckoutPublicKey = async () => {
      const publicKey = await getCheckoutConfiguration();
      console.log('publicKey', publicKey);
      setPublicKey(publicKey == null ? undefined : publicKey);
    };
    getCheckoutPublicKey();
  }, []);

  return (
    <View style={styles.container}>
      <Frames
        config={{
          debug: Config.APP_MODE == 'dev',
          publicKey: publicKey ?? '',
        }}
        cardTokenizationFailed={result => {
          console.log('cardTokenizationFailed', result);
          Toast.show({
            type: 'error',
            text1: t('invalidCard'),
            text2: t('invalidCardHint'),
            position: Platform.OS === 'android' ? 'top' : 'bottom',
          });
          onCheck(false, cardNumber, {});
        }}
        cardValidationChanged={result => {
          console.log('cardValidationChanged', result);
        }}
        paymentMethodChanged={result => {
          console.log('paymentMethodChanged', result);
        }}
        frameValidationChanged={result => {
          console.log('frameValidationChanged', result);
        }}
        cardTokenized={result => {
          onCheck(true, cardNumber, result);
        }}>
        <View>
          <Text style={styles.inputTitle}>{t('cardNumber')}</Text>
          <CardNumber
            onEndEditing={e => {
              console.log('end editing', e.nativeEvent.text);
              setCardNumber(e.nativeEvent.text);
            }}
            // onChange={e => {
            //   console.log('text', e.nativeEvent.text);
            // }}
            // onBlur={(e: NativeSyntheticEvent<TextInputFocusEventData>) => {
            //   console.log('eeee', e.nativeEvent);
            //   setCardNumber(e.nativeEvent.text);
            // }}
            style={styles.cardNumber}
            placeholderTextColor={secondaryColor}
            showIcon={false}
          />
        </View>

        <View style={styles.dateAndCode}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputTitle}>{t('empiryDate')}</Text>
            <ExpiryDate
              style={styles.expiryDate}
              placeholderTextColor={secondaryColor}
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputTitle}>{t('cvvCode')}</Text>
            <Cvv style={styles.cvv} placeholderTextColor={secondaryColor} />
          </View>
        </View>

        <SubmitButton
          disabled={publicKey == undefined}
          title="Continue"
          style={styles.button}
          textStyle={styles.buttonText}
          onPress={() => console.log('merchant action')}
        />

        <Text style={styles.desc}>{t('cardDesc')}</Text>
      </Frames>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: primaryBackgroundColor,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 24,
    paddingBottom: 24,
    margin: 16,
    borderColor: secondaryBorderColor,
    borderWidth: 1,
    borderRadius: 12,
  },
  inputTitle: {
    color: primaryColor3,
    marginLeft: 12,
    marginBottom: 3,
    fontSize: 13,
    fontWeight: '600',
  },
  cardNumber: {
    fontSize: 16,
    height: 48,
    color: primaryColor2,
    backgroundColor: inputBackgroundColor,
    borderColor: secondaryBorderColor,
    borderRadius: 12,
    borderWidth: 1,
  },
  dateAndCode: {
    marginTop: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputContainer: {
    width: '48%',
  },
  expiryDate: {
    fontSize: 16,
    height: 48,
    color: primaryColor2,
    backgroundColor: inputBackgroundColor,
    borderColor: secondaryBorderColor,
    borderRadius: 12,
    borderWidth: 1,
  },
  cvv: {
    fontSize: 16,
    height: 48,
    color: primaryColor2,
    backgroundColor: inputBackgroundColor,
    borderColor: secondaryBorderColor,
    borderRadius: 12,
    borderWidth: 1,
  },
  button: {
    height: 44,
    borderRadius: 12,
    marginTop: 20,
    justifyContent: 'center',
    borderColor: secondaryBorderColor,
    borderWidth: 1,
    backgroundColor: secondaryBorderColor,
  },
  buttonText: {
    color: primaryColor2,
    fontSize: 16,
  },
  desc: {
    marginTop: 32,
    color: primaryColor3,
  },
});
