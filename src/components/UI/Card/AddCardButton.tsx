import {
  GestureResponderEvent,
  StyleSheet,
  Text,
  TouchableOpacity,
  DimensionValue,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  mainLineGradientEndColor,
  mainLineGradientMiddleColor,
  mainLineGradientStartColor,
  primaryColor3,
  primaryHeaderBackgroundColor,
  secondaryBackgroundColor,
  secondaryColor,
  thirdButtonTextColor,
} from '../../../theme/default';

import CardFillIcon from '../../../assets/icons/card_fill.svg';
import {useTranslation} from 'react-i18next';

type AddCardButtonProp = {
  type: string; // 'init' | 'common'
  width?: DimensionValue | undefined;
  onPress?: (event: GestureResponderEvent) => void;
};

export function AddCardButton({
  type,
  width,
  onPress,
}: Readonly<AddCardButtonProp>) {
  const {t} = useTranslation();

  return type == 'init' ? (
    <LinearGradient
      colors={[
        mainLineGradientStartColor,
        mainLineGradientMiddleColor,
        mainLineGradientEndColor,
      ]}
      start={{x: 0, y: 0}} // 起始点为左上角
      end={{x: 1, y: 1}} // 结束点为右下角（45度角）
      style={{...styles.gradient, width: width ?? '100%'}}>
      <TouchableOpacity
        onPress={onPress}
        style={{
          flex: 1,
          justifyContent: 'flex-start',
          alignItems: 'center',
          flexDirection: 'row',
        }}>
        <View
          style={{
            width: 48,
            height: 40,
            justifyContent: 'flex-start',
            alignItems: 'center',
            flexDirection: 'column', // 设置为列布局
          }}>
          <CardFillIcon
            width={24}
            height={24}
            color={thirdButtonTextColor}></CardFillIcon>
        </View>
        <View
          style={{
            flex: 1,
            height: 40,
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
          }}>
          <Text
            style={{
              color: thirdButtonTextColor,
              fontWeight: '600',
              fontSize: 16,
            }}>
            {t('addNewCard')}
          </Text>
          <Text style={{color: secondaryBackgroundColor}}>
            {t('addNewCardHint')}
          </Text>
        </View>
      </TouchableOpacity>
    </LinearGradient>
  ) : (
    <View style={{...styles.container, width: width ?? '100%'}}>
      <TouchableOpacity
        onPress={onPress}
        style={{
          flex: 1,
          justifyContent: 'flex-start',
          alignItems: 'center',
          flexDirection: 'row',
        }}>
        <View
          style={{
            width: 48,
            height: 40,
            justifyContent: 'flex-start',
            alignItems: 'center',
            flexDirection: 'column', // 设置为列布局
          }}>
          <CardFillIcon
            width={24}
            height={24}
            color={primaryColor3}></CardFillIcon>
        </View>
        <View
          style={{
            flex: 1,
            height: 40,
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
          }}>
          <Text
            style={{
              color: primaryColor3,
              fontWeight: '600',
              fontSize: 16,
            }}>
            {t('addNewCard')}
          </Text>
          <Text style={{color: secondaryColor}}>{t('addNewCardHint')}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 64,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: primaryHeaderBackgroundColor,
  },
  gradient: {
    height: 64,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
  },
});
