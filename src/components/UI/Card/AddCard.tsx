import {StyleSheet, View} from 'react-native';
import {primaryBackgroundColor, primaryColor} from '../../../theme/default';
import {CardCheck} from '../../UI/Card/CardCheck';
import {UpNetworkLogo} from '../../UI/UpNetworkLogo';
import React from 'react';

type AddCardProp = {
  onCheck: (isValid: boolean, cardNumber: string, cardTokenized: any) => void;
};

export default function AddCard({onCheck}: Readonly<AddCardProp>) {
  return (
    <View style={styles.container}>
      <View style={styles.cardCheckContainer}>
        <CardCheck onCheck={onCheck}></CardCheck>
      </View>

      <View
        style={{
          position: 'absolute',
          bottom: 64,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <UpNetworkLogo color={primaryColor} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: primaryBackgroundColor,
  },
  cardCheckContainer: {},
});
