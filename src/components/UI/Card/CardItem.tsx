import {CardWithAddress} from '../../../db/models/cardModel';
import {
  View,
  TouchableOpacity,
  GestureResponderEvent,
  Text,
  StyleSheet,
  Image,
} from 'react-native';
import {
  primaryHeaderBackgroundColor,
  secondaryColor,
  primaryColor2,
} from '../../../theme/default';
import React, {useState} from 'react';
import {getCardIcon} from './CardIcons';
import {parseCardNumberMask, toShortAddress} from '../../../utils';

import MoveIcon from '../../../assets/icons/move.svg';

type CardItemProp = {
  card: CardWithAddress;
  onPress?: (event: GestureResponderEvent) => void;
};

export function CardItem({card, onPress}: Readonly<CardItemProp>) {
  const [isShowCardNumber, setIsShowCardNumber] = useState<boolean>(true);
  const toggleShowCardNumber = () => {
    setIsShowCardNumber(!isShowCardNumber);
  };

  return (
    <View style={styles.container}>
      <View style={styles.avatar}>
        <Image
          style={{width: '100%', height: '100%'}}
          source={getCardIcon(card.scheme)}></Image>
      </View>
      <View style={styles.contentContainer}>
        <TouchableOpacity
          onPress={toggleShowCardNumber}
          style={styles.cardInfoContainer}>
          <Text
            style={{
              color: secondaryColor,
              fontSize: 13,
              fontWeight: '600',
              marginBottom: 3,
            }}>
            {card.issuer}
          </Text>
          {isShowCardNumber ? (
            <Text
              style={{
                color: primaryColor2,
                fontSize: 16, // Set font size
                lineHeight: 16, // Set line height to match font size for better alignment
                textAlign: 'center',
                fontWeight: '600',
              }}>
              {parseCardNumberMask(card.cardNumber)}
            </Text>
          ) : (
            <Text
              style={{
                color: primaryColor2,
                fontSize: 16, // Set font size
                lineHeight: 16, // Set line height to match font size for better alignment
                textAlign: 'center',
                fontWeight: '600',
              }}>
              {toShortAddress(card.solanaAddress, 6)}
            </Text>
          )}
        </TouchableOpacity>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            onPress={onPress}
            style={{
              width: 24,
              height: 24,
              borderRadius: 24,
              backgroundColor: 'red',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <MoveIcon color="white" width={16} height={16}></MoveIcon>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    paddingLeft: 16,
    paddingTop: 18,
    paddingRight: 16,
    paddingBottom: 18,
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    marginBottom: 12,
    backgroundColor: primaryHeaderBackgroundColor,
  },
  avatar: {
    width: 50,
    height: (50 * 200) / 300,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
    flexDirection: 'row',
  },
  cardInfoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
    flexDirection: 'column',
  },
  buttonContainer: {
    display: 'none',
    width: 40,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
