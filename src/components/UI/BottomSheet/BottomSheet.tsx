import React, {
  useEffect,
  useRef,
  ReactNode,
  Children,
  isValidElement,
} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
  ScrollView,
} from 'react-native';
import {
  primaryHeaderBackgroundColor,
  primaryColor2,
  secondaryBackgroundColor,
} from '../../../theme/default';
import CloseIcon from '../../../assets/icons/close-2.svg';
import {PanGestureHandler, State} from 'react-native-gesture-handler';
import BackArrowIcon from '../../../assets/icons/back.svg';

interface BottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  defaultPosition?: '50%' | '90%';
  children: ReactNode;
  title: string;
  showBackButton?: boolean;
  closeAnimationDuration?: number;
  containsVirtualizedList?: boolean; // New prop to explicitly specify if content contains VirtualizedList
}

const {height: screenHeight} = Dimensions.get('window');

// Function to check if children contain VirtualizedList components
const containsVirtualizedList = (children: ReactNode): boolean => {
  let hasVirtualizedList = false;

  Children.forEach(children, child => {
    if (isValidElement(child)) {
      const componentType = child.type;
      let componentName = '';

      if (typeof componentType === 'function') {
        componentName =
          (componentType as any).displayName || componentType.name || '';
        console.log('Component name:', componentName);
      } else if (typeof componentType === 'string') {
        componentName = componentType;
      }

      // Check for VirtualizedList components
      if (
        componentName.includes('FlatList') ||
        componentName.includes('SectionList') ||
        componentName.includes('VirtualizedList') ||
        componentName === 'FlatList' ||
        componentName === 'SectionList'
      ) {
        hasVirtualizedList = true;
        return; // Early return if found
      }

      // Recursively check nested children
      if (child.props.children && !hasVirtualizedList) {
        hasVirtualizedList = containsVirtualizedList(child.props.children);
      }
    }
  });

  return hasVirtualizedList;
};

const BottomSheet: React.FC<BottomSheetProps> = ({
  isVisible,
  onClose,
  defaultPosition = '50%',
  children,
  title,
  showBackButton = true,
  closeAnimationDuration = 50,
  containsVirtualizedList: explicitVirtualizedList = false,
}) => {
  const slideAnim = useRef(new Animated.Value(0)).current;
  const panAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const currentPositionRef = useRef(0);
  const isClosingRef = useRef(false);

  // Define positions for 90% height
  const POSITIONS = {
    HALF: screenHeight * 0.4, // Top at 50%
    NINETY: 0, // Top at 10%
    CLOSED: screenHeight, // Offscreen bottom
  };

  // Set initial position based on defaultPosition
  const getInitialPosition = () => {
    return defaultPosition === '90%' ? POSITIONS.NINETY : POSITIONS.HALF;
  };

  // Handle close with animation
  const handleClose = () => {
    if (isClosingRef.current) return;
    isClosingRef.current = true;

    const currentPosition = currentPositionRef.current;

    // Close directly from current position
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: closeAnimationDuration,
      useNativeDriver: true,
    }).start(() => {
      // Reset states and call onClose
      slideAnim.setValue(0);
      translateY.setValue(0);
      currentPositionRef.current = 0;
      panAnim.setValue(0);
      isClosingRef.current = false;
      onClose();
    });
  };

  useEffect(() => {
    if (isVisible) {
      // Reset closing state when opening
      isClosingRef.current = false;

      // Set initial position
      const initialPosition = getInitialPosition();
      currentPositionRef.current = initialPosition;
      translateY.setValue(initialPosition);

      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Only reset if not in the middle of closing animation
      if (!isClosingRef.current) {
        slideAnim.setValue(0);
        // Reset position states
        translateY.setValue(0);
        currentPositionRef.current = 0;
        panAnim.setValue(0);
        isClosingRef.current = false;
      }
    }
  }, [isVisible, slideAnim, defaultPosition]);

  const onGestureEvent = Animated.event(
    [{nativeEvent: {translationY: panAnim}}],
    {useNativeDriver: true},
  );

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      const {translationY} = event.nativeEvent;
      const currentPosition = currentPositionRef.current;
      const currentDragPosition = currentPosition + translationY;

      let targetPosition = currentPosition; // Default to staying

      if (currentPosition === POSITIONS.NINETY) {
        // At 90% (0% position)
        if (currentDragPosition > POSITIONS.HALF + 100) {
          // Released well below 50% position - close directly
          handleClose();
          return;
        } else if (translationY > 0) {
          // Dragged down but not enough to close
          // Calculate the midpoint between 50% and 90% positions
          const midpoint = (POSITIONS.HALF + POSITIONS.NINETY) / 2;
          if (currentDragPosition > midpoint) {
            // Released between 50% and 90% - go to 50%
            targetPosition = POSITIONS.HALF;
          } else {
            // Released between 0% and 50% - stay at 90%
            targetPosition = POSITIONS.NINETY;
          }
        }
      } else {
        // At 50% position
        if (translationY < -50) {
          // Dragging up - go to 90%
          targetPosition = POSITIONS.NINETY;
        } else if (translationY > 0) {
          // Dragging down - close directly from current position
          handleClose();
          return;
        }
      }

      // Set the current value to the drag position, then animate to the target.
      translateY.setValue(currentDragPosition);
      currentPositionRef.current = targetPosition;

      // Animate from the current drag position to the target position.
      Animated.timing(translateY, {
        toValue: targetPosition,
        duration: 250,
        useNativeDriver: true,
      }).start();

      panAnim.setValue(0);
    }
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      onRequestClose={handleClose}>
      <View style={styles.overlay}>
        <TouchableOpacity style={styles.backdrop} onPress={handleClose} />
        <Animated.View
          style={[
            styles.bottomSheet,
            {
              transform: [
                {
                  translateY: Animated.add(
                    slideAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [screenHeight, 0], // Use screenHeight for dynamic slide
                    }),
                    Animated.add(translateY, panAnim),
                  ),
                },
              ],
            },
          ]}>
          <PanGestureHandler
            onGestureEvent={onGestureEvent}
            onHandlerStateChange={onHandlerStateChange}>
            <Animated.View style={styles.dragHandle}>
              <View style={styles.dragIndicator} />
            </Animated.View>
          </PanGestureHandler>

          {/* Header - Fixed */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              {showBackButton && (
                <TouchableOpacity
                  onPress={handleClose}
                  style={styles.backButton}>
                  <BackArrowIcon width={17} height={22} color={primaryColor2} />
                </TouchableOpacity>
              )}
              <Text style={styles.title}>{title}</Text>
            </View>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <CloseIcon width={24} height={24} color={primaryColor2} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          {(() => {
            const hasVirtualizedList =
              explicitVirtualizedList || containsVirtualizedList(children);
            console.log(
              'BottomSheet: containsVirtualizedList =',
              hasVirtualizedList,
              'explicit =',
              explicitVirtualizedList,
            );
            return hasVirtualizedList ? (
              <View
                style={[styles.contentContainer, styles.contentContainerStyle]}>
                {children}
              </View>
            ) : (
              <ScrollView
                style={styles.contentContainer}
                contentContainerStyle={styles.contentContainerStyle}
                showsVerticalScrollIndicator={false}
                bounces={false}>
                {children}
              </ScrollView>
            );
          })()}
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingBottom: screenHeight * 0.1,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheet: {
    backgroundColor: '#000',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    height: '90%', // Set height to 90% of the screen
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: primaryColor2,
  },
  backButton: {
    padding: 4,
  },
  closeButton: {
    padding: 4,
  },
  contentContainer: {
    flex: 1,
  },
  contentContainerStyle: {
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  description: {
    fontSize: 16,
    color: '#999',
    marginBottom: 24,
    textAlign: 'center',
  },
  optionsContainer: {
    gap: 16,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: secondaryBackgroundColor,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#333',
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionIconText: {
    fontSize: 24,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: primaryColor2,
    marginBottom: 4,
  },
  optionSubtitle: {
    fontSize: 14,
    color: '#999',
  },
  dragHandle: {
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dragIndicator: {
    width: 36,
    height: 5,
    borderRadius: 24,
    backgroundColor: '#D9D9D9',
    opacity: 0.4,
  },
});

export default BottomSheet;
