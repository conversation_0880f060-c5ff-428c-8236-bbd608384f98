import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {primaryColor2, secondaryBackgroundColor} from '../../../theme/default';

const SendContent = () => {
  return (
    <>
      <Text style={styles.description}>
        Choose how you want to send your tokens
      </Text>

      {/* Send Options */}
      <View style={styles.optionsContainer}>
        <TouchableOpacity style={styles.option}>
          <View style={styles.optionIcon}>
            <Text style={styles.optionIconText}>📱</Text>
          </View>
          <View style={styles.optionContent}>
            <Text style={styles.optionTitle}>Send to Contact</Text>
            <Text style={styles.optionSubtitle}>
              Send to someone in your contacts
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.option}>
          <View style={styles.optionIcon}>
            <Text style={styles.optionIconText}>🔗</Text>
          </View>
          <View style={styles.optionContent}>
            <Text style={styles.optionTitle}>Send to Address</Text>
            <Text style={styles.optionSubtitle}>Send to a wallet address</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.option}>
          <View style={styles.optionIcon}>
            <Text style={styles.optionIconText}>📷</Text>
          </View>
          <View style={styles.optionContent}>
            <Text style={styles.optionTitle}>Scan QR Code</Text>
            <Text style={styles.optionSubtitle}>Scan a QR code to send</Text>
          </View>
        </TouchableOpacity>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  description: {
    fontSize: 16,
    color: '#999',
    marginBottom: 24,
    textAlign: 'center',
  },
  optionsContainer: {
    gap: 16,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: secondaryBackgroundColor,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#333',
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionIconText: {
    fontSize: 24,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: primaryColor2,
    marginBottom: 4,
  },
  optionSubtitle: {
    fontSize: 14,
    color: '#999',
  },
});

export default SendContent;
