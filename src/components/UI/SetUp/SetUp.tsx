import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import LottieView from 'lottie-react-native';
import loadingAnimation from '../../../assets/icons/loading.json';

const {width} = Dimensions.get('window');

const SetUp = ({
  onSetup,
  loading,
}: {
  onSetup?: () => void;
  loading?: boolean;
}) => {
  const {t} = useTranslation();
  return (
    <View style={styles.container}>
      {/* Emoji */}
      <View style={styles.emojiBox}>
        <Text style={styles.emoji}>🔐</Text>
      </View>
      {/* Title */}
      <Text style={styles.title}>{t('setupPasskeyTitle')}</Text>
      {/* Description */}
      <Text style={styles.desc}>{t('setupPasskeyDesc')}</Text>
      {/* Button */}
      <TouchableOpacity style={styles.button} onPress={onSetup}>
        {loading ? (
          <LottieView
            source={loadingAnimation}
            style={styles.loadingIcon}
            autoPlay
            loop
          />
        ) : (
          <Text style={styles.buttonText}>{t('setupPasskeyButton')}</Text>
        )}
      </TouchableOpacity>
      {/* Bottom tip */}
      <Text style={styles.tip}>{t('setupPasskeyTip')}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(16, 16, 17, 1)',
    borderRadius: 32,
    padding: 28,
    alignItems: 'center',
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  emojiBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  emoji: {
    fontSize: 38,
    marginBottom: 12,
    fontWeight: '700',
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 12,
    textAlign: 'center',
  },
  desc: {
    fontSize: 15,
    color: '#fff',
    fontWeight: '400',
    textAlign: 'center',
    marginBottom: 28,
    lineHeight: 20,
  },
  loadingIcon: {
    width: 24,
    height: 24,
  },
  button: {
    width: width - 64,
    height: 58,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginBottom: 18,
    flexDirection: 'row',
  },
  buttonText: {
    color: '#000',
    fontSize: 17,
    fontWeight: '600',
  },
  tip: {
    color: 'rgba(245, 245, 245, 1)',
    fontSize: 13,
    fontWeight: '400',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default SetUp;
