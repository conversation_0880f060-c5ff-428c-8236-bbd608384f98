import {
  ActivityIndicator,
  Dimensions,
  DimensionValue,
  FlatList,
  View,
  Text,
  RefreshControl,
} from 'react-native';
import {UserToken} from '../../../../db/models/userTokenModel';
import {useCallback, useEffect, useState} from 'react';
import tw from 'twrnc';
import {
  primaryColor,
  primaryColor2,
  primaryColor3,
  secondaryBackgroundColor,
  secondaryBorderColor,
} from '../../../../theme/default';
import {useTranslation} from 'react-i18next';
import {useIcrc1TokenTransactions} from '../../../../hooks/useICP/useIcrc1TokenTransactions';
import {useAccount} from '../../../../hooks/useAccount';
import {TransactionWithId} from '../../../../libs/icrc1/index_service';
import Icrc1TokenTxHistoryItem from './Icrc1TokenTxHistoryItem';

type Icrc1TokenTxHisoryListProp = {
  tokenInfo?: UserToken;
  height?: DimensionValue | undefined;
};

const {width} = Dimensions.get('window');
const limit = 10n;

export default function Icrc1TokenTxHistoryList({
  tokenInfo,
  height,
}: Readonly<Icrc1TokenTxHisoryListProp>) {
  const {loading, hasMore, oldestTxId, getIcrc1TokenTransactions} =
    useIcrc1TokenTransactions();
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const {userInfo} = useAccount();
  const {t} = useTranslation();

  const [txHistoryList, setTxHistoryList] = useState<TransactionWithId[]>([]);

  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View
        style={{
          padding: 10,
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
        }}>
        <ActivityIndicator size="small" color={primaryColor} />
        <Text style={{color: primaryColor2}}>Loading...</Text>
      </View>
    );
  };

  const handleLoadMore = useCallback(async () => {
    if (!hasMore) {
      return;
    }
    setLoadingMore(true);
    let currentTxHistoryList: TransactionWithId[] = [];
    currentTxHistoryList = await getIcrc1TokenTransactions(
      userInfo!.principalId,
      tokenInfo!.address,
      oldestTxId,
      limit,
    );

    setTxHistoryList(txHistoryList.concat(currentTxHistoryList));
    setLoadingMore(false);
  }, [txHistoryList, tokenInfo, userInfo]);

  const getTxHistory = async () => {
    let currentTxHistoryList = await getIcrc1TokenTransactions(
      userInfo!.principalId,
      tokenInfo!.address,
      [],
      limit,
    );

    setTxHistoryList(currentTxHistoryList);
  };

  useEffect(() => {
    if (userInfo !== null) {
      getTxHistory();
    }
  }, [tokenInfo, userInfo]);

  useEffect(() => {
    if (!loading) {
      setRefreshing(false);
    }
  }, [loading]);

  const onRefresh = () => {
    setRefreshing(true);
    getTxHistory();
  };

  const renderTokenTxHistoryItem = ({item}: {item: TransactionWithId}) => {
    return (
      <Icrc1TokenTxHistoryItem
        tokenInfo={tokenInfo!}
        tx={item!}
        key={item.id}
        onPress={() => {}}></Icrc1TokenTxHistoryItem>
    );
  };

  return loading && !loadingMore && !refreshing ? (
    <View
      style={{
        minHeight: 180,
        justifyContent: 'center',
        alignItems: 'center',
        maxHeight: height,
        ...tw.style('flex flex-col gap-5'),
        ...tw.style(
          `border border-[${secondaryBorderColor}] mt-[10px] p-4 rounded-xl bg-[${secondaryBackgroundColor}]`,
        ),
      }}>
      <ActivityIndicator
        size={'large'}
        color={primaryColor}></ActivityIndicator>
    </View>
  ) : txHistoryList.length == 0 ? (
    <View
      style={{
        minHeight: 180,
        justifyContent: 'center',
        alignItems: 'center',
        maxHeight: height,
        ...tw.style('flex flex-col gap-5'),
        ...tw.style(
          `border border-[${secondaryBorderColor}] mt-[10px] p-4 rounded-xl bg-[${secondaryBackgroundColor}]`,
        ),
      }}>
      <Text style={{fontSize: 16, color: primaryColor3}}>{t('noData')}</Text>
    </View>
  ) : (
    <FlatList
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={<View style={{height: 0}} />}
      contentContainerStyle={{
        paddingTop: 0,
        flexGrow: 1,
      }}
      contentInset={{top: 0}}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[primaryColor2]} // Android: Circle colors
          tintColor={primaryColor2}
        />
      }
      style={{
        maxHeight: height,
        ...tw.style('flex flex-col gap-5'),
        ...tw.style(
          `border border-[${secondaryBorderColor}] mt-[12px] pl-4 pr-4 pb-6 rounded-xl bg-[${secondaryBackgroundColor}]`,
        ),
      }}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      ListFooterComponent={renderFooter}
      data={txHistoryList}
      renderItem={renderTokenTxHistoryItem}
      keyExtractor={item => item!.id.toString() + ''}></FlatList>
  );
}
