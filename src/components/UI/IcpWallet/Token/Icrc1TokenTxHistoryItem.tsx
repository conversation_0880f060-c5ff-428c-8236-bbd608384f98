import {
  View,
  Text,
  GestureResponderEvent,
  TouchableOpacity,
} from 'react-native';
import tw from 'twrnc';

import {primaryColor2, primaryColor3} from '../../../../theme/default';
import {useEffect, useState} from 'react';
import {
  formatNumber,
  formatTimestamp,
  parseValue,
  toShortAddress,
} from '../../../../utils';
import {UserToken} from '../../../../db/models/userTokenModel';
import {useAccountProvider} from '../../../../hooks/useAccountProvider';
import {useTranslation} from 'react-i18next';
import {
  Transaction,
  TransactionWithId,
} from '../../../../libs/icrc1/index_service';

type Icrc1TokenTxHistoryItemProp = {
  tokenInfo: UserToken;
  tx: TransactionWithId;
  onPress?: (event: GestureResponderEvent) => void;
};

export default function Icrc1TokenTxHistoryItem({
  tokenInfo,
  tx,
  onPress,
}: Readonly<Icrc1TokenTxHistoryItemProp>) {
  const [fromAddress, setFromAddress] = useState<string>();
  const [toAddress, setToAddress] = useState<string>();
  const [amount, setAmount] = useState<string>();
  const [txId, setTxId] = useState<bigint>();
  const [historyType, setHistoryType] = useState<string>();
  const {t} = useTranslation();
  const {userInfo} = useAccountProvider();
  const parseHistoryData = async () => {
    setTxId(tx.id);
    const txInfo = tx.transaction as Transaction;
    if (txInfo.kind == 'transfer') {
      const _fromAddress = txInfo.transfer[0]?.from.owner.toText();
      const _toAddress = txInfo.transfer[0]?.to.owner.toText();
      const _amount = txInfo.transfer[0]?.amount.toString();
      setFromAddress(_fromAddress);
      setToAddress(_toAddress);

      setAmount(
        formatNumber(parseValue(_amount ?? '0', tokenInfo.decimals), 2, 6),
      );
      if (_fromAddress == userInfo?.principalId) {
        setHistoryType('Send');
      } else {
        setHistoryType('Receive');
      }
    } else {
      setHistoryType(txInfo.kind);
    }
  };

  useEffect(() => {
    parseHistoryData();
  }, [tokenInfo, userInfo]);

  return historyType == 'Send' || historyType == 'Receive' ? (
    <TouchableOpacity style={{paddingVertical: 0}}>
      <View
        style={{
          ...tw.style('flex flex-row items-center justify-between'),
          height: 56,
        }}>
        <View
          style={{
            ...tw.style('flex flex-col gap-1'),
            height: '100%',
            justifyContent: 'center',
          }}>
          <Text style={tw.style(`text-[${primaryColor2}] text-[12px]`)}>
            {t(historyType!)}
          </Text>
          <Text style={tw.style(`text-[${primaryColor3}] text-[12px]`)}>
            {undefined == historyType
              ? '--'
              : 'Receive' == historyType
              ? `${t('from')} ${toShortAddress(fromAddress!, 8)}`
              : `${t('to')} ${toShortAddress(toAddress!, 8)}`}
          </Text>
        </View>

        <View
          style={{
            ...tw.style('flex flex-col gap-1'),
            height: '100%',
            justifyContent: 'center',
          }}>
          <Text
            style={tw.style(`text-[${primaryColor2}] text-[14px] text-right`)}>
            {`${
              undefined == historyType
                ? ''
                : 'Receive' == historyType
                ? '+'
                : '-'
            }${amount}`}
          </Text>
          <Text
            style={tw.style(`text-[${primaryColor3}] text-[12px] text-right`)}>
            {formatTimestamp(Number(tx.transaction.timestamp) / 1000000)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  ) : (
    <TouchableOpacity style={{paddingVertical: 0}}>
      <View
        style={{
          ...tw.style('flex flex-row items-center justify-between'),
          height: 56,
        }}>
        <View
          style={{
            ...tw.style('flex flex-col gap-1'),
            height: '100%',
            justifyContent: 'center',
          }}>
          <Text style={tw.style(`text-[${primaryColor2}] text-[12px]`)}>
            {historyType}
          </Text>
          <Text style={tw.style(`text-[${primaryColor3}] text-[12px]`)}>
            ID: {txId?.toString()}
          </Text>
        </View>

        <View
          style={{
            ...tw.style('flex flex-col gap-1'),
            height: '100%',
            justifyContent: 'center',
          }}>
          <Text
            style={tw.style(
              `text-[${primaryColor2}] text-[14px] text-right`,
            )}></Text>
          <Text
            style={tw.style(`text-[${primaryColor3}] text-[12px] text-right`)}>
            {formatTimestamp(Number(tx.transaction.timestamp) / 1000000)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}
