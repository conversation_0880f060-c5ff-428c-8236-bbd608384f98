import { StyleSheet, TouchableOpacity } from 'react-native';
import { secondaryBackgroundColor, secondaryColor } from '../../theme/default';

import BackButtonIcon from '../../assets/icons/back.svg';
import { useNavigation } from '@react-navigation/native';

export function BackButton() {
  const navigation = useNavigation();

  return (
    <TouchableOpacity
      style={styles.button}
      onPress={() => {
        console.log('--------==========go back==========----------');
        navigation.goBack();
      }}>
      <BackButtonIcon
        color={secondaryColor}
        width={20}
        height={20}></BackButtonIcon>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 32,
    height: 32,
    borderRadius: '100%',
    // backgroundColor: secondaryBackgroundColor, // 移除背景色
  },
});
