import React from 'react';
import {
  Dimensions,
  Image,
  Modal,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  StatusBar,
} from 'react-native';
import {primaryBackgroundColor, primaryColor} from '../../../theme/default';
import {useTranslation} from 'react-i18next';

type KYCLimitModalProps = {
  visible: boolean;
  onClose: () => void;
  onKYC: () => void;
  usedAmount: number;
  totalLimit: number;
  currency?: string;
};

const {width, height} = Dimensions.get('window');

const KYCLimitModal = ({
  visible,
  onClose,
  onKYC,
  usedAmount,
  totalLimit,
  currency = 'VUSD',
}: KYCLimitModalProps) => {
  const {t} = useTranslation();

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>

          <View style={styles.contentContainer}>
            <View style={styles.imageContainer}>
              <Image
                source={require('../../../assets/images/kyc.png')}
                style={styles.image}
              />
            </View>

            <Text style={styles.title}>
              Approaching transaction limit, unlock with KYC
            </Text>

            <Text style={styles.description}>
              You have used{' '}
              <Text style={styles.subDescription}>
                {usedAmount}/{totalLimit} {currency}
              </Text>{' '}
              transaction limit
            </Text>

            <TouchableOpacity style={styles.kycButton} onPress={onKYC}>
              <Text style={styles.kycButtonText}>GO KYC</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: primaryBackgroundColor,
    justifyContent: 'center',
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  closeButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 20,
    right: 20,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#888',
    fontWeight: '600',
  },
  imageContainer: {
    width: '100%',
    height: 320,
    marginBottom: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#d4d4d4',
    textAlign: 'center',
  },
  subDescription: {
    fontSize: 16,
    color: '#afa5e7',
    textAlign: 'center',
    marginBottom: 24,
  },
  kycButton: {
    width: '80%',
    maxWidth: 300,
    backgroundColor: '#9683EC',
    paddingVertical: 14,
    borderRadius: 24,
    alignItems: 'center',
    marginTop: 30,
  },
  kycButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default KYCLimitModal;
