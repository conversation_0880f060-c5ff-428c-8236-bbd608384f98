import React, {useState, useEffect} from 'react';
import {Camera, useCameraDevice} from 'react-native-vision-camera';

import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Vibration,
  Animated,
  Image,
  Platform,
} from 'react-native';

import {primaryColor2} from '../theme/default';

import FlashOnIcon from '../assets/icons/flash-on.svg';
import FlashOffIcon from '../assets/icons/flash-off.svg';
import CloseIcon from '../assets/icons/close-2.svg';
import {useTranslation} from 'react-i18next';
import {useNavigation, useRoute, useIsFocused} from '@react-navigation/native';

// Import tab components
import {ScanTab, ShowQRTab, NFCTab} from './QRScanner/tabs';

type QRScannerProp = {
  onScanResult: (value: string | undefined | null) => void;
  hideCloseButton?: boolean;
  onClose?: () => void;
};

export default function QRScanner({
  onScanResult,
  hideCloseButton = false,
  onClose,
}: Readonly<QRScannerProp>) {
  const [hasPermission, setHasPermission] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [scannerLinePosition] = useState(new Animated.Value(128));
  const [torch, setTorch] = useState<'off' | 'on'>('off');
  const [activeTab, setActiveTab] = useState<'scan' | 'show' | 'nfc'>('scan');
  const [isProcessingScan, setIsProcessingScan] = useState(false);
  const device = useCameraDevice('back');
  const {t} = useTranslation();
  const navigation = useNavigation();
  const route = useRoute();
  const isFocused = useIsFocused();

  const toggleFlashlight = async () => {
    setTorch(torch == 'off' ? 'on' : 'off');
  };

  const handleScanResult = (value: string | undefined | null) => {
    // Prevent duplicate processing
    if (isProcessingScan) {
      console.log('Already processing scan result, ignoring');
      return;
    }

    // Prevent empty or invalid values
    if (!value || value.trim() === '') {
      console.log('Invalid scan result, ignoring');
      return;
    }

    console.log(`onCodeScanned value`, value);

    try {
      setIsProcessingScan(true);
      Vibration.vibrate(10);
      onScanResult(value);
    } catch (error) {
      console.error('Error in handleScanResult:', error);
    } finally {
      // Reset processing state after 1 second
      setTimeout(() => {
        setIsProcessingScan(false);
      }, 1000);
    }
  };

  useEffect(() => {
    const startScannerAnimation = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(scannerLinePosition, {
            toValue: 250,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(scannerLinePosition, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ]),
      ).start();
    };

    setRefresh(!refresh);
    if (device != null && hasPermission && activeTab === 'scan') {
      startScannerAnimation();
    }
  }, [device, hasPermission, activeTab]);

  useEffect(() => {
    const requestCameraPermission = async () => {
      const permission = await Camera.requestCameraPermission();
      console.log('Camera.requestCameraPermission ', permission);
      setHasPermission(permission === 'granted');
    };

    requestCameraPermission();
  }, []);

  // Only show camera error when in scan tab and camera is not available
  const shouldShowCameraError =
    activeTab === 'scan' && (device == null || !hasPermission);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'scan':
        // Show error message if camera is not available
        if (shouldShowCameraError) {
          return (
            <TouchableOpacity
              style={styles.cameraErrorContainer}
              onPress={() => {
                // todo 993
                // Vibration.vibrate(10);
                // onScanResult(
                // 'ij2mx-2t773-6yhsi-tgjgs-ton7r-l4zi4-mbkra-ivo6e-5ymgm-omnv3-rqe',
                // '8BQxz4Xu7bAD9i31ZH1tBQqZ4hPo7r6y4CYhg7rNjc2x',
                //   'https://pay.onta.network/webpay/order_id/1389679612921249792',
                // );
              }}>
              <Text style={styles.cameraErrorText}>{t('noCameraHint')}</Text>
            </TouchableOpacity>
          );
        }
        return (
          <ScanTab
            torch={torch}
            onScanResult={handleScanResult}
            scannerLinePosition={scannerLinePosition}
          />
        );
      case 'show':
        return <ShowQRTab isActive={isFocused && activeTab === 'show'} />;
      case 'nfc':
        return <NFCTab />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.mainContainer}>
      {/* Header with logo and close button */}
      <View style={styles.newHeader}>
        <Image
          source={require('../assets/images/OntapayLogo.png')}
          style={styles.logo}
        />
        {!hideCloseButton && (
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => {
              try {
                // Use custom close handler if provided
                if (onClose) {
                  onClose();
                } else {
                  // Otherwise use default behavior: check if can go back
                  if (navigation.canGoBack()) {
                    navigation.goBack();
                  } else {
                    // If can't go back, accessed via Tab, do nothing
                    console.log(
                      'Scanner accessed via Tab navigation, close button disabled',
                    );
                  }
                }
              } catch (error) {
                console.error('Error handling close button:', error);
              }
            }}>
            <CloseIcon width={30} height={30} />
          </TouchableOpacity>
        )}
      </View>

      {/* Tab buttons */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'scan' && styles.activeTab]}
          onPress={() => setActiveTab('scan')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'scan' && styles.activeTabText,
            ]}>
            Scan QR
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'show' && styles.activeTab]}
          onPress={() => setActiveTab('show')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'show' && styles.activeTabText,
            ]}>
            Show QR
          </Text>
        </TouchableOpacity>
        {Platform.OS === 'ios' && (
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'nfc' && styles.activeTab]}
            onPress={() => setActiveTab('nfc')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'nfc' && styles.activeTabText,
              ]}>
              NFC Pay
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Tab content */}
      {renderTabContent()}

      {/* Bottom text and flash button for scan tab */}
      {activeTab === 'scan' && !shouldShowCameraError && (
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.flashButton}
            onPress={() => {
              toggleFlashlight();
            }}>
            {torch == 'off' ? (
              <FlashOnIcon
                width={32}
                height={32}
                color={'rgba(255, 255, 255, .8)'}></FlashOnIcon>
            ) : (
              <FlashOffIcon
                width={32}
                height={32}
                color={'rgba(255, 255, 255, .8)'}></FlashOffIcon>
            )}
          </TouchableOpacity>
          <Text style={styles.scanHintText}>Scan OntaPay QR Code</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  noDeciveOrPermission: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'black',
  },
  mainContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  // Header styles
  newHeader: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    height: 60,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    zIndex: 1000,
  },
  logo: {
    width: 120,
    height: 30,
    resizeMode: 'contain',
  },
  closeButton: {
    position: 'absolute',
    right: 24,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Tab styles
  tabContainer: {
    position: 'absolute',
    top: 140,
    left: 24,
    right: 24,
    height: 44,
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 22,
    zIndex: 1000,
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
  },
  activeTab: {
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  tabText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 15,
    fontWeight: '500',
  },
  activeTabText: {
    color: 'white',
    fontWeight: '600',
  },
  // Camera error styles
  cameraErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  cameraErrorText: {
    color: primaryColor2,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    paddingHorizontal: 24,
  },
  // Footer styles
  footer: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1000,
  },
  flashButton: {
    width: 48,
    height: 48,
    marginBottom: 16,
  },
  scanHintText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontWeight: '500',
  },
  // Legacy styles (keeping for compatibility)
  header: {
    position: 'absolute',
    top: 64,
    left: 0,
    right: 0,
    paddingLeft: 24,
    height: 50,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
});
