import React from 'react';
import {RouteProp} from '@react-navigation/native';
import {RootStackParamList} from '../../Nav/routes';
import PaymentReceiptComponent from '../../UI/Payment/PaymentReceipt';

type PaymentScreenRouteProp = RouteProp<RootStackParamList, 'PaymentReceipt'>;

type Props = {
  route: PaymentScreenRouteProp;
};

export default function PaymentReceiptView({route}: Readonly<Props>) {
  const {paymentReceipt} = route.params;
  return <PaymentReceiptComponent receipt={paymentReceipt} />;
}
