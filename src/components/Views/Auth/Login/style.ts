import {LogBox, StyleSheet, Dimensions} from 'react-native';
import {primaryColor} from '../../../../theme/default';
const {width} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 16, 17, 1)',
  },
  main: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
    width: '100%',
  },
  bg: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    left: 0,
    top: 0,
  },
  LogoBox: {},
  bottomSection: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingBottom: 32,
  },
  payWithScanDesc: {
    fontSize: 32,
    fontWeight: '600',
    lineHeight: 45,
    letterSpacing: -0.43,
    textAlign: 'center',
    textAlignVertical: 'bottom',
    color: '#fff',
    paddingHorizontal: 24,
  },
  button: {
    width: width - 64,
    height: 58,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  buttonText: {
    color: '#000',
    fontSize: 17,
    fontWeight: '600',
  },
  button2: {
    width: width - 64,
    marginBottom: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button2Text: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '600',
  },
});

export default styles;
