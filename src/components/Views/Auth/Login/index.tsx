import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Image,
  ActivityIndicator,
  Animated,
} from 'react-native';
import {
  primaryBackgroundColor,
  primaryColor,
  primaryColor2,
  primaryBackgroundColor3,
  secondaryColor,
  secondaryBorderColor,
} from '../../../../theme/default';
import React, {useEffect, useState} from 'react';

import {NativeStackNavigationProp} from '@react-navigation/native-stack';

import OntalIcon from '../../../../assets/icons/onta-logo.svg';
import {IdentityModel} from '../../../../db/models/identityModel';
import {useRegister} from '../../../../hooks/useRegister';
import {useIdentityProvider} from '../../../../hooks/useSIWPIdentity';
import {generatePasskeyTitle, getStatusBarHeight} from '../../../../utils';
import type {RootStackParamList} from '../../../Nav/routes';
import {useTranslation} from 'react-i18next';
import {UserModel} from '../../../../db/models/userModel';
import Toast from '../../../../components/UI/Toast';
import LoadingModal from '../../../UI/Common/LoadingModal';
import SimpleModal2 from '../../../UI/Common/SimpleModal2';
import {useAuthProvider} from '../../../../hooks/useAuthProvider.tsx';
import {useAccounts} from '../../../../hooks/useAccounts.ts';
import loginBg from '../../../../assets/images/login-bg.png';
import styles from './style';
import SetUp from '../../../UI/SetUp/SetUp';

type LoginScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Login'
>;

type Props = {
  navigation: LoginScreenNavigationProp;
};

const {width} = Dimensions.get('window');

function LoginView({navigation}: Readonly<Props>) {
  const {t} = useTranslation();
  const {loginStatus, login, loginError} = useIdentityProvider();
  const {registerFast, error} = useRegister();
  const [loading, setLoading] = useState<boolean>();
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const {setShouldInitBindInviteCode} = useAuthProvider();
  const {identityId} = useIdentityProvider();
  const {users} = useAccounts(identityId ?? '');
  const [showSetup, setShowSetup] = useState(false);
  const slideAnim = React.useRef(new Animated.Value(0)).current;
  const bgMoveAnim = React.useRef(new Animated.Value(0)).current;
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  const showSetupWithAnimation = () => {
    setShowSetup(true);
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bgMoveAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideSetupWithAnimation = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bgMoveAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowSetup(false);
    });
  };

  useEffect(() => {
    if (loginError != undefined) {
      setToastMessage(loginError.message);
      setToastVisible(true);
      setTimeout(() => setToastVisible(false), 3000);
    }
  }, [loginError]);

  useEffect(() => {
    if (error != '') {
      setToastMessage(error);
      setToastVisible(true);
      setTimeout(() => setToastVisible(false), 3000);
    }
  }, [error]);

  const handleSignUpAnonymously = async () => {
    try {
      setLoading(true);
      const response = await registerFast({
        passkeyTitle: generatePasskeyTitle(),
      });

      if (response == undefined) {
        setLoading(false);
        return;
      }

      await login(response);
      setShouldInitBindInviteCode(true);
      return;
    } catch (error) {}
  };

  useEffect(() => {
    if (loginStatus != 'logging-in') {
      setLoading(false);
    }
  }, [loginStatus]);

  const handleLogin = async () => {
    try {
      if (loginStatus == 'logging-in') {
        return;
      }

      const identiyModel = new IdentityModel();
      const identity = await identiyModel.getSelected();
      // if (identity != null) {
      //   setLoading(true);
      //   await login(identity.uid);
      // } else {
      const userModel = new UserModel();
      const userList = await userModel.getAll();

      if (userList.length == 0) {
        setIsModalVisible(true);
      } else {
        setLoading(true);
        await login();
        setShouldInitBindInviteCode(true);
        console.log('users_1', users);
        const rs = await userModel.getAllWithCardNumber();
        console.log('users_2', rs);
      }
      // }
    } catch (error) {
      console.log(error);
    }
  };

  const handleLoginDirectly = async () => {
    try {
      setLoading(true);
      await login();
      setShouldInitBindInviteCode(true);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <View style={{...styles.container}}>
      {/* main */}
      <View style={styles.main}>
        <Toast
          visible={toastVisible}
          message={toastMessage}
          onClose={() => setToastVisible(false)}
        />
        {/* bg */}
        <Animated.Image
          source={loginBg}
          style={[
            styles.bg,
            {
              transform: [
                {
                  translateY: bgMoveAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -380],
                  }),
                },
              ],
              borderBottomLeftRadius: bgMoveAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 50],
              }),
              borderBottomRightRadius: bgMoveAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 50],
              }),
            },
          ]}
        />
        {/* logo */}
        <View style={{...styles.LogoBox, marginTop: getStatusBarHeight() + 45}}>
          <OntalIcon width={120} />
        </View>

        {/* bottom */}
        <View style={styles.bottomSection}>
          {/* register with card */}
          {/* <View
          style={{
            marginBottom: 24,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text
            style={{fontSize: 15, fontWeight: '600', color: secondaryColor}}>
            {t('registerWith')}
          </Text>
        </View>

        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 32,
          }}>
          <TouchableOpacity
            onPress={() => {
              navigation.push('Register');
            }}
            style={{
              backgroundColor: primaryBackgroundColor3,
              padding: 8,
              borderRadius: 64,
            }}>
            <CardIcon width={30} height={30} color={primaryColor} />
          </TouchableOpacity>
        </View> */}

          <View style={{marginBottom: 24}}>
            <Text style={styles.payWithScanDesc}>{t('payWithScanDesc')}</Text>
          </View>

          <TouchableOpacity
            onPress={showSetupWithAnimation}
            style={{...styles.button, marginBottom: 26}}>
            <Text style={styles.buttonText}>{t('creatWallet')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={async () => {
              handleLoginDirectly();
            }}
            style={styles.button2}>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
              }}>
              {/* {loginStatus == 'logging-in' && (
            <ActivityIndicator
              size="small"
              color={primaryColor}
              style={{marginRight: 6}}
            />
          )} */}
              <Text style={styles.button2Text}>{t('iHaveOne')}</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              navigation.navigate('Terms');
            }}
            style={{
              width: width - 64,
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            <Text style={{color: primaryColor2}}>{t('termsHint')}</Text>
            <Text style={{color: primaryColor}}>{t('terms')}</Text>
            <Text style={{color: primaryColor2}}>{t('ofUse')}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Setup Passkey Card */}
      {showSetup && (
        <>
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}
            activeOpacity={1}
            onPress={hideSetupWithAnimation}
          />
          <Animated.View
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              width: '100%',
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [300, 0],
                  }),
                },
              ],
            }}>
            <SetUp
              loading={loading}
              onSetup={() => {
                // hideSetupWithAnimation();
                handleSignUpAnonymously();
              }}
            />
          </Animated.View>
        </>
      )}

      {/* modal */}
      <LoadingModal
        visible={loading == undefined ? false : loading}></LoadingModal>
      {isModalVisible && (
        <SimpleModal2
          leftButtonTitle={t('signUpWithPasskey')}
          rightButtonTitle={t('loginTitle')}
          visible={isModalVisible}
          onClose={() => {
            setIsModalVisible(!isModalVisible);
          }}
          title={t('loginButtonTitle')}
          content={t('loginHint')}
          onLeftConfirm={() => {
            setIsModalVisible(false);
            setTimeout(() => {
              handleSignUpAnonymously();
            }, 500);
          }}
          onRightConfirm={() => {
            setIsModalVisible(false);
            setTimeout(async () => {
              setLoading(true);
              await login();
              setShouldInitBindInviteCode(true);
            }, 500);
          }}></SimpleModal2>
      )}
    </View>
  );
}

export default LoginView;
