import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../../Nav/routes';
import AddCard from '../../../UI/Card/AddCard';

type AddCardNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Register'
>;

type Props = {
  navigation: AddCardNavigationProp;
};

export function RegisterView({navigation}: Readonly<Props>) {
  return (
    <AddCard
      onCheck={(isValid, cardNumber, cardTokenized) => {
        console.log(isValid, cardNumber, cardTokenized);
        if (isValid) {
          navigation.push('FinishRegister', {
            cardNumber: cardNumber.replaceAll(' ', ''),
            cardTokenized: cardTokenized ?? {},
          });
        }
      }}
    />
  );
}
