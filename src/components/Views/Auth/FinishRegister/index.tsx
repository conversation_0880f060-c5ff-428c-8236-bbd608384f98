import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../../Nav/routes';
import {useRoute, RouteProp} from '@react-navigation/native';
import React from 'react';
import CreateCardWallet from '../../../UI/Card/CreateCardWallet';

type CreateCardWalletNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'FinishRegister'
>;

type Props = {
  navigation: CreateCardWalletNavigationProp;
};

export default function FinishRegisterView({navigation}: Readonly<Props>) {
  const route = useRoute<RouteProp<RootStackParamList, 'FinishRegister'>>();
  const {cardNumber, cardTokenized} = route.params;

  return (
    <CreateCardWallet cardNumber={cardNumber} cardTokenized={cardTokenized} />
  );
}
