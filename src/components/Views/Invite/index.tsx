import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../Nav/routes';
import {
  Dimensions,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Share,
  Platform,
  ScrollView,
} from 'react-native';
import { primaryBackgroundColor, primaryColor } from '../../../theme/default';
import { useNavigation } from '@react-navigation/native';
import QRCode from 'react-qr-code';
import TwitterIcon from '../../../assets/icons/twitter.svg';
import TelegramIcon from '../../../assets/icons/telegram.svg';
import WhatsAppIcon from '../../../assets/icons/whatsapp.svg';
import WeChatIcon from '../../../assets/icons/wechat.svg';
import CopyIcon from '../../../assets/icons/icon_copy.svg';
import { useTranslation } from 'react-i18next';
import { useAuthProvider } from '../../../hooks/useAuthProvider';
import { useEffect, useState } from 'react';
import { getNFT } from '../../../api/backend';
import { useInvite } from '../../../hooks/useInvite';
import Svg, { Circle } from 'react-native-svg';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';
import Config from 'react-native-config';

type InviteScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Invite'
>;

type Props = {
  navigation: InviteScreenNavigationProp;
};

type CircularProgressProps = {
  progress: number;
  size?: number;
  strokeWidth?: number;
  max?: number;
};

const CircularProgress = ({
  progress,
  size = 58,
  strokeWidth = 5,
  max = 10,
}: CircularProgressProps) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const progressOffset = circumference - (progress / max) * circumference;

  return (
    <Svg width={size} height={size}>
      <Circle
        cx={size / 2}
        cy={size / 2}
        r={radius}
        stroke="#6a6a6a"
        strokeWidth={strokeWidth}
        fill="transparent"
      />
      <Circle
        cx={size / 2}
        cy={size / 2}
        r={radius}
        stroke="#B0A5E9"
        strokeWidth={strokeWidth}
        fill="transparent"
        strokeDasharray={`${circumference} ${circumference}`}
        strokeDashoffset={progressOffset}
        strokeLinecap="round"
        transform={`rotate(-180 ${size / 2} ${size / 2})`}
      />
    </Svg>
  );
};

export default function InviteView({ navigation }: Readonly<Props>) {
  const { t } = useTranslation();
  const { authToken } = useAuthProvider();
  const { inviteCode, inviteNumber, isLoading, error } = useInvite();
  const [nft, setNFT] = useState<number>(0);
  const inviteLink = Config.INVITE_LINK + '?code=' + inviteCode;

  useEffect(() => {
    const callGetNFT = async () => {
      if (authToken) {
        const nft = await getNFT(authToken);
        console.log('nft result', nft);
        if (nft?.data) {
          setNFT(nft.data);
        } else {
          setNFT(0);
        }
      }
    };
    callGetNFT();
  }, [authToken]);

  const handleCopy = () => {
    Clipboard.setString(inviteLink);
    Toast.show({
      type: 'info',
      position: Platform.OS === 'android' ? 'top' : 'bottom',
      text1: t('copied'),
    });
  };

  const handleShare = async (platform: string) => {
    try {
      await Share.share({
        message: inviteLink,
      });
    } catch (error) {
      console.warn(error);
    }
  };

  return (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}>
      <View
        style={{
          ...styles.section,
          marginBottom: 14,
        }}>
        <Text style={styles.title}>How to Invite</Text>
        <Text style={styles.subtitle}>Invite friends to scan the QR code</Text>

        <View style={styles.qrContainer}>
          <QRCode
            value={inviteLink}
            size={132}
            bgColor={'white'}
            fgColor={'black'}
          />
        </View>

        <TouchableOpacity style={styles.linkContainer} onPress={handleCopy}>
          <Text style={styles.link} numberOfLines={1}>
            {inviteLink}
          </Text>
          <CopyIcon width={20} height={20} />
        </TouchableOpacity>

        <View style={styles.socialContainer}>
          <TouchableOpacity onPress={() => handleShare('twitter')}>
            <TwitterIcon width={24} height={24} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleShare('telegram')}>
            <TelegramIcon width={24} height={24} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => handleShare('whatsapp')}>
            <WhatsAppIcon width={24} height={24} />
          </TouchableOpacity>
          {/* <TouchableOpacity onPress={() => handleShare('wechat')}>
            <WeChatIcon width={24} height={24} />
          </TouchableOpacity> */}
        </View>
      </View>

      {/* invite track number */}
      {/* <View style={styles.section}>
        <Text style={styles.trackerTitle}>Invitation Tracker</Text>
        <View
          style={{
            ...styles.rewardItem,
            paddingHorizontal: 30,
          }}>
          <Text style={styles.inviteNumberTitle}>Invite Number</Text>
          <Text style={styles.inviteNumber}>{inviteNumber}</Text>
        </View>
      </View> */}

      {/* Invitation Tracker */}
      <View
        style={{
          ...styles.section,
          marginBottom: 20,
        }}>
        <Text style={styles.trackerTitle}>Invitation Tracker</Text>

        <View style={styles.rewardItem}>
          <View>
            <Text style={styles.rewardTitle}>Referral Cash Rewards</Text>
            <Text style={styles.rewardValue}>1 vUSD</Text>
            <Text style={styles.rewardDesc}>
              per new friends invited to register,{'\n'}max 3 rewards.
            </Text>
          </View>
          <View style={styles.progressContainer}>
            <CircularProgress
              progress={inviteNumber > 5 ? 5 : inviteNumber}
              size={58}
              strokeWidth={5}
              max={5}
            />
            <Text style={styles.progressText}>{inviteNumber}/5</Text>
          </View>
        </View>

        <View style={styles.rewardItem}>
          <View>
            <Text style={styles.rewardTitle}>Proof for Future Airdrop</Text>
            <Text style={styles.rewardValue}>OG Badge</Text>
            <Text style={styles.rewardDesc}>
              Invite 3+ new friends to register.
            </Text>
          </View>
          <View style={styles.progressContainer}>
            <CircularProgress
              progress={inviteNumber > 3 ? 3 : inviteNumber}
              size={58}
              strokeWidth={5}
              max={3}
            />
            <Text style={styles.progressText}>{inviteNumber}/3</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#262626',
  },
  scrollContent: {
    paddingHorizontal: 28,
    paddingTop: 14,
    paddingBottom: 20,
  },
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#262626',
    paddingHorizontal: 28,
    paddingTop: 14,
  },
  section: {
    width: '100%',
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 16,
    paddingTop: 24,
    justifyContent: 'flex-start',
    alignItems: 'center',
    borderRadius: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 14,
    color: '#8F92A1',
    marginBottom: 36,
  },
  qrContainer: {
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 0,
    marginBottom: 56,
  },
  linkContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 14,
    borderRadius: 6,
    width: '100%',
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  link: {
    color: '#B0A5E9',
    fontSize: 14,
    marginRight: 5,
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
    marginBottom: 36,
  },
  trackerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginBottom: 38,
  },
  inviteNumberTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  inviteNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#B0A5E9',
  },
  rewardItem: {
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },

  rewardTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFFCC',
    marginBottom: 4,
  },
  rewardValue: {
    fontSize: 28,
    fontWeight: '600',
    color: '#B0A5E9',
    marginBottom: 4,
  },
  rewardDesc: {
    fontSize: 10,
    color: '#FFFFFFCC',
  },
  progressContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressText: {
    position: 'absolute',
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});
