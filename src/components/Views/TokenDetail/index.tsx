import {
  primaryBackgroundColor,
  primaryColor,
  primaryColor2,
  primaryColor3,
  primaryHeaderBackgroundColor,
} from '../../../theme/default';
import {Text, TouchableOpacity, View} from 'react-native';
import tw from 'twrnc';

import {useNavigation} from '@react-navigation/native';

import ReceiveIcon from '../../../assets/icons/qr.svg';
import SendIcon from '../../../assets/icons/send.svg';
import {RootStackParamList} from '../../Nav/routes';
import {UpNetworkLogo} from '../../UI/UpNetworkLogo';
import TokenTxHistoryList from '../../UI/SolanaWallet/Token/TokenTxHistoryList';
import {TokenLogo} from '../../UI/TokenLogo';
import {useTranslation} from 'react-i18next';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useEffect} from 'react';
import {UserToken} from '../../../db/models/userTokenModel';
import Icrc1TokenTxHistoryList from '../../UI/IcpWallet/Token/Icrc1TokenTxHistoryList';

type RootStackNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const TokenDetail = ({route}: any) => {
  const {tokenInfo} = route.params;
  const {t} = useTranslation();
  const navigation = useNavigation<RootStackNavigationProp>();

  const handleSend = () => {
    if ((tokenInfo as UserToken).type == 'icrc1') {
      navigation.navigate('SendIcrc1', {tokenInfo});
    } else {
      navigation.navigate('Send', {tokenInfo});
    }
  };

  useEffect(() => {
    navigation.setOptions({
      title: tokenInfo.name,
    });
  }, [tokenInfo]);
  return (
    <View
      style={{
        ...tw.style(`bg-[${primaryBackgroundColor}] min-h-full p-[20px]`),
        justifyContent: 'flex-start',
      }}>
      <View
        style={{
          height: '25%',
          justifyContent: 'center',
        }}>
        <View style={tw.style(`flex flex-col items-center gap-2`)}>
          <TokenLogo
            tokenName={tokenInfo.name}
            logoUri={tokenInfo.logo}
            size={48}
            borderRadius={48}
            backgroundColor="blue"
            textFontSize={12}></TokenLogo>
          <Text style={tw.style('text-white text-[14px]')}>
            {tokenInfo.uiAmount} {tokenInfo.symbol}
          </Text>
        </View>
        <View
          style={{
            ...tw.style(`flex flex-row items-center justify-around mt-[20px]`),
            backgroundColor: primaryHeaderBackgroundColor,
            paddingVertical: 12,
            borderRadius: 16,
          }}>
          <TouchableOpacity
            onPress={() => {
              handleSend();
            }}
            style={tw.style(`flex flex-col items-center gap-1`)}>
            <SendIcon width={32} height={32} color={primaryColor} />
            <Text style={tw.style(`text-[${primaryColor3}] text-[14px]`)}>
              {t('send')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('Receive', {tokenInfo});
            }}
            style={tw.style(`flex flex-col items-center gap-1`)}>
            <ReceiveIcon width={32} height={32} color={primaryColor} />
            <Text style={tw.style(`text-[${primaryColor3}] text-[14px]`)}>
              {t('receive')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      <View style={{maxHeight: '60%'}}>
        <Text
          style={tw.style(
            `text-[${primaryColor2}] text-[14px] font-bold mt-[30px] ml-[10]`,
          )}>
          {t('txHistory')}
        </Text>
        {(tokenInfo as UserToken).type == 'icrc1' ? (
          <Icrc1TokenTxHistoryList
            tokenInfo={tokenInfo}></Icrc1TokenTxHistoryList>
        ) : (
          <TokenTxHistoryList tokenInfo={tokenInfo}></TokenTxHistoryList>
        )}
      </View>
      <View
        style={{
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 48,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <UpNetworkLogo color={primaryColor}></UpNetworkLogo>
      </View>
    </View>
  );
};

export default TokenDetail;
