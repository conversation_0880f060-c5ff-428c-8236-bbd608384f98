import {StyleSheet} from 'react-native';
import {
  primaryHeaderBackgroundColor,
  primaryColor2,
} from '../../../theme/default';
const secondaryColor = 'rgba(255, 255, 255, 0.10)';
import {getStatusBarHeight} from '../../../utils';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: primaryHeaderBackgroundColor,
    paddingHorizontal: 16,
    paddingTop: getStatusBarHeight() + 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    backgroundColor: secondaryColor,
    borderRadius: 16,
    marginTop: 16,
    padding: 16,
    position: 'relative',
  },
  actionButton: {
    width: '50%',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: 3,
    position: 'relative',
    borderColor: 'rgba(255, 255, 255, 0.3)',
    zIndex: 1,
  },
  circle: {
    width: 16,
    height: 16,
    backgroundColor: '#282829',
    borderRadius: 8,
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{translateX: 8}, {translateY: 0}],
    zIndex: 2,
  },
  actionText: {
    color: '#fff',
    marginTop: 8,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 19,
  },
  soonIcon: {
    position: 'absolute',
    bottom: -16,
    right: -16,
    width: 80,
    height: 80,
  },
  yourTokens: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 16,
    opacity: 0.4,
  },
  tokenListContainer: {
    borderRadius: 16,
    padding: 16,
  },
  tokenRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  tokenInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tokenNameContainer: {
    marginLeft: 12,
  },
  tokenName: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  tokenAmount: {
    color: 'gray',
    marginTop: 4,
  },
  tokenValueContainer: {
    alignItems: 'flex-end',
  },
  tokenValue: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  tokenChange: {
    marginTop: 4,
  },
});

export default styles;
