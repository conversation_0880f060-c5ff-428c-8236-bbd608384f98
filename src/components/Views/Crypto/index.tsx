import React, {useState, useRef} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Image} from 'react-native';
import SendIcon from '../../../assets/icons/send-1.svg';
import ReceiveIcon from '../../../assets/icons/receive-1.svg';
import BuyIcon from '../../../assets/icons/buy.svg';
import EarnIcon from '../../../assets/icons/earn.svg';
import styles from './style';
import {TokenList} from '../../UI/SolanaWallet/Token/TokenList';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {RootStackParamList} from '../../Nav/routes';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import BottomSheet from '../../UI/BottomSheet/BottomSheet';
import {UserToken} from '../../../db/models/userTokenModel';
import SendView from '../Send';
import ReceiveView from '../Receive';
import SendIcrc1View from '../Send/icrc1';

type CryptoNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Crypto'
>;

type SendNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Send'>;
type SendIcrc1StackNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SendIcrc1'
>;
type ReceiveNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Receive'
>;

const Crypto = () => {
  const navigation = useNavigation<CryptoNavigationProp>();
  const isFocused = useIsFocused();
  const [isSendListBottomSheetVisible, setIsSendListBottomSheetVisible] =
    useState(false);
  const [isSendBottomSheetVisible, setIsSendBottomSheetVisible] =
    useState(false);
  const [isReceiveListBottomSheetVisible, setIsReceiveListBottomSheetVisible] =
    useState(false);
  const [isReceiveBottomSheetVisible, setIsReceiveBottomSheetVisible] =
    useState(false);
  const [currentTokenInfo, setCurrentTokenInfo] = useState<UserToken | null>(
    null,
  );

  const actionButtons = [
    {
      text: 'Send',
      icon: <SendIcon width={32} height={32} />,
      onPress: () => setIsSendListBottomSheetVisible(true),
    },
    {
      text: 'Receive',
      icon: <ReceiveIcon width={32} height={32} />,
      onPress: () => setIsReceiveListBottomSheetVisible(true),
    },
    {
      text: 'Buy Crypto',
      icon: <BuyIcon style={{opacity: 0.5}} width={32} height={32} />,
      onPress: () => {},
      soon: true,
    },
    {
      text: 'Earn',
      icon: <EarnIcon style={{opacity: 0.5}} width={32} height={32} />,
      onPress: () => {},
      soon: true,
    },
  ];

  return (
    <View style={styles.container}>
      {/* Fixed Header */}
      <View style={styles.actionsContainer}>
        {actionButtons.map((button, index) => (
          <TouchableOpacity
            key={index}
            style={{
              ...styles.actionButton,
              borderRightWidth: index === 0 ? 0.3 : 0,
              borderBottomWidth: index === 1 ? 0.3 : 0,
              borderTopWidth: index === 2 ? 0.3 : 0,
              borderLeftWidth: index === 3 ? 0.3 : 0,
              paddingTop: [2, 3].includes(index) ? 19 : 0,
            }}
            onPress={button.onPress}>
            {button.icon}
            <Text
              style={[
                styles.actionText,
                {
                  color: button.soon ? '#999' : '#fff',
                  opacity: button.soon ? 0.5 : 1,
                },
              ]}>
              {button.text}
            </Text>
            {button.soon && [3].includes(index) && (
              <Image
                source={require('../../../assets/images/soon.png')}
                style={{
                  ...styles.soonIcon,
                }}
                resizeMode="contain"
              />
            )}
          </TouchableOpacity>
        ))}
        <View style={styles.circle} />
      </View>

      <Text style={styles.yourTokens}>Your Tokens</Text>

      {/* Scrollable TokenList */}
      <TokenList
        onItemPress={item => {
          navigation.navigate('TokenDetail', {tokenInfo: item});
        }}
      />

      {/* Send list Bottom Sheet */}
      <BottomSheet
        isVisible={isSendListBottomSheetVisible && isFocused}
        onClose={() => setIsSendListBottomSheetVisible(false)}
        defaultPosition="50%"
        showBackButton={false}
        title="Send"
        containsVirtualizedList={true}>
        <TokenList
          onItemPress={item => {
            console.log('item', item);
            const tokenInfo = item as UserToken;
            setCurrentTokenInfo(tokenInfo);
            setIsSendBottomSheetVisible(true);
          }}
          type="select"
        />
      </BottomSheet>

      {/* Receive list Bottom Sheet */}
      <BottomSheet
        isVisible={isReceiveListBottomSheetVisible && isFocused}
        onClose={() => setIsReceiveListBottomSheetVisible(false)}
        defaultPosition="50%"
        showBackButton={false}
        title="Receive"
        containsVirtualizedList={true}>
        <TokenList
          onItemPress={item => {
            const tokenInfo = item as UserToken;
            setCurrentTokenInfo(tokenInfo);
            setIsReceiveBottomSheetVisible(true);
          }}
          type="select"
        />
      </BottomSheet>

      {/* send bottom sheet */}
      {currentTokenInfo && (
        <BottomSheet
          isVisible={isSendBottomSheetVisible && isFocused}
          onClose={() => setIsSendBottomSheetVisible(false)}
          defaultPosition="90%"
          title={`Send ${currentTokenInfo.name}`}>
          {(currentTokenInfo as UserToken).type == 'icrc1' ? (
            <SendIcrc1View
              route={{params: {tokenInfo: currentTokenInfo}} as any}
              navigation={navigation as unknown as SendIcrc1StackNavigationProp}
            />
          ) : (
            <SendView
              route={{params: {tokenInfo: currentTokenInfo}} as any}
              navigation={navigation as unknown as SendNavigationProp}
            />
          )}
        </BottomSheet>
      )}

      {/* receive bottom sheet */}
      {currentTokenInfo && (
        <BottomSheet
          isVisible={isReceiveBottomSheetVisible && isFocused}
          onClose={() => setIsReceiveBottomSheetVisible(false)}
          defaultPosition="90%"
          title={`Receive ${currentTokenInfo.name}`}>
          <ReceiveView
            route={{params: {tokenInfo: currentTokenInfo}} as any}
            navigation={navigation as unknown as ReceiveNavigationProp}
          />
        </BottomSheet>
      )}
    </View>
  );
};

export default Crypto;
