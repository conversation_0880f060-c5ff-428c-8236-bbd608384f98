import React, {useCallback, useEffect, useState, useRef, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  Animated,
  Dimensions,
} from 'react-native';
import {PanGestureHandler, State} from 'react-native-gesture-handler';
import {useTranslation} from 'react-i18next';
import USDCIcon from '../../../assets/chains/usdc.svg';
import VlyIcon from '../../../assets/chains/vusd.svg';
import {useIdentityProvider} from '../../../hooks/useSIWPIdentity';
import {useAccounts} from '../../../hooks/useAccounts';
import {useBridgeTransaction} from '../../../hooks/useBridgeTransaction';
import {User} from '../../../db/models/userModel';
import {useAtomValue, useSet<PERSON>tom} from 'jotai';
import {bridgeState<PERSON>tom, supported<PERSON><PERSON><PERSON><PERSON><PERSON>} from '../../../store/bridgeAtom';
import {SUPPORTED_CHAINS} from '../../../config';
import {useBridgeState} from '../../../hooks/useBridgeState';
import {useWalletConnect} from '../../../hooks/useWalletConnect';
import {useSolanaBridge} from '../../../hooks/useSolanaBridge';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import styles from './style';
import ArrowIcon from '../../../assets/icons/arrow.svg';
import AmountInput from '../../UI/Common/AmountInput';
import BridgeConfirm from './confirm';

const {width: screenWidth} = Dimensions.get('window');

// 滑动组件样式
const slideStyles = StyleSheet.create({
  slideContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

const BridgeView = () => {
  const {identityId} = useIdentityProvider();
  const {users} = useAccounts(identityId);
  const {t} = useTranslation();
  const user = users.find(user => user.userName === identityId) as User;
  const bridgeState = useAtomValue(bridgeStateAtom);
  const {connectWallet, getBalance, icService, solanaService} =
    useWalletConnect(user);
  const setChains = useSetAtom(supportedChainsAtom);
  const {executeBridge, status, progress, statusMessage, processing} =
    useBridgeTransaction(user);
  const {
    fromChainInfo,
    toChainInfo,
    bridgeAmount,
    setBridgeAmount,
    error: bridgeStateError,
    isProcessing,
    validateAmount,
    swapChains,
    resetBridge,
    fromChainData,
    toChainData,
  } = useBridgeState();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  // Animation related states
  const [showSlideComponent, setShowSlideComponent] = useState(false);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const mainContentAnim = useRef(new Animated.Value(0)).current;
  const gestureAnim = useRef(new Animated.Value(0)).current;
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [statusModalState, setStatusModalState] = useState('normal');

  // Intercept back navigation
  useFocusEffect(
    useCallback(() => {
      const unsubscribe = navigation.addListener('beforeRemove', e => {
        // If slide component is showing, intercept back and execute slide animation
        if (showSlideComponent) {
          e.preventDefault();
          handleSlideOut();
          return;
        }

        // If processing, prevent back
        if (isProcessing || processing) {
          e.preventDefault();
          return;
        }

        // If there is an input amount, ask user to confirm leaving
        if (bridgeAmount && parseFloat(bridgeAmount) > 0) {
          e.preventDefault();
          resetBridge();
          navigation.dispatch(e.data.action);
        }
      });

      return unsubscribe;
    }, [
      navigation,
      showSlideComponent,
      isProcessing,
      processing,
      bridgeAmount,
      resetBridge,
    ]),
  );

  // Gesture handling
  const onGestureEvent = Animated.event(
    [{nativeEvent: {translationX: gestureAnim}}],
    {useNativeDriver: true},
  );

  const onHandlerStateChange = useCallback(
    (event: any) => {
      if (event.nativeEvent.oldState === State.ACTIVE) {
        const {translationX} = event.nativeEvent;
        const progress = Math.abs(translationX) / screenWidth;

        // If gesture distance exceeds 30% of screen width, trigger slide back
        if (progress > 0.3 && showSlideComponent) {
          handleSlideOut();
        } else {
          // Reset gesture animation
          Animated.timing(gestureAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }).start();
        }
      }
    },
    [showSlideComponent],
  );

  // Slide in animation
  const handleSlideIn = () => {
    setShowSlideComponent(true);
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(mainContentAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Slide out animation
  const handleSlideOut = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(mainContentAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowSlideComponent(false);
      gestureAnim.setValue(0);
    });
  };

  const [validationError, setValidationError] = useState<string | null>(null);
  const [estimatedGasFee, setEstimatedGasFee] = useState<string | null>(null);
  const [isEstimatingFee, setIsEstimatingFee] = useState<boolean>(false);
  const [isSolBalanceSufficient, setIsSolBalanceSufficient] =
    useState<boolean>(true);

  const handleBridge = async () => {
    // Validate the amount once more before submitting
    const error = validateAmount(bridgeAmount);
    if (error) {
      setValidationError(error);
      return;
    }

    // Check for SOL balance if bridging from ICP to Solana
    if (bridgeState.fromChainId === 'icp' && !isSolBalanceSufficient) {
      setValidationError('Insufficient SOL balance for gas fees');
      return;
    }

    // Check for SOL balance if bridging from Solana to ICP
    if (bridgeState.fromChainId === 'solana' && !isSolBalanceSufficient) {
      setValidationError('Insufficient SOL balance for gas fees');
      return;
    }

    if (progress === 100 || progress === 0) {
      setShowStatusModal(true);
      setStatusModalState('normal');
      if (await executeBridge()) {
        fromChainInfo?.id && (await getBalance(fromChainInfo.id));
        toChainInfo?.id && (await getBalance(toChainInfo.id));
        // navigation.navigate('BridgeSuccess', {amount: bridgeAmount});
        setStatusModalState('success');
        return;
      }
      setShowStatusModal(false);
    }
    return;
  };

  // Handle amount input change
  const handleAmountChange = (value: string) => {
    console.log('value', value);
    // Allow empty string, numbers, decimal point and decimal values being typed (e.g., "0.")
    if (value === '' || /^(\d*\.?\d*)$/.test(value)) {
      setBridgeAmount(value);

      // Validate the amount
      if (value) {
        const error = validateAmount(value);
        setValidationError(error);
      } else {
        setValidationError(null);
      }
    }
  };

  const checkSolBalance = useCallback(async () => {
    if (!solanaService) return true;

    const solBalance = bridgeState.chains.solana.solBalance;
    console.log('solBalance', solBalance);
    if (!solBalance) return true;

    try {
      // 对于两种方向的交易，都需要检查SOL余额
      if (bridgeState.fromChainId === 'icp') {
        // ICP到Solana：需要SOL支付claim费用，可能包括创建token账户
        const tokenBalance = await solanaService.getTokenBalance();

        // 根据token账户是否存在设置不同的阈值
        // todo 993
        // const minRequired = tokenBalance === 0 ? 0.001 : 0.003;
        const minRequired = tokenBalance === 0 ? 0.00001 : 0.00001;

        // 比较并设置状态
        setIsSolBalanceSufficient(parseFloat(solBalance) >= minRequired);
      } else if (bridgeState.fromChainId === 'solana') {
        // Solana到ICP：需要SOL支付deposit费用
        const estimatedFee = await solanaService.estimateGasFee('deposit');

        // 添加一些缓冲余量
        const minRequired = Math.max(parseFloat(estimatedFee) * 1.2, 0.00001);

        // 比较并设置状态
        setIsSolBalanceSufficient(parseFloat(solBalance) >= minRequired);
      }
    } catch (error) {
      console.error('Error checking SOL balance:', error);
      // 出错时使用较高的阈值以确保安全
      setIsSolBalanceSufficient(parseFloat(solBalance) >= 0.003);
    }
  }, [
    solanaService,
    bridgeState.chains.solana.solBalance,
    bridgeState.fromChainId,
    bridgeState.bridgeAmount,
  ]);

  useEffect(() => {
    setChains(SUPPORTED_CHAINS);
  }, [setChains]);

  // Connect source chain wallet
  const handleConnectFromWallet = async () => {
    if (!fromChainInfo || !solanaService || !icService) return;
    await connectWallet(fromChainInfo.id);
  };

  // Connect destination chain wallet
  const handleConnectToWallet = async () => {
    if (!toChainInfo || !icService || !solanaService) return;
    await connectWallet(toChainInfo.id);
  };

  useEffect(() => {
    handleConnectFromWallet();
  }, [fromChainInfo, solanaService, icService]);

  useEffect(() => {
    handleConnectToWallet();
  }, [toChainInfo, solanaService, icService]);

  // Check SOL balance when relevant state changes
  useEffect(() => {
    checkSolBalance();
  }, [
    checkSolBalance,
    bridgeState.chains.solana.solBalance,
    bridgeState.fromChainId,
  ]);

  // Estimate gas fee when amount or chains change
  useEffect(() => {
    const estimateGasFee = async () => {
      if (!bridgeAmount || parseFloat(bridgeAmount) <= 0) {
        setEstimatedGasFee(null);
        return;
      }

      setIsEstimatingFee(true);

      try {
        // Different gas fee based on the source chain
        if (fromChainInfo?.id === 'icp') {
          if (solanaService) {
            try {
              const estimatedFee = await solanaService.estimateGasFee('claim');
              setEstimatedGasFee(`${estimatedFee} SOL`);
            } catch (error) {
              console.error('Error getting Solana fee estimate:', error);
              // Fallback to fixed value
              setEstimatedGasFee('~0.000005 SOL');
            }
          } else {
            setEstimatedGasFee('~0.000005 SOL');
          }
        } else if (fromChainInfo?.id === 'solana') {
          // Solana to ICP gas fee - now using the dynamic estimation method
          if (solanaService) {
            try {
              // 使用新的estimateGasFee方法获取动态费用估算
              const estimatedFee = await solanaService.estimateGasFee(
                'deposit',
              );
              setEstimatedGasFee(`${estimatedFee} SOL`);
            } catch (error) {
              console.error('Error getting Solana fee estimate:', error);
              // Fallback to fixed value
              setEstimatedGasFee('~0.000005 SOL');
            }
          } else {
            setEstimatedGasFee('~0.000005 SOL');
          }
        }
      } catch (error) {
        console.error('Error estimating gas fee:', error);
        setEstimatedGasFee('Error estimating gas fee');
      } finally {
        setIsEstimatingFee(false);
      }
    };

    estimateGasFee();
  }, [bridgeAmount, fromChainInfo?.id, toChainInfo?.id, solanaService]);

  // Determine the token symbol to display based on selected chain
  const fromTokenSymbol = fromChainInfo?.tokenSymbol;

  // Determine if submit button should be disabled
  const isSubmitDisabled =
    isProcessing ||
    !!validationError ||
    !bridgeAmount ||
    parseFloat(bridgeAmount || '0') <= 0 ||
    !isSolBalanceSufficient;

  console.log('isSubmitDisabled', isSubmitDisabled);

  // Get submit button text based on state
  const getSubmitButtonText = () => {
    if (isProcessing) return 'Processing...';
    if (!isSolBalanceSufficient) return 'Insufficient SOL for gas fees';
    return 'Review transaction';
  };

  const fromIcon = useMemo(() => {
    return fromChainInfo?.name === 'Solana' ? (
      <USDCIcon width={44} height={44} />
    ) : (
      <VlyIcon width={44} height={44} />
    );
  }, [fromChainInfo]);

  const toIcon = useMemo(() => {
    return toChainInfo?.name === 'Solana' ? (
      <USDCIcon width={44} height={44} />
    ) : (
      <VlyIcon width={44} height={44} />
    );
  }, [toChainInfo]);

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={styles.container}>
        <PanGestureHandler
          onGestureEvent={onGestureEvent}
          onHandlerStateChange={onHandlerStateChange}
          activeOffsetX={[-20, 20]}
          failOffsetY={[-20, 20]}>
          <Animated.View
            style={[
              styles.container,
              {
                transform: [
                  {
                    translateX: Animated.add(
                      mainContentAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, -screenWidth],
                      }),
                      gestureAnim,
                    ),
                  },
                ],
              },
            ]}>
            {/* new from card */}
            <View style={styles.inputWrapper}>
              {/* card */}
              <View style={styles.newCard}>
                <View style={styles.newCardLeft}>
                  {/* Icon */}
                  <View style={styles.newCardIcon}>{fromIcon}</View>
                  {/* Info */}
                  <View style={styles.newCardInfo}>
                    <Text style={styles.newCardTitle}>
                      {fromChainInfo?.tokenSymbol}
                    </Text>
                    <Text style={styles.newCardLine}>
                      {fromChainData.balance}
                    </Text>
                  </View>
                </View>

                {/* Use Max Button */}
                <TouchableOpacity
                  style={styles.newCardButton}
                  onPress={() => {
                    const balance =
                      parseFloat(fromChainData.balance || '0') || 0;
                    setBridgeAmount(balance.toString());
                    handleAmountChange(balance.toString());
                  }}>
                  <Text style={styles.newCardButtonText}>Use Max</Text>
                </TouchableOpacity>
              </View>
              {/* line */}
              <View style={styles.line} />
              {/* input */}
              <AmountInput
                value={bridgeAmount}
                onChangeText={handleAmountChange}
                validationError={validationError}
                showFiatValue={true}
                showSwapButton={true}
                onFocus={() => {
                  if (bridgeAmount === '0') {
                    setBridgeAmount('');
                  }
                }}
                onSwap={() => {
                  swapChains();
                  resetBridge();
                }}
              />
            </View>
            <View style={styles.arrowContainer}>
              <TouchableOpacity
                onPress={() => {
                  swapChains();
                  resetBridge();
                }}>
                <ArrowIcon width={50} height={50} />
              </TouchableOpacity>
            </View>
            <View style={styles.inputWrapper}>
              {/* new to card */}
              <View style={[styles.newCard, {backgroundColor: '#0F0F0F'}]}>
                <View style={styles.newCardLeft}>
                  {/* Icon */}
                  <View style={styles.newCardIcon}>{toIcon}</View>
                  {/* Info */}
                  <View style={styles.newCardInfo}>
                    <Text style={styles.newCardTitle}>
                      {toChainInfo?.tokenSymbol}
                    </Text>
                    <Text style={styles.newCardLine}>
                      {toChainData.balance}
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Info */}
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('Estimated Time')}</Text>
              <Text style={styles.infoValue}>{'~1 minutes'}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('Gas Fee')}</Text>
              <Text style={styles.infoValue}>{estimatedGasFee || '—'}</Text>
            </View>

            {/* Button */}
            <TouchableOpacity
              style={[
                styles.reviewButton,
                {
                  backgroundColor: isSubmitDisabled ? '#5e5e5e' : '#fff',
                },
              ]}
              onPress={handleSlideIn}
              // onPress={handleBridge}
              disabled={isSubmitDisabled}>
              <Text
                style={[
                  styles.reviewButtonText,
                  isSubmitDisabled && {color: '#000'},
                ]}>
                {getSubmitButtonText()}{' '}
                {progress !== 0 && progress !== 100 && `(${progress}%)`}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        </PanGestureHandler>

        {/* show slide component */}
        {showSlideComponent && (
          <Animated.View
            style={[
              slideStyles.slideContainer,
              {
                transform: [
                  {
                    translateX: Animated.add(
                      slideAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [screenWidth, 0],
                      }),
                      gestureAnim,
                    ),
                  },
                ],
              },
            ]}>
            <BridgeConfirm
              fromChainInfo={fromChainInfo}
              toChainInfo={toChainInfo}
              fromChainData={fromChainData}
              toChainData={toChainData}
              bridgeAmount={bridgeAmount}
              estimatedGasFee={estimatedGasFee}
              onConfirm={handleBridge}
              onClose={handleSlideOut}
              navigation={navigation}
              showStatusModal={showStatusModal}
              setShowStatusModal={setShowStatusModal}
              statusModalState={statusModalState}
              setStatusModalState={setStatusModalState}
            />
          </Animated.View>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default BridgeView;
