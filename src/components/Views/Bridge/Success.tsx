import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';

// 类型定义
// BridgeSuccess 页面参数类型
// 你需要在 routes.ts 里加上 BridgeSuccess: { amount: string };
type BridgeSuccessScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'BridgeSuccess'
>;
type BridgeSuccessScreenRouteProp = RouteProp<
  RootStackParamList,
  'BridgeSuccess'
>;

const BridgeSuccess = () => {
  const navigation = useNavigation<BridgeSuccessScreenNavigationProp>();
  const route = useRoute<BridgeSuccessScreenRouteProp>();
  const {amount} = route.params;

  return (
    <View style={styles.container}>
      <View style={styles.iconWrapper}>
        <View style={styles.circleOuter}>
          <View style={styles.circleInner}>
            <Text style={styles.check}>✓</Text>
          </View>
        </View>
      </View>
      <Text style={styles.successText}>Top-up Success!</Text>
      <Text style={styles.amountText}>{amount} vUSD</Text>
      <TouchableOpacity
        style={styles.button}
        onPress={() => navigation.navigate('Main')}>
        <Text style={styles.buttonText}>Back to home</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232325',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 100,
  },
  iconWrapper: {marginBottom: 32},
  circleOuter: {
    width: 220,
    height: 220,
    borderRadius: 110,
    backgroundColor: 'rgba(163,243,183,0.08)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  circleInner: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#D1FFD6',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 8,
    borderColor: '#A3F3B7',
  },
  check: {fontSize: 72, color: '#5DDC6D'},
  successText: {color: '#fff', fontSize: 24, marginTop: 24, marginBottom: 16},
  amountText: {
    color: '#B6A8F7',
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 40,
  },
  button: {
    backgroundColor: '#A18AFF',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 60,
    // position: 'absolute',
    // bottom: 60,
  },
  buttonText: {color: '#fff', fontSize: 18, fontWeight: 'bold'},
});

export default BridgeSuccess;
