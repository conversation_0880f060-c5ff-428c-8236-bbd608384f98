import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {
  primaryBackgroundColor2,
  primaryColor,
  secondaryColor,
} from '../../../theme/default';
import ArrowDownBridge from '../../../assets/icons/arrow_down_bridge.svg';
import SolanaIcon from '../../../assets/chains/solana.svg';
import USDCIcon from '../../../assets/chains/usdc.svg';
import VlyIcon from '../../../assets/chains/vusd.svg';
import {useIdentityProvider} from '../../../hooks/useSIWPIdentity';
import {useAccounts} from '../../../hooks/useAccounts';
import {useBridgeTransaction} from '../../../hooks/useBridgeTransaction';
import {User} from '../../../db/models/userModel';
import {useAtomValue, useSetAtom} from 'jotai';
import {bridgeStateAtom, supportedChainsAtom} from '../../../store/bridgeAtom';
import {SUPPORTED_CHAINS} from '../../../config';
import {useBridgeState} from '../../../hooks/useBridgeState';
import {useWalletConnect} from '../../../hooks/useWalletConnect';
import {useSolanaBridge} from '../../../hooks/useSolanaBridge';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import styles from './style';

const BridgeView = () => {
  const {identityId} = useIdentityProvider();
  const {users} = useAccounts(identityId);
  const {t} = useTranslation();
  const user = users.find(user => user.userName === identityId) as User;
  const bridgeState = useAtomValue(bridgeStateAtom);
  const {connectWallet, getBalance, icService, solanaService} =
    useWalletConnect(user);
  const setChains = useSetAtom(supportedChainsAtom);
  const {executeBridge, status, progress, statusMessage, processing} =
    useBridgeTransaction(user);
  const {
    fromChainInfo,
    toChainInfo,
    bridgeAmount,
    setBridgeAmount,
    error: bridgeStateError,
    isProcessing,
    validateAmount,
    swapChains,
    resetBridge,
    fromChainData,
    toChainData,
  } = useBridgeState();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  console.log('fromChainData', fromChainData);
  console.log('toChainData', toChainData);
  console.log('progress', progress);
  // console.log('uses', users);
  console.log('user', user);
  console.log('bridge state', {
    fromChainInfo,
    toChainInfo,
    bridgeAmount,
    setBridgeAmount,
    error: bridgeStateError,
    isProcessing,
    validateAmount,
  });

  const [validationError, setValidationError] = useState<string | null>(null);
  const [estimatedGasFee, setEstimatedGasFee] = useState<string | null>(null);
  const [isEstimatingFee, setIsEstimatingFee] = useState<boolean>(false);
  const [isSolBalanceSufficient, setIsSolBalanceSufficient] =
    useState<boolean>(true);

  const handleBridge = async () => {
    // Validate the amount once more before submitting
    const error = validateAmount(bridgeAmount);
    if (error) {
      setValidationError(error);
      return;
    }

    // Check for SOL balance if bridging from ICP to Solana
    if (bridgeState.fromChainId === 'icp' && !isSolBalanceSufficient) {
      setValidationError('Insufficient SOL balance for gas fees');
      return;
    }

    // Check for SOL balance if bridging from Solana to ICP
    if (bridgeState.fromChainId === 'solana' && !isSolBalanceSufficient) {
      setValidationError('Insufficient SOL balance for gas fees');
      return;
    }

    if (progress === 100 || progress === 0) {
      if (await executeBridge()) {
        fromChainInfo?.id && (await getBalance(fromChainInfo.id));
        toChainInfo?.id && (await getBalance(toChainInfo.id));
        navigation.navigate('BridgeSuccess', {amount: bridgeAmount});
        return;
      }
    }
    return;
  };

  // Handle amount input change
  const handleAmountChange = (value: string) => {
    console.log('value', value);
    // Allow empty string, numbers, decimal point and decimal values being typed (e.g., "0.")
    if (value === '' || /^(\d*\.?\d*)$/.test(value)) {
      setBridgeAmount(value);

      // Validate the amount
      if (value) {
        const error = validateAmount(value);
        setValidationError(error);
      } else {
        setValidationError(null);
      }
    }
  };

  const checkSolBalance = useCallback(async () => {
    if (!solanaService) return true;

    const solBalance = bridgeState.chains.solana.solBalance;
    console.log('solBalance', solBalance);
    if (!solBalance) return true;

    try {
      // 对于两种方向的交易，都需要检查SOL余额
      if (bridgeState.fromChainId === 'icp') {
        // ICP到Solana：需要SOL支付claim费用，可能包括创建token账户
        const tokenBalance = await solanaService.getTokenBalance();

        // 根据token账户是否存在设置不同的阈值
        // todo 993
        // const minRequired = tokenBalance === 0 ? 0.001 : 0.003;
        const minRequired = tokenBalance === 0 ? 0.00001 : 0.00001;

        // 比较并设置状态
        setIsSolBalanceSufficient(parseFloat(solBalance) >= minRequired);
      } else if (bridgeState.fromChainId === 'solana') {
        // Solana到ICP：需要SOL支付deposit费用
        const estimatedFee = await solanaService.estimateGasFee('deposit');

        // 添加一些缓冲余量
        const minRequired = Math.max(parseFloat(estimatedFee) * 1.2, 0.00001);

        // 比较并设置状态
        setIsSolBalanceSufficient(parseFloat(solBalance) >= minRequired);
      }
    } catch (error) {
      console.error('Error checking SOL balance:', error);
      // 出错时使用较高的阈值以确保安全
      setIsSolBalanceSufficient(parseFloat(solBalance) >= 0.003);
    }
  }, [
    solanaService,
    bridgeState.chains.solana.solBalance,
    bridgeState.fromChainId,
    bridgeState.bridgeAmount,
  ]);

  useEffect(() => {
    setChains(SUPPORTED_CHAINS);
  }, [setChains]);

  // Connect source chain wallet
  const handleConnectFromWallet = async () => {
    if (!fromChainInfo || !solanaService || !icService) return;
    await connectWallet(fromChainInfo.id);
  };

  // Connect destination chain wallet
  const handleConnectToWallet = async () => {
    if (!toChainInfo || !icService || !solanaService) return;
    await connectWallet(toChainInfo.id);
  };

  useEffect(() => {
    handleConnectFromWallet();
  }, [fromChainInfo, solanaService, icService]);

  useEffect(() => {
    handleConnectToWallet();
  }, [toChainInfo, solanaService, icService]);

  // Check SOL balance when relevant state changes
  useEffect(() => {
    checkSolBalance();
  }, [
    checkSolBalance,
    bridgeState.chains.solana.solBalance,
    bridgeState.fromChainId,
  ]);

  // Estimate gas fee when amount or chains change
  useEffect(() => {
    const estimateGasFee = async () => {
      if (!bridgeAmount || parseFloat(bridgeAmount) <= 0) {
        setEstimatedGasFee(null);
        return;
      }

      setIsEstimatingFee(true);

      try {
        // Different gas fee based on the source chain
        if (fromChainInfo?.id === 'icp') {
          if (solanaService) {
            try {
              const estimatedFee = await solanaService.estimateGasFee('claim');
              setEstimatedGasFee(`${estimatedFee} SOL`);
            } catch (error) {
              console.error('Error getting Solana fee estimate:', error);
              // Fallback to fixed value
              setEstimatedGasFee('~0.000005 SOL');
            }
          } else {
            setEstimatedGasFee('~0.000005 SOL');
          }
        } else if (fromChainInfo?.id === 'solana') {
          // Solana to ICP gas fee - now using the dynamic estimation method
          if (solanaService) {
            try {
              // 使用新的estimateGasFee方法获取动态费用估算
              const estimatedFee = await solanaService.estimateGasFee(
                'deposit',
              );
              setEstimatedGasFee(`${estimatedFee} SOL`);
            } catch (error) {
              console.error('Error getting Solana fee estimate:', error);
              // Fallback to fixed value
              setEstimatedGasFee('~0.000005 SOL');
            }
          } else {
            setEstimatedGasFee('~0.000005 SOL');
          }
        }
      } catch (error) {
        console.error('Error estimating gas fee:', error);
        setEstimatedGasFee('Error estimating gas fee');
      } finally {
        setIsEstimatingFee(false);
      }
    };

    estimateGasFee();
  }, [bridgeAmount, fromChainInfo?.id, toChainInfo?.id, solanaService]);

  // Determine the token symbol to display based on selected chain
  const fromTokenSymbol = fromChainInfo?.tokenSymbol;

  // Determine if submit button should be disabled
  const isSubmitDisabled =
    isProcessing ||
    !!validationError ||
    !bridgeAmount ||
    parseFloat(bridgeAmount || '0') <= 0 ||
    !isSolBalanceSufficient;

  // Get submit button text based on state
  const getSubmitButtonText = () => {
    if (isProcessing) return 'Processing...';
    if (!isSolBalanceSufficient) return 'Insufficient SOL for gas fees';
    return 'Bridge Tokens';
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={styles.container}>
        {/* From Card */}
        <View style={styles.card}>
          <View style={styles.cardRow}>
            <View style={styles.cardRowLeft}>
              <Text style={styles.cardLabel}>{t('From')}</Text>
              <View style={styles.cardType}>
                <Text style={styles.cardTypeText}>Funding</Text>
              </View>
            </View>
            <View style={styles.chainType}>
              <View style={styles.chainTypeIcon}>
                {fromChainInfo?.name === 'Solana' ? (
                  <SolanaIcon width={20} height={20} />
                ) : (
                  <VlyIcon width={20} height={20} />
                )}
              </View>
              <Text style={styles.chainTypeText}>{fromChainInfo?.name}</Text>
            </View>
          </View>
          <View style={styles.tokenRow}>
            <View style={styles.tokenRowLeft}>
              <View style={styles.tokenRowIcon}>
                {fromChainInfo?.name === 'Solana' ? (
                  <USDCIcon width={21} height={21} />
                ) : (
                  <VlyIcon width={21} height={21} />
                )}
              </View>
              <Text style={styles.tokenSymbol}>{fromTokenSymbol}</Text>
            </View>
            <Text style={styles.balance}>
              {t('Balance')}: {fromChainData.balance}
            </Text>
          </View>
          <View style={styles.amountRow}>
            <TextInput
              style={styles.amountInput}
              value={bridgeAmount}
              onChangeText={handleAmountChange}
              keyboardType="numeric"
              placeholder="0.00"
            />
            <TouchableOpacity
              style={styles.maxBtn}
              onPress={() => {
                setBridgeAmount(
                  parseFloat(fromChainData.balance || '0').toString(),
                );
              }}>
              <Text style={styles.maxBtnText}>Max</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.amountHint}>≈$ {bridgeAmount}</Text>
        </View>

        {/* Arrow */}
        <View style={styles.arrowContainer}>
          <TouchableOpacity
            onPress={() => {
              swapChains();
              resetBridge();
            }}>
            <ArrowDownBridge width={56} height={56} />
          </TouchableOpacity>
        </View>

        {/* To Card */}
        <View style={styles.card}>
          <View style={styles.cardRow}>
            <View style={styles.cardRowLeft}>
              <Text style={styles.cardLabel}>{t('To')}</Text>
              <View style={styles.cardType}>
                <Text style={styles.cardTypeText}>Spending</Text>
              </View>
            </View>
            <View style={styles.chainType}>
              <View style={styles.chainTypeIcon}>
                {toChainInfo?.name === 'Solana' ? (
                  <SolanaIcon width={20} height={20} />
                ) : (
                  <VlyIcon width={20} height={20} />
                )}
              </View>
              <Text style={styles.chainTypeText}>{toChainInfo?.name}</Text>
            </View>
          </View>
          <View style={styles.tokenRow}>
            <View style={styles.tokenRowLeft}>
              <View style={styles.tokenRowIcon}>
                {toChainInfo?.name === 'Solana' ? (
                  <USDCIcon width={21} height={21} />
                ) : (
                  <VlyIcon width={21} height={21} />
                )}
              </View>
              <Text style={styles.tokenSymbol}>{toChainInfo?.tokenSymbol}</Text>
            </View>
            <Text style={styles.balance}>
              {t('Balance')}: {toChainData.balance}
            </Text>
          </View>
          <View style={styles.amountRow}>
            <TextInput
              style={styles.amountInput}
              value={bridgeAmount}
              onChangeText={handleAmountChange}
              keyboardType="numeric"
              placeholder="0.00"
            />
          </View>
          <Text style={styles.amountHint}>≈$ {bridgeAmount}</Text>
        </View>

        {/* Info */}
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('Estimated Time')}</Text>
          <Text style={styles.infoValue}>{'~1 minutes'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('Gas Fee')}</Text>
          <Text style={styles.infoValue}>{estimatedGasFee || '—'}</Text>
        </View>

        {/* Warning */}
        <Text style={styles.warning}>{validationError}</Text>

        {/* Button */}
        <TouchableOpacity
          style={{
            ...styles.topupBtn,
            // opacity: isSubmitDisabled ? 0.5 : 1,
            backgroundColor: isSubmitDisabled ? '#9D9BA1' : '#A18AFF',
          }}
          onPress={handleBridge}
          disabled={isSubmitDisabled}>
          <Text style={styles.topupBtnText}>
            {getSubmitButtonText()}{' '}
            {progress !== 0 && progress !== 100 && `(${progress}%)`}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default BridgeView;
