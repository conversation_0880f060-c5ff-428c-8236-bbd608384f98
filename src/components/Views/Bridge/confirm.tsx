import {Text, TouchableOpacity, View, StyleSheet, Platform} from 'react-native';
import React, {useEffect, useState} from 'react';
import {TokenLogo} from '../../UI/TokenLogo';
import {useTranslation} from 'react-i18next';
import StatusModal from '../../UI/Common/StatusModal';
import {toShortAddress} from '../../../utils';
import FaceIcon from '../../../assets/icons/face.svg';
import ArrowIcon from '../../../assets/icons/arrow.svg';
import {Chain, ChainWalletData} from '../../../store/bridgeAtom';
import SolanaIcon from '../../../assets/chains/solana.svg';
import USDCIcon from '../../../assets/chains/usdc.svg';
import VlyIcon from '../../../assets/chains/vusd.svg';

interface BridgeConfirmProps {
  fromChainInfo: Chain | null;
  toChainInfo: Chain | null;
  fromChainData: ChainWalletData;
  toChainData: ChainWalletData;
  bridgeAmount: string;
  estimatedGasFee: string | null;
  onConfirm: () => void;
  onClose: () => void;
  navigation: any;
  showStatusModal: boolean;
  setShowStatusModal: (show: boolean) => void;
  statusModalState: string;
  setStatusModalState: (state: string) => void;
}

const BridgeConfirm: React.FC<BridgeConfirmProps> = ({
  fromChainInfo,
  toChainInfo,
  fromChainData,
  toChainData,
  bridgeAmount,
  estimatedGasFee,
  onConfirm,
  onClose,
  navigation,
  showStatusModal,
  setShowStatusModal,
  statusModalState,
  setStatusModalState,
}) => {
  const {t} = useTranslation();

  const handelConfirm = () => {
    onConfirm();
  };

  // Get token icon component based on chain info
  const getTokenIcon = (chainInfo: Chain | null) => {
    if (!chainInfo) return <SolanaIcon width={56} height={56} />;

    if (chainInfo.id === 'solana') {
      return <USDCIcon width={56} height={56} />;
    } else if (chainInfo.id === 'icp') {
      return <VlyIcon width={56} height={56} />;
    }
    return <SolanaIcon width={56} height={56} />;
  };

  // Get token name based on chain info
  const getTokenName = (chainInfo: Chain | null) => {
    if (!chainInfo) return ' ';
    return chainInfo.name;
  };

  // Get token symbol based on chain info
  const getTokenSymbol = (chainInfo: Chain | null) => {
    if (!chainInfo) return ' ';
    return chainInfo.tokenSymbol;
  };

  return (
    <View style={styles.container}>
      {/* token info */}
      <View style={styles.tokenInfo}>
        <View style={styles.tokenInfoSection}>
          <View style={styles.tokenInfoLogo}>
            {getTokenIcon(fromChainInfo)}
          </View>
          <View style={styles.tokenInfoTitleContainer}>
            <Text style={styles.tokenInfoTitleText}>Swapping</Text>
            <Text style={styles.tokenInfoContentText}>
              {getTokenSymbol(fromChainInfo)}
            </Text>
          </View>
        </View>
        <ArrowIcon style={styles.tokenInfoArrow} width={50} height={50} />
        <View style={styles.tokenInfoSection}>
          <View style={styles.tokenInfoLogo}>{getTokenIcon(toChainInfo)}</View>
          <View style={styles.tokenInfoTitleContainer}>
            <Text style={styles.tokenInfoTitleText}>Receiving</Text>
            <Text style={styles.tokenInfoContentText}>
              {getTokenSymbol(toChainInfo)}
            </Text>
          </View>
        </View>
        <Text style={[styles.tokenInfoTitle]}>Review & Confirm Swap</Text>
      </View>
      {/* payment info */}
      <View style={styles.confirmInfo}>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>
            {t('Swap')} {getTokenSymbol(fromChainInfo)}
          </Text>
          <Text style={styles.confirmInfoItemValue}>
            {bridgeAmount} {getTokenSymbol(fromChainInfo)}
          </Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={[styles.confirmInfoItemTitle]}>
            {t('Receive')} {getTokenSymbol(toChainInfo)}
          </Text>
          <Text style={[styles.confirmInfoItemValue, {color: '#00CA47'}]}>
            {bridgeAmount} {getTokenSymbol(toChainInfo)}
          </Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('Wallet from')}</Text>
          <Text style={styles.confirmInfoItemValue}>
            {fromChainData.address
              ? toShortAddress(fromChainData.address)
              : 'Not connected'}
          </Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('Wallet to')}</Text>
          <Text style={styles.confirmInfoItemValue}>
            {toChainData.address
              ? toShortAddress(toChainData.address)
              : 'Not connected'}
          </Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('Estimated time')}</Text>
          <Text style={styles.confirmInfoItemValue}>1 min</Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('Gas Fee')}</Text>
          <Text style={styles.confirmInfoItemValue}>
            {estimatedGasFee || '--'}
          </Text>
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={() => handelConfirm()}>
          <FaceIcon width={24} height={24} style={{marginRight: 8}} />
          <Text style={styles.buttonText}>Confirm transfer</Text>
        </TouchableOpacity>
      </View>

      <StatusModal
        visible={showStatusModal}
        status={statusModalState as 'normal' | 'success'}
        onClose={() => {
          setShowStatusModal(false);
        }}
        onSuccess={() => {
          setShowStatusModal(false);
          navigation.reset({
            index: 0,
            routes: [{name: 'Main' as never}],
          });
        }}
        message={`Swap Started`}
        successMessage={`Swap Successful`}
        logo={getTokenIcon(fromChainInfo)}
        logo1={getTokenIcon(toChainInfo)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    paddingTop: 60,
    paddingBottom: 120,
    paddingHorizontal: 40,
  },
  tokenInfo: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    marginBottom: 24,
    width: '100%',
    position: 'relative',
    zIndex: 1,
  },
  tokenInfoSection: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  tokenInfoArrow: {
    position: 'relative',
    zIndex: 2,
    marginVertical: -20,
    marginLeft: 2,
  },
  tokenInfoLogo: {
    width: 56,
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  tokenInfoTitleContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  tokenInfoTitleText: {
    fontSize: 17,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.4)',
    marginBottom: 4,
  },
  tokenInfoContentText: {
    fontSize: 19,
    fontWeight: '500',
    color: '#fff',
  },
  tokenInfoTitle: {
    fontSize: 20,
    fontWeight: '500',
    color: '#fff',
    marginTop: 53,
  },
  buttonContainer: {
    width: '100%',
    marginTop: 64,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '95%',
    height: 48,
    backgroundColor: '#fff',
    borderRadius: 24,
  },
  buttonText: {
    color: '#000',
    textAlign: 'center',
    fontWeight: '500',
    fontSize: 17,
  },
  horizontalLine: {
    borderBottomColor: '#4e4d4d',
    borderBottomWidth: 1,
    width: '100%',
    marginVertical: 10,
  },
  confirmInfo: {
    width: '100%',
  },
  confirmInfoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 24,
  },
  confirmInfoItemTitle: {
    fontSize: 17,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.4)',
  },
  confirmInfoItemValue: {
    fontSize: 17,
    fontWeight: '500',
    color: '#fff',
  },
});
export default BridgeConfirm;
