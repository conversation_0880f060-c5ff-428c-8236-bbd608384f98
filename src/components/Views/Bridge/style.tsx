import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    padding: 16,
  },
  inputWrapper: {
    flexDirection: 'column',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#0F0F0F',
  },
  line: {
    width: '100%',
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  header: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
    alignSelf: 'center',
  },
  card: {
    backgroundColor: '#2B3342',
    borderRadius: 16,
    padding: 16,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 11,
  },
  cardRowLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  cardLabel: {
    color: '#fff',
    fontWeight: '400',
    fontSize: 12,
    marginRight: 8,
  },
  cardType: {
    backgroundColor: '#252B36',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  cardTypeText: {
    fontWeight: '400',
    fontSize: 12,
    color: '#fff',
  },
  chainType: {
    backgroundColor: '#252B36',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 6,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chainTypeIcon: {
    marginRight: 10,
    backgroundColor: '#2B3342',
    borderRadius: 20,
    overflow: 'hidden',
  },
  chainTypeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '400',
  },
  tokenRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  tokenRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(37, 43, 54, 1)',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 8,
  },
  tokenRowIcon: {
    marginRight: 8,
  },
  tokenSymbol: {
    color: '#fff',
    fontWeight: '400',
    fontSize: 12,
  },
  balance: {
    color: 'rgba(113, 122, 140, 1)',
    fontSize: 12,
    fontWeight: '400',
  },
  amountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  amountInputBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    paddingHorizontal: 0,
    backgroundColor: 'transparent',
    marginBottom: 8,
    marginTop: 8,
    position: 'relative',
  },
  amountInputMain: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  amountInputNumber: {
    color: '#fff',
    fontSize: 56,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 72,
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingVertical: 0,
    paddingHorizontal: 0,
    margin: 0,
    includeFontPadding: false,
  },
  amountInputFiat: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 8,
  },
  amountInputArrowBtn: {
    backgroundColor: 'transparent',
    width: 16,
    height: 14,
    position: 'absolute',
    right: 0,
    top: '50%',
    transform: [{translateY: -7}],
  },
  maxBtn: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    marginLeft: 8,
  },
  maxBtnText: {
    color: 'rgba(176, 165, 233, 1)',
    fontWeight: '400',
    fontSize: 12,
  },
  amountHint: {
    color: 'rgba(113, 122, 140, 1)',
    fontSize: 8,
    marginBottom: 0,
  },
  arrowContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: -14,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 0,
  },
  infoLabel: {
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: 16,
    fontWeight: '400',
  },
  infoValue: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '400',
  },
  warning: {
    color: '#FF4D4F',
    fontSize: 14,
    marginTop: 12,
    textAlign: 'left',
  },
  topupBtn: {
    backgroundColor: '#A18AFF',
    borderRadius: 12,
    paddingVertical: 18,
    alignItems: 'center',
    marginTop: 16,
  },
  topupBtnText: {
    color: '#fff',
    fontWeight: '400',
    fontSize: 16,
  },
  newCard: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 20,
    paddingVertical: 12,
    width: '100%',
  },
  newCardLeft: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flex: 1,
  },
  newCardIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#2566D6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  newCardInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  newCardTitle: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '500',
    marginBottom: 2,
  },
  newCardLine: {
    color: 'rgba(255, 255, 255, 0.4)',
    fontSize: 15,
    fontWeight: '500',
  },
  newCardButton: {
    backgroundColor: '#252B36',
    borderRadius: 24,
    paddingHorizontal: 12,
    paddingVertical: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  newCardButtonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '500',
  },
  reviewButton: {
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 0,
    marginTop: 64,
    marginBottom: 8,
  },
  reviewButtonText: {
    color: '#111',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default styles;
