import React from 'react';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {RootStackParamList} from '../../Nav/routes';
import PaymentCardFromPosScan from '../../UI/Payment/PaymentCardFromPosScan';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

type PaymentScreenRouteProp = RouteProp<
  RootStackParamList,
  'PaymentFromPosScan'
>;

type Props = {
  route: PaymentScreenRouteProp;
};

export default function PaymentFromPosScan({route}: Readonly<Props>) {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {orderId, payerWallet, timestamp} = route.params;
  return (
    <PaymentCardFromPosScan
      orderId={orderId}
      payerWallet={payerWallet}
      timestamp={timestamp}
      onReceipt={receipt => {
        navigation.replace('PaymentReceipt', {
          paymentReceipt: receipt,
        });
      }}></PaymentCardFromPosScan>
  );
}
