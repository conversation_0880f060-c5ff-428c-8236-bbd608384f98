import React from 'react';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from '../../Nav/routes';
import PaymentCard from '../../UI/Payment/PaymentCard2';
import PaymentCardPaynow from '../../UI/Payment/PaymentCardPaynow';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type PaymentScreenRouteProp = RouteProp<RootStackParamList, 'Payment'>;

type Props = {
  route: PaymentScreenRouteProp;
};

export default function PaymentView({ route }: Readonly<Props>) {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { orderId, paynowId } = route.params;
  if (orderId) {
    return (
      <PaymentCard
        orderId={orderId}
        onReceipt={receipt => {
          navigation.replace('PaymentReceipt', {
            paymentReceipt: receipt,
          });
        }}></PaymentCard>
    );
  } else if (paynowId) {
    return (
      <PaymentCardPaynow
        orderId={paynowId}
        onReceipt={receipt => {
          navigation.replace('PaymentReceipt', {
            paymentReceipt: receipt,
          });
        }}></PaymentCardPaynow>
    );
  }

}
