import * as React from 'react';
import {Platform, View} from 'react-native';

import {NativeStackNavigationProp} from '@react-navigation/native-stack';

import {TokenList} from '../../UI/SolanaWallet/Token/TokenList';
import {TotalAssets} from '../../UI/SolanaWallet/Token/TotalAssets';
import {useScannerProvider} from '../../../hooks/useScannerProvider';
import {primaryBackgroundColor} from '../../../theme/default';
import {AirdropBar} from '../../UI/SolanaWallet/AirdropBar';
import {SwitchWalletLoadingModal} from '../../UI/SolanaWallet/SwitchWalletLoadingModal';
import {SwitchWalletModal} from '../../UI/SolanaWallet/SwitchWalletModal';
import {WalletBar} from '../../UI/SolanaWallet/WalletBar';
import {RootStackParamList} from '../../Nav/routes';
import {selectUniwebOrder} from '../../../api/backend';
import {getOrderById} from '../../../api/backend/kyc';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {useNdefOrderProvider} from '../../../hooks/useNdefOrderProvider';
import {useJPush} from '../../../hooks/useJPush';
import {useAccountProvider} from '../../../hooks/useAccountProvider';
import {useAuthProvider} from '../../../hooks/useAuthProvider';

type WalletScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Main'
>;

type Props = {
  navigation: WalletScreenNavigationProp;
};

function Wallet({navigation}: Readonly<Props>) {
  const isFocused = useIsFocused();
  const {codeResult} = useScannerProvider();
  const {t} = useTranslation();
  const {userInfo} = useAccountProvider();
  const {authToken} = useAuthProvider();
  const navigation2 =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const {orderInfo} = useNdefOrderProvider();

  React.useEffect(() => {
    const handleQrCodeResult = async () => {
      console.log('wallet view scan code result', codeResult);
      if (codeResult !== undefined) {
        if (!authToken) {
          console.warn('get token failed, authToken is null');
          return navigation.navigate('Payment', {
            orderId: codeResult.codeResult,
          });
        }
        const orderInfo = await getOrderById(authToken, codeResult.codeResult);
        console.log('codeResult:', codeResult.codeResult);
        console.log('orderInfo:', JSON.stringify(orderInfo));
        if (orderInfo != null) {
          navigation.navigate('Payment', {orderId: codeResult.codeResult});
        } else {
          Toast.show({
            type: 'info',
            text1: t('QRCodeTile'),
            text2: t('unsupportedQRCode'),
            position: Platform.OS === 'android' ? 'top' : 'bottom',
          });
        }
      }
    };
    if (isFocused) {
      handleQrCodeResult();
    }
  }, [codeResult]);

  React.useEffect(() => {
    const handleNfcOrder = async () => {
      if (orderInfo !== undefined) {
        if (orderInfo.orderNo != 'error') {
          if (!authToken) {
            console.warn('get token failed, authToken is null');
            return;
          }
          const uniWebOrderInfo = await getOrderById(
            authToken,
            orderInfo.orderNo,
          );
          if (uniWebOrderInfo != null) {
            navigation.navigate('Payment', {orderId: orderInfo.orderNo});
          }
        }
      }
    };
    if (isFocused) {
      handleNfcOrder();
    }
  }, [orderInfo]);
  const {useJPushInit} = useJPush();
  React.useEffect(() => {
    //userInfo?.principalId
    if (userInfo?.principalId) {
      useJPushInit(userInfo?.principalId);
    }
  }, []);
  return (
    // <ImageBackground source={require('../../../assets/73679001.png')} resizeMode='repeat' style={{
    //     flex: 1,
    //     justifyContent: 'flex-start',
    //     alignItems: 'center',
    // }}>
    <View
      style={{
        flex: 1,
        justifyContent: 'flex-start',
        alignItems: 'center',
        backgroundColor: primaryBackgroundColor,
        paddingBottom: 100,
      }}>
      <TotalAssets userTokenList={[]} />
      <WalletBar navigation={navigation} />
      <AirdropBar />

      <TokenList
        onItemPress={item => {
          navigation2.push('TokenDetail', {tokenInfo: item});
        }}
      />
      <SwitchWalletModal />
      <SwitchWalletLoadingModal />
    </View>

    // </ImageBackground>
  );
}

export default Wallet;
