import React, {useEffect, useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Image} from 'react-native';
import {useSolanaOwnerTokenAccountsProvider} from '../../../hooks/useSolana/useSolanaOwnerTokenAccounts';
import {useTokenPriceProvider} from '../../../hooks/useTokenPriceProvider';
import BigNumber from 'bignumber.js';
import {formatCurrency} from '../../../utils';
import {DefaultDecimals} from '../../../config';
import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import {WalletBar} from '../../UI/SolanaWallet/WalletBar';
import {useAssetVisible} from '../../../hooks/useAssetVisible';
import {useAccountProvider} from '../../../hooks/useAccountProvider';
import {useKYC} from '../../../hooks/useKYC';
import {useSwitchWalletModalProvider} from '../../../hooks/useSwitchWalletModalProvider';
import {VerificationStatusTag} from '../../UI/KYC';
import CardIcon from '../../../assets/icons/card.svg';
import MessageIcon from '../../../assets/icons/message.svg';

const WalletHeader = ({
  onSendPress,
  onReceivePress,
}: {
  onSendPress: () => void;
  onReceivePress: () => void;
}) => {
  const {accountsloading, userTokens} = useSolanaOwnerTokenAccountsProvider();
  const {priceLoading, tokenPrices} = useTokenPriceProvider();
  const [tokenTotalValue, setTokenTotalValue] = useState<number>(0);
  const [vUsdValue, setVUsdValue] = useState<number>(0);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const [showAsset] = useAssetVisible();
  const {userInfo} = useAccountProvider();
  const {userProfile, fetchUserProfile} = useAccountProvider();
  const {launchSNSMobileSDK, getToken} = useKYC();
  const {toggleModal} = useSwitchWalletModalProvider();
  const [loading, setLoading] = useState(false);

  const startKYC = async () => {
    if (loading) {
      return;
    }
    setLoading(true);
    console.log('startKYC');
    const token = await getToken();
    console.log('token', token);
    if (token) {
      launchSNSMobileSDK(() => {
        fetchUserProfile(true);
      });
    }
    setLoading(false);
  };

  useEffect(() => {
    if (!priceLoading && !accountsloading) {
      let totalValue = BigNumber(0);
      for (let i = 0; i < userTokens.length; i++) {
        const tokenPrice = tokenPrices.find(item => {
          return (
            item.symbol.toLowerCase() == userTokens[i].symbol.toLowerCase()
          );
        });
        if (tokenPrice != undefined) {
          const tokenValue = BigNumber(userTokens[i].uiAmount!).multipliedBy(
            BigNumber(tokenPrice.price),
          );
          totalValue = totalValue.plus(tokenValue);
          setTokenTotalValue(totalValue.toNumber());
        }
      }
    }
  }, [accountsloading, priceLoading]);

  useEffect(() => {
    console.log('tokenTotalValue', tokenTotalValue);
    const vUsdToSGDPriceObj = tokenPrices.find(item => {
      return item.symbol.toLowerCase() == 'vusd';
    });
    if (vUsdToSGDPriceObj != undefined) {
      setVUsdValue(
        BigNumber(tokenTotalValue!)
          .multipliedBy(BigNumber(vUsdToSGDPriceObj.price))
          .toNumber(),
      );
    }
  }, [tokenTotalValue]);

  return (
    <View style={styles.containerWrapper}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        {/* Header Left */}
        <View style={styles.headerLeft}>
          <TouchableOpacity
            onPress={() => {
              toggleModal();
            }}
            style={styles.userInfoContainer}>
            <Image
              source={require('../../../assets/images/default_avat.png')}
              style={styles.avatar}
            />
            <Text style={styles.userName}>{userInfo?.displayName}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (userProfile && [0, 3].indexOf(userProfile.verified) !== -1) {
                startKYC();
              }
            }}>
            <VerificationStatusTag verified={userProfile?.verified} />
          </TouchableOpacity>
        </View>

        {/* Header Right */}
        <View style={styles.headerRight}>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('CardList');
            }}
            style={styles.headerButton}>
            <CardIcon width={40} height={40} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            activeOpacity={0.5}
            onPress={() => {
              navigation.navigate('Notification');
            }}>
            <MessageIcon width={40} height={40} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Balance Section */}
      <View style={styles.balanceSection}>
        <Text style={styles.amount}>
          {showAsset
            ? formatCurrency(tokenTotalValue ?? 0, 'SGD', 2, 2)
            : '****'}
        </Text>
        <Text style={styles.vusd}>
          {showAsset ? '≈ ' : ''}{' '}
          {showAsset
            ? vUsdValue.toFixed(DefaultDecimals).replace(/\.?0+$/, '')
            : '****'}
          {showAsset ? ' vUSD' : ''}
        </Text>
        {/* transfer to react native */}
        <View style={styles.actionsRow}>
          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => {
              navigation.navigate('Bridge');
            }}>
            <Text style={styles.actionText}>Top-Up</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionItem} onPress={onSendPress}>
            <Text style={styles.actionText}>Send</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionItem} onPress={onReceivePress}>
            <Text style={styles.actionText}>Receive</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  containerWrapper: {
    width: '100%',
  },
  headerSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 8,
  },
  userName: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  headerButton: {
    borderRadius: 24,
    padding: 3,
    alignItems: 'center',
  },
  balanceSection: {
    width: '100%',
    backgroundColor: '#0C27F6',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    paddingBottom: 40,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  balanceLabel: {
    color: '#fff',
    fontWeight: 600,
    fontSize: 16,
  },
  assetText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 15,
  },
  amount: {
    color: '#fff',
    fontWeight: 600,
    fontSize: 34,
    marginBottom: 16,
  },
  vusd: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    marginBottom: 30,
  },
  actionsRow: {
    flexDirection: 'row',
    marginTop: 15,
    marginBottom: 15,
  },
  actionItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 50,
    paddingVertical: 10,
    paddingHorizontal: 20,
    alignItems: 'center',
    marginRight: 10,
  },
  actionText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 600,
  },
});

export default WalletHeader;
