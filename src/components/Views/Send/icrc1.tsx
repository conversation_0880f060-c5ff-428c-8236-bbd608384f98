import {
  primaryBackgroundColor2,
  secondaryColor,
  primaryBackgroundColor,
  primaryColor2,
  secondaryBackgroundColor,
  secondaryBorderColor,
  primaryColor,
  primaryColor3,
} from '../../../theme/default';
import {
  Text,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  Keyboard,
  Platform,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import tw from 'twrnc';
import {TokenLogo} from '../../UI/TokenLogo';
import {useTranslation} from 'react-i18next';
import {formatNumber, isValidPrincipalId, parseUnits} from '../../../utils';
import BigNumber from 'bignumber.js';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

import ScanIcon from '../../../assets/icons/scan.svg';
import {RootStackParamList} from '../../Nav/routes';
import {RouteProp, useIsFocused} from '@react-navigation/native';
import {useScannerProvider} from '../../../hooks/useScannerProvider';
import {useIcrc1TokenTransfer} from '../../../hooks/useICP/useIcrc1TokenTransfer';

import LoadingModal from '../../UI/Common/LoadingModal';
import StatusModal from '../../UI/Common/StatusModal';
import Toast from 'react-native-toast-message';
import Clipboard from '@react-native-clipboard/clipboard';
import CloseIcon from '../../../assets/icons/close-3.svg';

type SendIcrc1StackNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SendIcrc1'
>;
type SendIcrc1RouteProp = RouteProp<RootStackParamList, 'SendIcrc1'>;

const SendIcrc1 = ({
  route,
  navigation,
}: {
  route: SendIcrc1RouteProp;
  navigation: SendIcrc1StackNavigationProp;
}) => {
  const {tokenInfo} = route.params;
  const {t} = useTranslation();

  const isFocused = useIsFocused();
  const {codeResult, setCodeResult} = useScannerProvider();

  const [address, setAddress] = useState('');
  const [amount, setAmount] = useState('');
  const [isValidAmount, setIsValidAmount] = useState<boolean>();
  const [isValidAddress, setIsValidAddress] = useState<boolean>();
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [statusModalState, setStatusModalState] = useState<
    'normal' | 'success'
  >('normal');
  const [localLoading, setLocalLoading] = useState(false);

  console.log('showStatusModal', showStatusModal);
  console.log('statusModalState', statusModalState);

  const {loading, icrc1TokenTransfer} = useIcrc1TokenTransfer();

  const handleDone = () => {
    Keyboard.dismiss();
  };

  useEffect(() => {
    if (isFocused) {
      console.log('codeResult', codeResult);
      const _address = codeResult?.codeResult ?? '';
      if (_address != '') {
        setAddress(_address);
        setIsValidAddress(isValidPrincipalId(_address));
      }
    }
  }, [codeResult]);

  useEffect(() => {
    if (address == '') {
      setIsValidAddress(undefined);
    } else {
      setIsValidAddress(isValidPrincipalId(address));
    }
  }, [address]);

  useEffect(() => {
    navigation.setOptions({
      title: t('sendToken', {symbol: tokenInfo.symbol}),
    });
  }, [navigation, tokenInfo]);

  useEffect(() => {
    setAddress('');
    setAmount('');
    return () => {
      setCodeResult(undefined);
    };
  }, []);

  const stringifyWithBigInt = (data: unknown): string => {
    return JSON.stringify(data, (_key, value) =>
      typeof value === 'bigint' ? value.toString() : value,
    );
  };

  const handleTransfer = async () => {
    if (loading || localLoading) {
      return;
    }
    try {
      setLocalLoading(true);
      setShowStatusModal(true);
      setStatusModalState('normal');

      // setTimeout(() => {
      //   setStatusModalState('success');
      // }, 1000);

      // return;

      const amountUnits = BigInt(parseUnits(amount, tokenInfo.decimals));
      const result = await icrc1TokenTransfer(
        tokenInfo.address,
        address,
        amountUnits,
      );

      // 确保 loading 状态已经结束
      if (result && 'Ok' in result) {
        setLocalLoading(false);
        setStatusModalState('success');
      } else {
        setLocalLoading(false);
        setShowStatusModal(false);
        Toast.show({
          type: 'info',
          text1: t('send'),
          text2: stringifyWithBigInt(result),
          position: Platform.OS === 'android' ? 'top' : 'bottom',
        });
      }
    } catch (error: any) {
      setLocalLoading(false);
      setShowStatusModal(false);
      Toast.show({
        type: 'error',
        text1: t('send'),
        text2: error.toString(),
        position: Platform.OS === 'android' ? 'top' : 'bottom',
      });
    }
  };

  const handleAmountChange = (value: string) => {
    const maxDecimals = tokenInfo.decimals;
    const decimalRegex = new RegExp(`^[0-9]*\.?[0-9]{0,${maxDecimals}}$`);
    if (decimalRegex.test(value)) {
      setAmount(value);
      if (
        BigNumber(parseUnits(value, tokenInfo.decimals)).isGreaterThan(
          BigNumber(tokenInfo.amount!),
        )
      ) {
        setIsValidAmount(false);
      } else {
        setIsValidAmount(true);
      }
    }
  };

  return (
    <View style={[styles.container, {flex: 1}]}>
      <View style={{flexGrow: 1}}>
        <View
          style={[
            tw.style('flex-row items-center px-4 py-2 mb-4'),
            {
              backgroundColor: 'rgba(255, 255, 255, 0.10)',
              borderRadius: 20,
              minHeight: 44,
            },
          ]}>
          <Text style={tw.style('text-[14px] text-gray-400 mr-2')}>To:</Text>
          <TextInput
            style={[
              tw.style('flex-1 text-[16px] text-white'),
              {paddingVertical: 0},
            ]}
            placeholder={t('icrc1ReceiveAddressHint')}
            placeholderTextColor={primaryColor3}
            value={address}
            returnKeyType="done"
            onSubmitEditing={handleDone}
            onChangeText={setAddress}
          />
          {address.length === 0 ? (
            <TouchableOpacity
              onPress={async () => {
                const text = await Clipboard.getString();
                setAddress(text);
              }}
              style={[
                tw.style('ml-2'),
                {
                  backgroundColor: 'rgba(255,255,255,0.10)',
                  borderRadius: 20,
                  paddingHorizontal: 18,
                  paddingVertical: 6,
                },
              ]}>
              <Text style={{color: '#fff', fontWeight: '600', fontSize: 16}}>
                Paste
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              onPress={() => setAddress('')}
              style={tw.style('ml-2')}>
              <CloseIcon width={20} height={20} />
            </TouchableOpacity>
          )}
        </View>
        <View
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.10)',
            borderRadius: 20,
            padding: 16,
            marginBottom: 16,
          }}>
          {/* input */}
          <View
            style={tw.style(
              `flex flex-row items-center justify-between gap-2`,
            )}>
            <TextInput
              style={tw.style(`w-4/5 text-[${primaryColor2}] text-[16px]`)}
              placeholder={t('please enter amount')}
              keyboardType="numeric"
              returnKeyType="done"
              onSubmitEditing={handleDone}
              placeholderTextColor={secondaryColor}
              value={amount}
              onChangeText={handleAmountChange}
            />
            <View
              style={tw.style(
                `flex flex-row items-center justify-between gap-1`,
              )}>
              <Text style={tw.style(` text-[${primaryColor2}]`)}>
                {tokenInfo.symbol}
              </Text>
            </View>
          </View>
          {/* token info */}
          <View style={[styles.tokenInfo, {marginTop: 26}]}>
            <TokenLogo
              tokenName={tokenInfo.name}
              logoUri={tokenInfo.logo}
              size={40}
              borderRadius={40}
              backgroundColor="blue"
              textFontSize={12}
            />
            <View style={tw.style('ml-3 flex-1')}>
              <Text style={tw.style(`text-[16px] text-white font-bold`)}>
                {tokenInfo.symbol}
              </Text>
              <Text style={tw.style('text-[14px] text-gray-400 mt-1')}>
                {formatNumber(tokenInfo.uiAmount!, 2, 6)} {tokenInfo.symbol}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.tokenInfoMax}
              onPress={() => setAmount(tokenInfo.uiAmount!)}>
              <Text style={styles.tokenInfoText}>Use max</Text>
            </TouchableOpacity>
          </View>
        </View>
        {isValidAmount === false && (
          <View style={{marginTop: 8}}>
            <Text style={{color: 'red', fontSize: 14}}>
              {t('insufficientBalance')}
            </Text>
          </View>
        )}
      </View>
      <TouchableOpacity
        style={styles.continueButton}
        onPress={() => {
          if (isValidAddress && isValidAmount) {
            handleTransfer();
          }
        }}>
        <Text style={styles.continueButtonText}>Continue</Text>
      </TouchableOpacity>

      <StatusModal
        visible={showStatusModal}
        status={statusModalState}
        onClose={() => {
          setShowStatusModal(false);
        }}
        onSuccess={() => {
          setShowStatusModal(false);
          navigation.reset({
            index: 0,
            routes: [{name: 'Main'}],
          });
        }}
        message={`Send ${tokenInfo.symbol}`}
        successMessage={`${tokenInfo.symbol} Sent`}
        logo={
          <TokenLogo
            tokenName={tokenInfo.name}
            logoUri={tokenInfo.logo}
            size={60}
            borderRadius={30}
            textFontSize={14}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#000',
    minHeight: '100%',
    padding: 16,
    position: 'relative',
  },
  continueButton: {
    width: '100%',
    height: 56,
    backgroundColor: '#fff',
    borderRadius: 999,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  continueButtonText: {
    color: '#000',
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  horizontalLine: {
    borderBottomColor: '#4e4d4d',
    borderBottomWidth: 1,
    width: '100%',
    marginVertical: 10,
  },
  tokenInfo: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.10)',
    borderRadius: 16,
    padding: 16,
  },
  tokenInfoMax: {
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.10)',
    paddingHorizontal: 14,
    paddingVertical: 6,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tokenInfoText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});
export default SendIcrc1;
