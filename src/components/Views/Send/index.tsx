import {
  primaryBackgroundColor2,
  secondaryColor,
  primaryBackgroundColor,
  primaryColor2,
  secondaryBackgroundColor,
  secondaryBorderColor,
  primaryColor,
  primaryColor3,
} from '../../../theme/default';
import {
  Text,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import React, {useEffect, useMemo, useState, useRef} from 'react';
import tw from 'twrnc';
import {useTranslation} from 'react-i18next';
import {
  formatNumber,
  isValidSolanaAddress,
  parseUnits,
  toShortAddress,
} from '../../../utils';
import BigNumber from 'bignumber.js';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import Clipboard from '@react-native-clipboard/clipboard';
import ScanIcon from '../../../assets/icons/scan.svg';
import {RootStackParamList} from '../../Nav/routes';
import {RouteProp, useIsFocused} from '@react-navigation/native';
import {useScannerProvider} from '../../../hooks/useScannerProvider';
import {UserToken} from '../../../db/models/userTokenModel';
import CloseIcon from '../../../assets/icons/close-3.svg';
import AddressList, {AddressItem, RecentAddressItem} from './AddressList';
import storage from '../../../Storage';
import {formatDistanceToNow} from 'date-fns';
import AmountInput from '../../UI/Common/AmountInput';
import {TokenLogo} from '../../UI/TokenLogo';

type SendStackNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Send'
>;
type SendRouteProp = RouteProp<RootStackParamList, 'Send'>;

const Send = ({
  route,
  navigation,
}: {
  route: SendRouteProp;
  navigation: SendStackNavigationProp;
}) => {
  let tokenInfo: UserToken;
  if (route.params.tokenInfo) {
    tokenInfo = route.params.tokenInfo;
  } else {
    throw new Error('Token info is required');
  }

  const {t} = useTranslation();

  const isFocused = useIsFocused();
  const {codeResult, setCodeResult} = useScannerProvider();
  const [address, setAddress] = useState('');
  const [amount, setAmount] = useState('');
  const [isValidAmount, setIsValidAmount] = useState<boolean>(false);
  const [isValidAddress, setIsValidAddress] = useState<boolean>(false);
  const [recentAddresses, setRecentAddresses] = useState<RecentAddressItem[]>(
    [],
  );
  const errText = useMemo(() => {
    if (amount === '') {
      return 'Please enter amount';
    }
    if (isValidAddress === false) {
      return 'Invalid address';
    }
    if (isValidAmount === false) {
      return 'Insufficient balance';
    }
    return '';
  }, [isValidAddress, isValidAmount, amount]);
  const [initSubmit, setInitSubmit] = useState(false);
  const [isAddressInputFocus, setIsAddressInputFocus] = useState(false);
  const addressInputRef = useRef<TextInput>(null);

  const handleDone = () => {
    Keyboard.dismiss();
  };

  const onSelectAddress = (address: string) => {
    console.log('onSelectAddress', address);
    setAddress(address);
    saveAddressToStorage(address);
  };

  const saveAddressToStorage = (selectedAddress: string) => {
    try {
      console.log('selectedAddress', selectedAddress);
      const storageKey = 'recent_addresses';
      const existingAddressesJson = storage.getString(storageKey);
      let existingAddresses: Array<{address: string; timestamp: number}> = [];

      if (existingAddressesJson) {
        existingAddresses = JSON.parse(existingAddressesJson);
      }

      const filteredAddresses = existingAddresses.filter(
        item => item.address !== selectedAddress,
      );

      const newAddresses = [
        {address: selectedAddress, timestamp: Date.now()},
        ...filteredAddresses,
      ];

      console.log('newAddresses', newAddresses);
      const limitedAddresses = newAddresses.slice(0, 10);

      console.log('limitedAddresses', limitedAddresses);
      storage.set(storageKey, JSON.stringify(limitedAddresses));

      loadRecentAddresses();
    } catch (error) {
      console.error('Error saving address to storage:', error);
    }
  };

  const loadRecentAddresses = () => {
    try {
      const storageKey = 'recent_addresses';
      const addressesJson = storage.getString(storageKey);

      if (addressesJson) {
        const addresses: Array<{address: string; timestamp: number}> =
          JSON.parse(addressesJson);
        const formattedAddresses: RecentAddressItem[] = addresses.map(item => ({
          address: toShortAddress(item.address),
          fullAddress: item.address,
          usedAgo: formatDistanceToNow(item.timestamp, {addSuffix: true}),
        }));
        setRecentAddresses(formattedAddresses);
      }
    } catch (error) {
      console.error('Error loading recent addresses:', error);
    }
  };

  useEffect(() => {
    if (isFocused) {
      const _address = codeResult?.codeResult ?? '';
      if (_address != '') {
        setAddress(_address);
        setIsValidAddress(isValidSolanaAddress(_address));
      }
    }
  }, [codeResult]);

  useEffect(() => {
    if (address == '') {
      setIsValidAddress(false);
    } else {
      setIsValidAddress(isValidSolanaAddress(address));
    }
  }, [address]);

  useEffect(() => {
    navigation.setOptions({
      title: t('sendToken', {symbol: tokenInfo.symbol}),
    });
  }, [navigation, tokenInfo]);

  useEffect(() => {
    setAddress('');
    setAmount('');
    loadRecentAddresses();
    return () => {
      setCodeResult(undefined);
    };
  }, []);

  const transition = useMemo(() => {
    return {
      toAddress: address,
      amount: amount,
      token: tokenInfo,
    };
  }, [address, amount, navigation]);

  const handleAmountChange = (value: string) => {
    const maxDecimals = tokenInfo.decimals;
    const decimalRegex = new RegExp(`^[0-9]*\.?[0-9]{0,${maxDecimals}}$`);
    if (decimalRegex.test(value)) {
      setAmount(value);
      if (
        BigNumber(parseUnits(value, tokenInfo.decimals)).isGreaterThan(
          BigNumber(tokenInfo.amount!),
        )
      ) {
        setIsValidAmount(false);
      } else {
        setIsValidAmount(true);
      }
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={[styles.container, {flex: 1}]}>
        <View style={{flexGrow: 1}}>
          <View
            style={[
              tw.style('flex-row items-center px-4 py-2 mb-4'),
              {
                backgroundColor: 'rgba(255, 255, 255, 0.10)',
                borderRadius: 20,
                minHeight: 44,
              },
            ]}>
            <Text style={tw.style('text-[14px] text-gray-400 mr-2')}>To:</Text>
            <TextInput
              ref={addressInputRef}
              style={[
                tw.style('flex-1 text-[16px] text-white'),
                {paddingVertical: 0},
              ]}
              placeholder={t('receiveAddressHint')}
              placeholderTextColor={primaryColor3}
              value={address}
              returnKeyType="done"
              onSubmitEditing={handleDone}
              onChangeText={setAddress}
              onFocus={() => setIsAddressInputFocus(true)}
              onBlur={() => {
                // setTimeout(() => {
                //   setIsAddressInputFocus(false);
                // }, 200);
              }}
            />
            {address.length === 0 ? (
              <TouchableOpacity
                onPress={async () => {
                  const text = await Clipboard.getString();
                  setAddress(text);
                  setTimeout(() => {
                    addressInputRef.current?.focus();
                  }, 100);
                }}
                style={[
                  tw.style('ml-2'),
                  {
                    backgroundColor: 'rgba(255,255,255,0.10)',
                    borderRadius: 20,
                    paddingHorizontal: 18,
                    paddingVertical: 6,
                  },
                ]}>
                <Text style={{color: '#fff', fontWeight: '600', fontSize: 16}}>
                  Paste
                </Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={() => {
                  setAddress('');
                  setTimeout(() => {
                    addressInputRef.current?.focus();
                  }, 100);
                }}
                style={tw.style('ml-2')}>
                <CloseIcon width={20} height={20} />
              </TouchableOpacity>
            )}
          </View>
          {/* found match address */}
          {isValidAddress && isAddressInputFocus && (
            <AddressItem
              address={toShortAddress(address)}
              fullAddress={address}
              usedAgo="Found matching"
              onSelect={address => {
                onSelectAddress(address);
                setIsAddressInputFocus(false);
              }}
            />
          )}
          {/* Recently Used */}
          {isAddressInputFocus && (
            <AddressList
              recentAddresses={recentAddresses}
              onSelect={address => {
                onSelectAddress(address);
                setIsAddressInputFocus(false);
              }}
            />
          )}
          {/* input */}
          <AmountInput
            value={amount}
            onChangeText={handleAmountChange}
            placeholder={'0'}
            variant="send"
            validationError={initSubmit ? errText : null}
            onFocus={() => {
              setIsAddressInputFocus(false);
            }}
            onBlur={() => {
              Keyboard.dismiss();
            }}
          />
          {/* token info */}
          <View style={styles.tokenInfo}>
            <TokenLogo
              tokenName={tokenInfo.name}
              logoUri={tokenInfo.logo}
              size={40}
              borderRadius={40}
              backgroundColor="blue"
              textFontSize={12}
            />
            <View style={styles.tokenInfoText}>
              <Text style={styles.tokenSymbol}>{tokenInfo.symbol}</Text>
              <Text style={styles.tokenBalance}>
                {formatNumber(tokenInfo.uiAmount!, 2, 6)} {tokenInfo.symbol}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.useMaxButton}
              onPress={() => setAmount(tokenInfo.uiAmount!)}>
              <Text style={styles.useMaxText}>Use max</Text>
            </TouchableOpacity>
          </View>
          {/* {initSubmit && errText && (
          <View style={{marginTop: 8}}>
            <Text style={{color: 'red', fontSize: 14}}>{errText}</Text>
          </View>
        )} */}
        </View>
        <TouchableOpacity
          style={styles.continueButton}
          onPress={() => {
            setInitSubmit(true);
            if (isValidAddress && isValidAmount) {
              navigation.navigate('SendConfirm', {...transition});
            }
          }}>
          <Text style={styles.continueButtonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#000',
    minHeight: '100%',
    padding: 16,
    position: 'relative',
  },
  continueButton: {
    width: '100%',
    height: 56,
    backgroundColor: '#fff',
    borderRadius: 999,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  continueButtonText: {
    color: '#000',
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  tokenInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0F0F0F',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
  },
  tokenInfoText: {
    flex: 1,
    marginLeft: 12,
  },
  tokenSymbol: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  tokenBalance: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.6)',
    marginTop: 4,
  },
  useMaxButton: {
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.10)',
    paddingHorizontal: 14,
    paddingVertical: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  useMaxText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});
export default Send;
