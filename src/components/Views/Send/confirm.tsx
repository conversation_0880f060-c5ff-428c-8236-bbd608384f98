import {Text, TouchableOpacity, View, StyleSheet, Platform} from 'react-native';
import React, {useEffect, useState} from 'react';
import {TokenLogo} from '../../UI/TokenLogo';
import {useTranslation} from 'react-i18next';
import {
  calTokenValue,
  formatNumber,
  parseUnits,
  parseValue,
} from '../../../utils';
import {useTokenPriceProvider} from '../../../hooks/useTokenPriceProvider';
import {useSolanaTransaction} from '../../../hooks/useSolana/useSolanaTransaction';
import {Transaction} from '@solana/web3.js';
import {useAccountProvider} from '../../../hooks/useAccountProvider';
import {useSolanaClientProvider} from '../../../hooks/useSolana/useSolanaClientProvider';
import Toast from 'react-native-toast-message';
import StatusModal from '../../UI/Common/StatusModal';
import {toShortAddress} from '../../../utils';
import FaceIcon from '../../../assets/icons/face.svg';

const Confirm = ({route, navigation}: any) => {
  const {toAddress, amount, token} = route.params;

  const {t} = useTranslation();
  const {tokenPrices} = useTokenPriceProvider();
  const {userInfo} = useAccountProvider();
  const {solanaClient} = useSolanaClientProvider();

  const {createTransferSOLTx, createTransferSPLTx, signTx, sendRawTx} =
    useSolanaTransaction();

  const [rawTx, setRawTx] = useState<Transaction>();
  const [rawFee, setRawFee] = useState<string>('');
  const [feeValue, setFeeValue] = useState<string>('');
  const [loading, setLoading] = useState<boolean>();
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [statusModalState, setStatusModalState] = useState<
    'normal' | 'success'
  >('normal');

  const handleTokenValue = () => {
    const tokenPrice = tokenPrices.find(
      item => item.symbol.toLowerCase() == token.symbol.toLowerCase(),
    );
    let price = '0';
    if (tokenPrice) {
      price = tokenPrice.price;
    }
    return calTokenValue(amount, price, 6);
  };

  useEffect(() => {
    const handleFeeValue = async () => {
      if (rawFee == '') {
        setFeeValue('SGD 0');
      } else {
        const solananPrice = tokenPrices.find(
          item => item.symbol.toLowerCase() == 'sol',
        );
        if (solananPrice != undefined) {
          const feeValue = calTokenValue(
            parseValue(rawFee, 9),
            solananPrice.price,
          );
          setFeeValue(feeValue);
        } else {
          setFeeValue('SGD 0');
        }
      }
    };
    handleFeeValue();
  }, [rawFee]);

  useEffect(() => {
    const createTx = async () => {
      console.log('create tx....');
      let tx;
      if (token.type == 'native') {
        tx = await createTransferSOLTx(
          userInfo!.solanaAddress,
          toAddress,
          Number(amount),
        );
      } else {
        const amountUnits = BigInt(parseUnits(amount, token.decimals));
        tx = await createTransferSPLTx(
          userInfo!.solanaAddress,
          toAddress,
          token.address,
          amountUnits,
          token.decimals,
        );
      }

      console.log('tx', tx);
      setRawTx(tx);
      const fee = await tx.getEstimatedFee(solanaClient!);
      setRawFee(fee ? fee + '' : '');
    };
    createTx();
  }, [toAddress, amount, token]);

  const handelConfirm = async () => {
    if (rawTx !== undefined) {
      setShowStatusModal(true);
      setStatusModalState('normal');
      setLoading(true);
      try {
        const signedTx = await signTx(rawTx);
        await sendRawTx(signedTx);
        // todo 993
        // setTimeout(() => {
        setStatusModalState('success');
        setLoading(false);
        // }, 3000);
      } catch (error: any) {
        console.log('error', error);
        setShowStatusModal(false);
        setLoading(false);
        Toast.show({
          type: 'error',
          text1: t('send'),
          text2: error.toString(),
          position: Platform.OS === 'android' ? 'top' : 'bottom',
        });
      }
    }
  };

  console.log('token', token);

  return (
    <View style={styles.container}>
      {/* token info */}
      <View style={styles.tokenInfo}>
        <TokenLogo
          tokenName={token.name}
          logoUri={token.logo}
          size={60}
          borderRadius={30}
          textFontSize={14}
        />
        <Text
          style={[
            styles.tokenInfoTitle,
            {
              marginTop: 16,
            },
          ]}>
          Review & confirm sending
        </Text>
        <Text style={styles.tokenInfoTitle}>
          {token.symbol} to {toShortAddress(toAddress)}
        </Text>
      </View>
      {/* payment info */}
      <View style={styles.confirmInfo}>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('amount')}</Text>
          <Text style={styles.confirmInfoItemValue}>
            {amount} {token.symbol}
          </Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('value')}</Text>
          <Text style={styles.confirmInfoItemValue}>{handleTokenValue()}</Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('Wallet from')}</Text>
          <Text style={styles.confirmInfoItemValue}>Main Wallet</Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('Estimated time')}</Text>
          <Text style={styles.confirmInfoItemValue}>1 min</Text>
        </View>
        <View style={styles.confirmInfoItem}>
          <Text style={styles.confirmInfoItemTitle}>{t('Gas Fee')}</Text>
          <Text style={styles.confirmInfoItemValue}>
            {rawFee == '--'
              ? rawFee
              : `${
                  rawFee == ''
                    ? '--'
                    : formatNumber(parseValue(rawFee, 9), 2, 6)
                } SOL`}
            ≈ {feeValue}
          </Text>
        </View>
      </View>
      <View
        style={{
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: 64,
        }}>
        <TouchableOpacity style={styles.button} onPress={() => handelConfirm()}>
          <FaceIcon width={24} height={24} style={{marginRight: 8}} />
          <Text style={styles.buttonText}>Confirm transfer</Text>
        </TouchableOpacity>
      </View>

      <StatusModal
        visible={showStatusModal}
        status={statusModalState}
        onClose={() => {
          setShowStatusModal(false);
          setLoading(false);
        }}
        onSuccess={() => {
          setShowStatusModal(false);
          setLoading(false);
          navigation.reset({
            index: 0,
            routes: [{name: 'Main'}],
          });
        }}
        message={`Send ${token.symbol}`}
        successMessage={`${token.symbol} Sent`}
        logo={
          <TokenLogo
            tokenName={token.name}
            logoUri={token.logo}
            size={60}
            borderRadius={30}
            textFontSize={14}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 120,
  },
  tokenInfo: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    marginTop: 64,
    marginBottom: 64,
    width: '100%',
  },
  tokenInfoLogo: {
    marginBottom: 16,
  },
  tokenInfoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '95%',
    height: 48,
    backgroundColor: '#fff',
    borderRadius: 24,
  },
  buttonText: {
    color: '#000',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 17,
  },
  horizontalLine: {
    borderBottomColor: '#4e4d4d',
    borderBottomWidth: 1,
    width: '100%',
    marginVertical: 10,
  },
  confirmInfo: {
    width: '100%',
  },
  confirmInfoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 24,
  },
  confirmInfoItemTitle: {
    fontSize: 17,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.4)',
  },
  confirmInfoItemValue: {
    fontSize: 17,
    fontWeight: '500',
    color: '#fff',
  },
});
export default Confirm;
