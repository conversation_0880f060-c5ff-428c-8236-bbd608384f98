import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {},
  title: {
    color: '#888',
    fontSize: 14,
    marginBottom: 24,
  },
  addressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: 'rgba(255,255,255,0.08)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
  },
  contentContainer: {
    flex: 1,
  },
  addressText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '500',
  },
  usedAgoText: {
    color: 'rgba(255,255,255,0.4)',
    fontSize: 15,
    marginTop: 2,
  },
});
