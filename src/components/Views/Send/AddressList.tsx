import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import WalletIcon from '../../../assets/icons/wallet.svg';
import {styles} from './AddressList.style';

export interface RecentAddressItem {
  address: string;
  fullAddress: string;
  usedAgo: string;
}

const AddressItem = ({
  address,
  fullAddress,
  usedAgo,
  onSelect,
}: {
  address: string;
  fullAddress: string;
  usedAgo: string;
  onSelect: (address: string) => void;
}) => {
  return (
    <TouchableOpacity
      style={styles.addressItem}
      onPress={() => {
        onSelect(fullAddress);
      }}
      activeOpacity={0.7}>
      <View style={styles.iconContainer}>
        <WalletIcon width={24} height={24} />
      </View>
      <View style={styles.contentContainer}>
        <Text style={styles.addressText}>{address}</Text>
        <Text style={styles.usedAgoText}>Used {usedAgo}</Text>
      </View>
    </TouchableOpacity>
  );
};

const AddressList: React.FC<{
  recentAddresses: RecentAddressItem[];
  onSelect: (address: string) => void;
}> = ({recentAddresses, onSelect}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Recently Used</Text>
      {recentAddresses.map((item, idx) => (
        <AddressItem
          key={idx}
          address={item.address}
          fullAddress={item.fullAddress}
          usedAgo={item.usedAgo}
          onSelect={onSelect}
        />
      ))}
    </View>
  );
};

export default AddressList;

export {AddressItem};
