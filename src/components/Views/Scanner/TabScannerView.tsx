import React, {useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import QRScanner from '../../QRScanner';
import {BottomTabNavigationProp} from '@react-navigation/bottom-tabs';
import {BottomTabParamList} from '../../Nav/routes';
import {useScannerProvider} from '../../../hooks/useScannerProvider';

export default function TabScannerView() {
  const navigation =
    useNavigation<BottomTabNavigationProp<BottomTabParamList>>();
  const {setCodeResult} = useScannerProvider();
  const [isProcessingResult, setIsProcessingResult] = useState(false);

  const handleScanResult = (value: string | undefined | null) => {
    console.log('Tab Scanner result', value);

    // Prevent duplicate processing
    if (isProcessingResult) {
      console.log('Already processing scan result, ignoring');
      return;
    }

    // Prevent empty or invalid values
    if (!value || value.trim() === '') {
      console.log('Invalid scan result, ignoring');
      return;
    }

    if (value != null && value != undefined) {
      try {
        setIsProcessingResult(true);

        // Set scan result
        setCodeResult({
          codeResult: value!,
          timestamp: new Date().getTime(),
        });

        // In tab navigation, switch to Home tab after successful scan
        // Let Wallet component handle scan result
        navigation.navigate('Home');
        console.log('Scan successful, navigated to Home tab');
      } catch (error) {
        console.error('Error handling tab scan result:', error);
        // At least set scan result if navigation fails
        setCodeResult({
          codeResult: value!,
          timestamp: new Date().getTime(),
        });
      } finally {
        // Reset processing state after 2 seconds
        setTimeout(() => {
          setIsProcessingResult(false);
        }, 2000);
      }
    }
  };

  const handleClose = () => {
    try {
      // In tab navigation, switch to Home tab when closing scanner
      navigation.navigate('Home');
      console.log('Scanner closed, navigated to Home tab');
    } catch (error) {
      console.error('Error handling scanner close:', error);
    }
  };

  return (
    <QRScanner
      onScanResult={handleScanResult}
      hideCloseButton={false} // Show close button
      onClose={handleClose} // Add close handler
    />
  );
}
