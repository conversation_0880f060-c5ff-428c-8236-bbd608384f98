import React, {useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import QRScanner from '../../QRScanner';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import {useScannerProvider} from '../../../hooks/useScannerProvider';

export default function ScannerView() {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const {setCodeResult} = useScannerProvider();
  const [isProcessingResult, setIsProcessingResult] = useState(false);

  // TabBar component now auto-hides, no need to manually set
  // Keep this comment to explain hiding logic has moved to TabBar component

  const handleScanResult = (value: string | undefined | null) => {
    console.log('Scan code result', value);

    // Prevent duplicate processing
    if (isProcessingResult) {
      console.log('Already processing scan result, ignoring');
      return;
    }

    // Prevent empty or invalid values
    if (!value || value.trim() === '') {
      console.log('Invalid scan result, ignoring');
      return;
    }

    if (value != null && value != undefined) {
      try {
        setIsProcessingResult(true);

        // Set scan result
        setCodeResult({
          codeResult: value!,
          timestamp: new Date().getTime(),
        });

        // Check if pop operation can be executed (Stack navigation only)
        if (navigation.canGoBack()) {
          // Accessed via Stack navigation, return to previous page
          navigation.pop();
        } else {
          // Accessed via Tab navigation, no navigation action needed
          // Scan result is set, other components can listen and handle
          console.log('Scanner accessed via Tab navigation, result set');
        }
      } catch (error) {
        console.error('Error handling scan result:', error);
        // If navigation fails, at least set the scan result
        setCodeResult({
          codeResult: value!,
          timestamp: new Date().getTime(),
        });
      } finally {
        // Reset processing state after 2 seconds
        setTimeout(() => {
          setIsProcessingResult(false);
        }, 2000);
      }
    }
  };

  return <QRScanner onScanResult={handleScanResult}></QRScanner>;
}
