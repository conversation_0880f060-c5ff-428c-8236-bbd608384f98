import {
  primaryBackgroundColor,
  primaryBackgroundColor2,
  primaryColor,
  primaryColor2,
} from '../../../theme/default';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import tw from 'twrnc';

import { useNavigation } from '@react-navigation/native';

import ArrowDown from '../../../assets/icons/arrow_down.svg';
import { useSwitchTokenModalProvider } from '../../../hooks/useSwitchTokenModalProvider';
import { RootStackParamList } from '../../Nav/routes';
import { UpNetworkLogo } from '../../UI/UpNetworkLogo';
import { SwitchTokenModal } from '../../UI/SolanaWallet/Token/SwitchToken';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useSolanaOwnerTokenAccountsProvider } from '../../../hooks/useSolana/useSolanaOwnerTokenAccounts';
import { TokenLogo } from '../../UI/TokenLogo';
import { UserToken } from '../../../db/models/userTokenModel';
import TokenTxHistoryList from '../../UI/SolanaWallet/Token/TokenTxHistoryList';

const { height } = Dimensions.get('window');

const History = ({ route }: any) => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { toggleModal } = useSwitchTokenModalProvider();
  const { userTokens } = useSolanaOwnerTokenAccountsProvider();
  const [currentUserToken, setCurrentUserToken] = useState<UserToken>();
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    if (currentUserToken !== undefined) {
      navigation.setOptions({
        headerRight: () => (
          <TouchableOpacity
            onPress={toggleModal}
            style={tw.style(
              `flex flex-row items-center gap-2 bg-[${primaryBackgroundColor2}] p-2 rounded-xl`,
            )}>
            <TokenLogo
              tokenName={currentUserToken!.name}
              logoUri={currentUserToken!.logo}
              size={20}
              borderRadius={20}
              backgroundColor="blue"
              textFontSize={12}></TokenLogo>

            <Text style={tw.style(`text-[${primaryColor2}]`)}>
              {currentUserToken!.symbol}
            </Text>
            <ArrowDown color="#AAA" width={16} height={16} />
          </TouchableOpacity>
        ),
      });
    }
  }, [currentUserToken]);

  useEffect(() => {
    setLoading(true);
    const solToken = userTokens.find(item => item.symbol == 'SOL');
    console.log('solToken', solToken)
    setCurrentUserToken(solToken);

    setLoading(false);
  }, []);

  return (
    !loading && (
      <View
        style={{
          flex: 1,
          paddingHorizontal: 12,
          backgroundColor: primaryBackgroundColor,
        }}>
        <TokenTxHistoryList
          height={height - 220}
          tokenInfo={currentUserToken}></TokenTxHistoryList>

        <SwitchTokenModal
          tokenList={userTokens}
          selectToken={currentUserToken!}
          setSelectToken={value => {
            setCurrentUserToken(value);
          }}
        />

        <View
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            bottom: 48,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <UpNetworkLogo color={primaryColor}></UpNetworkLogo>
        </View>
      </View>
    )
  );
};

export default History;
