import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
  ScrollView,
  Platform,
} from 'react-native';
import type { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { useAuthProvider } from '../../../hooks/useAuthProvider';
import { useAccountProvider } from '../../../hooks/useAccountProvider';
import { BottomTabParamList, RootStackParamList } from '../../Nav/routes';
import {
  primaryBackgroundColor,
  primaryBackgroundColor2,
  primaryColor,
  primaryColor2,
  primaryHeaderBackgroundColor,
} from '../../../theme/default';
import { useTranslation } from 'react-i18next';
import { useMyProfile } from '../../../hooks/useMyProfile';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Config from 'react-native-config';
import { useKYC } from '../../../hooks/useKYC';
import { getNFT } from '../../../api/backend';
import { useInvite } from '../../../hooks/useInvite';
import OGIcon from '../../../assets/icons/og.svg';
import RightIcon from "../../../assets/icons/right_icon.svg"
import CurrencyIcon from '../../../assets/icons/currency.svg'
import CardIcon from "../../../assets/icons/credit_cards.svg"
import SecurityIcon from "../../../assets/icons/security.svg"
import { useAccounts } from '../../../hooks/useAccounts';
import { useIdentityProvider } from '../../../hooks/useSIWPIdentity';
import { VerificationStatusTag } from '../../UI/KYC';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';

type MyScreenNavigationProp = BottomTabNavigationProp<BottomTabParamList, 'More'>;

type Props = {
  navigation: MyScreenNavigationProp;
};

const { width } = Dimensions.get('window');

function Staking({ navigation }: Readonly<Props>) {
  const isFocused = useIsFocused();
  const { logout } = useAuthProvider();
  const [cardTotalNumber, setCardTotalNumber] = useState<Number>(0);
  const { t } = useTranslation();
  const { getCardTotalNumber } = useMyProfile();
  const { userProfile, userInfo, fetchUserProfile } = useAccountProvider();
  const { authToken } = useAuthProvider();
  const [nft, setNFT] = useState<number>(0);
  const { launchSNSMobileSDK, getToken } = useKYC();
  const { identityId } = useIdentityProvider();
  const { users } = useAccounts(identityId ?? '');
  const { inviteNumber } = useInvite();

  useEffect(() => {
    const callGetNFT = async () => {
      if (authToken) {
        const nft = await getNFT(authToken);
        if (nft?.data) {
          setNFT(nft.data);
        } else {
          setNFT(0);
        }
      }
    };
    callGetNFT();
  }, [authToken]);

  useEffect(() => {
    console.log('use userProfile', userProfile);
  }, [userProfile]);

  const startKYC = async () => {
    console.log('startKYC');
    const token = await getToken();
    console.log('token', token);
    if (token) {
      launchSNSMobileSDK(() => {
        fetchUserProfile(true);
      });
    }
  };

  const navigation2 =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  useEffect(() => {
    const getTotalNumber = async () => {
      const cardTotalNumber = await getCardTotalNumber();
      setCardTotalNumber(cardTotalNumber);
    };
    if (isFocused) {
      getTotalNumber();
    }
  }, [isFocused]);

  return (
    <ScrollView
      style={{
        flex: 1,
        backgroundColor: primaryBackgroundColor,
      }}
      contentContainerStyle={{
        justifyContent: 'flex-start',
        alignItems: 'center',
        paddingTop: 30,
        paddingBottom: 120, // 增加底部padding以避免被底部导航栏遮挡
      }}
      showsVerticalScrollIndicator={false}>
      <View style={styles.container}>
        <View
          style={styles.userInfoContainer}
        >
          <TouchableOpacity style={styles.userInfoContainerLeft}
            onPress={() => {
              // 复制 userInfo.solanaAddress
              Clipboard.setString(userInfo!.principalId);
              Toast.show({
                type: 'info',
                position: Platform.OS === 'android' ? 'top' : 'bottom',
                text1: t('copied'),
              });
            }}
          >
            <Image
              source={require('../../../assets/images/default_avat.png')}
              style={{
                width: 50,
                height: 50,
                marginRight: 12,
              }}
            />
            <View>
              <View style={styles.userNameContainer}>
                <Text style={{ color: primaryColor2, fontSize: 15 }}>
                  {userProfile?.verified === 2
                    ? `${userProfile.firstName} ${userProfile.lastName}`
                    : userInfo?.displayName || '--'}
                </Text>
              </View>
              <View style={styles.userBadgeWrapper}>
                {nft > 0 && (
                  <View style={styles.userBadgeItem}>
                    <OGIcon width={72} height={25} />
                  </View>
                )}
                <VerificationStatusTag verified={userProfile?.verified} />
              </View>
            </View>
          </TouchableOpacity>
          {userProfile && ([0, 3].indexOf(userProfile.verified ?? 0) !== -1) && (
            <TouchableOpacity style={styles.itemRightButton} onPress={() => {
              startKYC();
            }}>
              <Text style={{ color: '#fff', fontSize: 12, }}>
                Verify account
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* invite */}
      <View style={styles.inviteWrap}>
        <View
          style={styles.inviteContainer}
        >
          <Image
            source={require('../../../assets/images/invite_bg.png')}
            style={styles.inviteBg}></Image>
          <View style={styles.inviteContent}>
            <Text style={styles.commonText}>{inviteNumber}/5 received</Text>
          </View>
        </View>

        <TouchableOpacity style={styles.inviteButton} onPress={() => {
          navigation2.navigate('Invite');
        }}>
          <Text style={{ color: 'black', fontSize: 16, fontWeight: '600' }}>🎁</Text>
          <Text style={{ color: 'black', fontSize: 14, fontWeight: '600' }}>{t("Invite friends")}</Text>
        </TouchableOpacity>
      </View>


      <View style={{ ...styles.container }}>
        {/* settings */}
        <View style={{ ...styles.itemCommon }}>
          <Text style={{ ...styles.commonText, opacity: 0.4 }}>Settings</Text>
        </View>
        {/* list */}
        <TouchableOpacity
          onPress={() => {
            navigation2.navigate('ManageWallet');
          }}
          style={styles.listItem}>
          <CurrencyIcon style={styles.listIcon} />
          <View style={{ ...styles.itemCommon, ...styles.itemBorder, flex: 1 }}>

            <Text style={styles.labelText}>{t('mngWallets')}</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
              <Text style={{ ...styles.labelText, opacity: 0.4 }}>{users.length}</Text>
              <RightIcon width={16} height={16} color={primaryColor}></RightIcon>
            </View>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            navigation2.navigate('CardList');
          }}
          style={styles.listItem}>
          <CardIcon style={styles.listIcon} />
          <View style={{ ...styles.itemCommon, ...styles.itemBorder, flex: 1 }}>

            <Text style={styles.labelText}>{"Credit Cards"}</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
              <Text style={{ ...styles.labelText, opacity: 0.4 }}>{String(cardTotalNumber)}</Text>
              <RightIcon width={16} height={16} color={primaryColor}></RightIcon>
            </View>
          </View>
        </TouchableOpacity>
        <View
          style={styles.listItem}>
          <SecurityIcon style={styles.listIcon} />
          <View style={{ ...styles.itemCommon, ...styles.itemBorder, flex: 1 }}>

            <Text style={styles.labelText}>{"Solana"}</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
              <Text style={{ ...styles.labelText, opacity: 0.4 }}>{Config.APP_MODE == 'prod' ? 'Mainnet' : 'Devnet'}</Text>
              <RightIcon width={16} height={16} color={primaryColor}></RightIcon>
            </View>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => {
            navigation2.navigate('About');
          }}
          style={styles.listItem}>
          <CardIcon style={styles.listIcon} />
          <View style={{ ...styles.itemCommon, ...styles.itemBorder, flex: 1 }}>

            <Text style={styles.labelText}>{t('aboutUs')}</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
              <RightIcon width={16} height={16} color={primaryColor}></RightIcon>
            </View>
          </View>
        </TouchableOpacity>
      </View>



      <View style={styles.logoutButtonContainer}>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={async () => {
            await logout();
          }}>
          <Text style={{ fontSize: 15, color: primaryColor, fontWeight: '600' }}>
            {t('logoutTitle')}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

export default Staking;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingLeft: 16,
    justifyContent: 'flex-end',
  },
  itemCommon: {
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 60,
    flexDirection: 'row',
  },
  userInfoContainer: {
    width: width - 16,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 16,
    flexDirection: 'row',
  },
  itemBorder: {
    borderBottomColor: '#393939',
    borderBottomWidth: 1,
  },
  itemContent: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    height: '100%',
  },
  userInfoContainerLeft: {
    flexDirection: 'row',
    marginTop: 12,
    marginBottom: 12,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  userNameContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    height: 22,
    marginBottom: 4,
  },
  userBadgeWrapper: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    height: 25,
  },
  userBadgeItem: {
    marginRight: 12,
  },
  itemRightButton: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: "#282829",
    borderRadius: 28,
    paddingHorizontal: 14,
    paddingVertical: 8,
  },
  logoutButtonContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 34,
  },
  logoutButton: {
    width: width - 64,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    backgroundColor: primaryHeaderBackgroundColor,
    borderColor: primaryColor,
    borderRadius: 16,
    paddingVertical: 12,
  },
  inviteWrap: {
    width: width - 32,
    backgroundColor: primaryBackgroundColor2,
    borderRadius: 20,
    padding: 8,
    margin: 16
  },
  inviteContainer: {
    width: '100%',
    height: 160,
    position: 'relative',
  },
  inviteBg: {
    width: '100%',
    height: '100%',
    borderRadius: 12
  },
  inviteContent: {
    position: 'absolute',
    top: 120,
    left: 0,
    width: '100%',
    height: '100%',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    paddingLeft: 18,
    opacity: 0.8,
  },
  inviteButton: {
    width: 'auto',
    marginVertical: 16,
    marginHorizontal: 16,
    paddingHorizontal: 16,
    paddingVertical: 12, backgroundColor: 'white', borderRadius: 32, flexDirection: 'row', justifyContent: 'center', alignItems: 'center', gap: 12
  },
  commonText: {
    color: '#FAFAFA',
    fontSize: 12,
  },
  labelText: {
    color: primaryColor2, fontSize: 15
  },
  listItem: { flexDirection: 'row', alignItems: 'center', width: width - 32, },
  listIcon: { width: 20, height: 20, marginRight: 6 }
});
