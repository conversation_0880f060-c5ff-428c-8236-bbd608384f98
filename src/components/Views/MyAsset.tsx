import React, {useCallback, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  useWindowDimensions,
  TouchableOpacity,
} from 'react-native';
import {TabView, TabBar} from 'react-native-tab-view';
import {primaryBackgroundColor} from '../../theme/default';
import {TotalAssets} from '../UI/SolanaWallet/Token/TotalAssets';
import {WalletBar} from '../UI/SolanaWallet/WalletBar';
import {RootStackNavigationProp} from '../Nav/BottomTabs';
import {useNavigation} from '@react-navigation/native';
import {TokenList} from '../UI/SolanaWallet/Token/TokenList';
import EyeIcon from '../../assets/icons/eye.svg';
import EyeCloseIcon from '../../assets/icons/eye-close.svg';
import HistoryIcon from '../../assets/icons/history.svg';
import {useAssetVisible} from '../../hooks/useAssetVisible';

const TotalAssetsHeader = ({
  showAsset,
  setShowAsset,
  onHistoryPress,
}: {
  showAsset: boolean;
  setShowAsset: (v: boolean) => void;
  onHistoryPress: () => void;
}) => (
  <View
    style={{
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
      width: '100%',
      justifyContent: 'space-between',
    }}>
    <View style={{flexDirection: 'row', alignItems: 'center'}}>
      <Text style={{color: '#fff', fontSize: 18, marginRight: 8}}>
        Est. Total Value
      </Text>
      <TouchableOpacity onPress={() => setShowAsset(!showAsset)}>
        {showAsset ? (
          <EyeIcon width={20} height={20} />
        ) : (
          <EyeCloseIcon width={20} height={20} />
        )}
      </TouchableOpacity>
    </View>
    <TouchableOpacity onPress={onHistoryPress}>
      <HistoryIcon width={22} height={22} />
    </TouchableOpacity>
  </View>
);

const MyAsset = () => {
  const layout = useWindowDimensions();
  const [index, setIndex] = useState(0);
  const navigation = useNavigation<RootStackNavigationProp>();
  const [routes] = useState([
    {key: 'overview', title: 'Overview'},
    {key: 'spending', title: 'Spending'},
    {key: 'funding', title: 'Funding'},
  ]);
  const [showAsset, setShowAsset] = useAssetVisible();

  const renderTotalAssets = () => {
    return (
      <View style={styles.totalAssets}>
        <TotalAssetsHeader
          showAsset={showAsset}
          setShowAsset={setShowAsset}
          onHistoryPress={() => navigation.navigate('History')}
        />
        <TotalAssets userTokenList={[]} showAsset={showAsset} />
      </View>
    );
  };

  const TabContent = useCallback(
    ({route}: {route: {key: string}}) => {
      switch (route.key) {
        case 'overview':
          return (
            <>
              <WalletBar
                navigation={navigation}
                renderItemKey={['send', 'receive', 'bridge']}
                wrapperStyle={{
                  paddingHorizontal: 23,
                }}
              />
              <TokenList
                onItemPress={item =>
                  navigation.navigate('TokenDetail', {tokenInfo: item})
                }
              />
            </>
          );
        case 'spending':
          return (
            <>
              <WalletBar
                navigation={navigation}
                renderItemKey={['scan', 'NFC', 'bridge']}
                wrapperStyle={{
                  paddingHorizontal: 23,
                }}
              />
              <TokenList
                filterKey={['vUSD']}
                onItemPress={item =>
                  navigation.navigate('TokenDetail', {tokenInfo: item})
                }
              />
            </>
          );
        case 'funding':
          return (
            <>
              <WalletBar
                navigation={navigation}
                renderItemKey={['send', 'receive']}
                wrapperStyle={{
                  paddingHorizontal: 23,
                  justifyContent: 'flex-start',
                  gap: 40,
                }}
              />
              <TokenList
                onItemPress={item =>
                  navigation.navigate('TokenDetail', {tokenInfo: item})
                }
              />
            </>
          );
        default:
          return null;
      }
    },
    [navigation],
  );

  return (
    <View style={{flex: 1, backgroundColor: primaryBackgroundColor}}>
      <TabView
        navigationState={{index, routes}}
        renderScene={({route}) => (
          <View style={styles.scene}>
            {renderTotalAssets()}
            <TabContent route={route} />
          </View>
        )}
        onIndexChange={setIndex}
        initialLayout={{width: layout.width}}
        style={styles.tabView}
        renderTabBar={props => (
          <View
            style={{
              flexDirection: 'row',
              backgroundColor: primaryBackgroundColor,
            }}>
            {props.navigationState.routes.map((route, i) => {
              const focused = props.navigationState.index === i;
              return (
                <TouchableOpacity
                  key={route.key}
                  style={{
                    paddingHorizontal: 16,
                    paddingVertical: 6,
                    borderRadius: 12,
                    backgroundColor: focused ? '#232234' : 'transparent',
                    marginRight: 8,
                  }}
                  onPress={() => props.jumpTo(route.key)}>
                  <Text
                    style={{
                      color: focused ? '#A892F5' : '#fff',
                      fontSize: 18,
                      fontWeight: focused ? 'bold' : 'normal',
                    }}>
                    {route.title}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  tabView: {
    flex: 1,
    backgroundColor: primaryBackgroundColor,
  },
  totalAssets: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    padding: 16,
  },
  scene: {
    flex: 1,
    backgroundColor: primaryBackgroundColor,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  tabText: {
    color: '#fff',
    fontSize: 18,
  },
  ButtonContainer: {
    alignItems: 'center',
    flexDirection: 'column',
    paddingTop: 12,
    paddingBottom: 12,
  },
});

export default MyAsset;
