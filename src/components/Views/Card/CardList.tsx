import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import {CardList} from '../../UI/Card/CardList';
import {View} from 'react-native';
import {primaryBackgroundColor} from '../../../theme/default';

type CardListNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'CardList'
>;

type Props = {
  navigation: CardListNavigationProp;
};

export default function CardListView({navigation}: Readonly<Props>) {
  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: primaryBackgroundColor,
      }}>
      <CardList />
    </View>
  );
}
