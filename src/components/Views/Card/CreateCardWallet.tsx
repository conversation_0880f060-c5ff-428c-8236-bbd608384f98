import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import {useRoute, RouteProp} from '@react-navigation/native';

import CreateCardWallet from '../../UI/Card/CreateCardWallet';

type CreateCardWalletNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'CreateCardWallet'
>;

type Props = {
  navigation: CreateCardWalletNavigationProp;
};

export default function CreateCardWalletView({navigation}: Readonly<Props>) {
  const route = useRoute<RouteProp<RootStackParamList, 'CreateCardWallet'>>();
  const {cardNumber, cardTokenized} = route.params;

  return (
    <CreateCardWallet
      type="addCard"
      cardNumber={cardNumber}
      cardTokenized={cardTokenized}
    />
  );
}
