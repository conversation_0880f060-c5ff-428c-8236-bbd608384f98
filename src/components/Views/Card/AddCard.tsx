import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import AddCard from '../../UI/Card/AddCard';

type AddCardNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'AddCard'
>;

type Props = {
  navigation: AddCardNavigationProp;
};

export function AddCardView({navigation}: Readonly<Props>) {
  return (
    <AddCard
      onCheck={(isValid, cardNumber, cardTokenized) => {
        console.log(isValid, cardNumber, cardTokenized);
        if (isValid) {
          navigation.push('CreateCardWallet', {
            cardNumber: cardNumber.replaceAll(' ', ''),
            cardTokenized: cardTokenized ?? {},
          });
        }
      }}
    />
  );
}
