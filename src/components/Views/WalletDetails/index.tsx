import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {RouteProp} from '@react-navigation/native';
import {RootStackParamList} from '../../Nav/routes';
import {primaryBackgroundColor} from '../../../theme/default';
import AccountCard from '../../UI/SolanaWallet/Account/AccountCard';

type WalletDetialsScreenRouteProp = RouteProp<
  RootStackParamList,
  'WalletDetails'
>;

type Props = {
  route: WalletDetialsScreenRouteProp;
};

export function WalletDetailsView({route}: Readonly<Props>) {
  const {walletInfo} = route.params;
  return <AccountCard userInfo={walletInfo}></AccountCard>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    backgroundColor: primaryBackgroundColor,
  },
});
