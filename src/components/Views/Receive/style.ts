import {StyleSheet, Dimensions} from 'react-native';
import {
  primaryBackgroundColor,
  primaryColor,
  primaryColor2,
  primaryColor3,
  secondaryBorderColor,
  secondaryHoverBackgroundColor,
} from '../../../theme/default';

const {width} = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: primaryBackgroundColor,
  },
  qrCodeContainer: {
    width: width - 40,
    paddingVertical: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrCodeContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrCodeWrapper: {
    backgroundColor: '#fff',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    width: width - 64,
    height: width - 64,
  },
  qrCodeHint: {
    color: 'rgba(255, 255, 255, 0.4)',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginHorizontal: 70,
  },
  logoOverlay: {
    position: 'absolute',
    width: 45,
    height: 45,
    top: '50%',
    left: '50%',
    marginLeft: -22.5,
    marginTop: -22.5,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF',
    zIndex: 1000,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: primaryColor3,
    overflow: 'hidden',
  },
  logo: {
    width: 32,
    height: 32,
    borderRadius: 8,
    resizeMode: 'contain',
  },
  walletAddress: {
    textAlign: 'center',
    color: '#fff',
    marginTop: 12,
  },
  buttonContainer: {
    marginTop: 12,
    width: '100%',
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  button: {
    width: '48%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    backgroundColor: secondaryBorderColor,
    borderColor: primaryColor,
    borderRadius: 16,
  },
  buttonText: {
    fontSize: 14,
    color: primaryColor2,
    fontWeight: '600',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 32,
    height: 56,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  shareButtonText: {
    color: '#000',
    fontSize: 20,
    fontWeight: '500',
    marginLeft: 12,
  },
});
