import {primaryBackgroundColor, primaryColor} from '../../../theme/default';
import React from 'react';
import {View} from 'react-native';
import tw from 'twrnc';

import {UpNetworkLogo} from '../../UI/UpNetworkLogo';
import {TokenList} from '../../UI/SolanaWallet/Token/TokenList';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';

const Receive = ({route}: any) => {
  const {type} = route.params;
  console.log('type', type);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  return (
    <View
      style={tw.style(
        `bg-[${primaryBackgroundColor}] min-h-full pt-[20px] pl-[16px]`,
      )}>
      <View>
        <View>
          <TokenList
            onItemPress={item => {
              navigation.navigate('Receive', {tokenInfo: item});
            }}></TokenList>
        </View>
      </View>
      <View
        style={tw.style(
          `absolute bottom-[32px] w-full items-center justify-between`,
        )}>
        <UpNetworkLogo color={primaryColor}></UpNetworkLogo>
      </View>
    </View>
  );
};
export default Receive;
