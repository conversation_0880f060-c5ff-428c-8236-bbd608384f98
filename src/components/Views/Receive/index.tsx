import {RouteProp} from '@react-navigation/native';
import {RootStackParamList} from '../../Nav/routes';
import {useEffect, useRef, useState} from 'react';
import {
  Dimensions,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {useAccountProvider} from '../../../hooks/useAccountProvider';
import QRCode from 'react-qr-code';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import ViewShot, {captureRef} from 'react-native-view-shot';
import Share from 'react-native-share';
import {styles} from './style';
import CopyText from '../../UI/Common/CopyText';
import ShareIcon from '../../../assets/icons/share.svg';
import OntaIcon from '../../../assets/icons/onta-white.svg';

type ReceiveViewRouteProp = RouteProp<RootStackParamList, 'Receive'>;
type ReceiveViewNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Receive'
>;
type Props = {
  route: ReceiveViewRouteProp;
  navigation: ReceiveViewNavigationProp;
};

export default function ReceiveView({route, navigation}: Readonly<Props>) {
  const {tokenInfo} = route.params;
  const {t} = useTranslation();
  const {userInfo} = useAccountProvider();
  const {width} = Dimensions.get('window');

  const [qrCodeValue, setQrCodeValue] = useState<string | null>(null);
  const [qrBase64, setQrBase64] = useState<string | null>(null);
  const viewRef = useRef<ViewShot | null>(null);

  useEffect(() => {
    return () => {
      setQrCodeValue(null);
      setQrBase64(null);
    };
  }, []);

  useEffect(() => {
    if (userInfo) {
      if (tokenInfo?.type == 'icrc1') {
        setQrCodeValue(userInfo.principalId);
      } else {
        setQrCodeValue(userInfo.solanaAddress);
      }
    }
  }, [userInfo, tokenInfo]);

  useEffect(() => {
    navigation.setOptions({
      title:
        undefined === tokenInfo
          ? t('receive')
          : t('receiveToken', {symbol: tokenInfo.symbol}),
    });
  }, [navigation, tokenInfo]);

  const handleShare = async () => {
    if (qrBase64) {
      const shareOptions = {
        title: 'Share QR Code',
        url: `data:image/png;base64,${qrBase64}`,
        type: 'image/png',
      };

      Share.open(shareOptions)
        .then(res => console.log('Share response:', res))
        .catch(err => console.warn('Error sharing:', err));
    } else {
      if (viewRef.current) {
        try {
          const uri = await captureRef(viewRef, {
            format: 'png',
            result: 'base64',
          });

          setQrBase64(uri);

          const shareOptions = {
            title: 'Share QR Code',
            url: `data:image/png;base64,${uri}`,
            type: 'image/png',
          };

          Share.open(shareOptions)
            .then(res => console.log('Share response:', res))
            .catch(err => console.warn('Error sharing:', err));
        } catch (error) {
          console.warn('Failed to capture view:', error);
        }
      }
    }
  };

  return (
    <View style={styles.container}>
      <ViewShot ref={viewRef} style={styles.qrCodeContainer}>
        <View style={styles.qrCodeContent}>
          {qrCodeValue && (
            <View style={styles.qrCodeWrapper}>
              <QRCode
                size={width - 104}
                bgColor={'#fff'}
                fgColor={'#000'}
                value={qrCodeValue}
                viewBox={`0 0 225 225`}
              />
              <View style={styles.logoOverlay}>
                <OntaIcon width={45} height={45} />
              </View>
            </View>
          )}
          <CopyText
            value={
              tokenInfo?.type == 'icrc1'
                ? userInfo?.principalId ?? ''
                : userInfo?.solanaAddress ?? ''
            }
            containerStyle={{
              alignSelf: 'center',
              marginHorizontal: 48,
              marginVertical: 32,
            }}
            textStyle={{color: '#fff', fontSize: 18, fontWeight: '400'}}
          />
          <Text style={styles.qrCodeHint}>
            {tokenInfo?.type == 'icrc1' ? t('icpQrcodeHint') : t('qrcodeHint')}
          </Text>
        </View>
      </ViewShot>
      <View style={styles.buttonContainer}>
        <TouchableOpacity onPress={handleShare} style={styles.shareButton}>
          <ShareIcon width={24} height={24} />
          <Text style={styles.shareButtonText}>{t('shareTitle')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
