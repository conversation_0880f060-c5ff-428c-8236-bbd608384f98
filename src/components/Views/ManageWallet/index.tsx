import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../Nav/routes';
import {AccountList} from '../../UI/SolanaWallet/Account/AccountList';
import {Dimensions, StyleSheet, View} from 'react-native';
import {primaryBackgroundColor} from '../../../theme/default';
import {useNavigation} from '@react-navigation/native';

const {width} = Dimensions.get('window');

type ManageWalletScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Main'
>;

type Props = {
  navigation: ManageWalletScreenNavigationProp;
};

export default function ManageWalletView({navigation}: Readonly<Props>) {
  return (
    <View style={styles.container}>
      <AccountList
        onSelect={(e, value) => {
          navigation.navigate('WalletDetails', {walletInfo: value});
        }}
        type="ManageWallet"></AccountList>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: primaryBackgroundColor,
    paddingHorizontal: 12,
    paddingVertical: 16,
  },
});
