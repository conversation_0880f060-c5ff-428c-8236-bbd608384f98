import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import {pageUserMessageDto} from '../../../api/backend';
import {
  primaryBackgroundColor,
  primaryColor2,
  secondaryColor,
  primaryColor,
} from '../../../theme/default';

import {UpNetworkLogo} from '../../UI/UpNetworkLogo';
import tw from 'twrnc';
import {formatTimestamp} from '../../../utils';
export function NotificationView({navigation}: any) {
  // 状态管理
  const [data, setData] = useState([]); // 存储接口返回的数据
  const [loading, setLoading] = useState(false); // 加载状态
  const [error, setError] = useState(null); // 错误状态
  const [page, setPage] = useState(1);
  const [itemsPerPage, setitemsPerPage] = useState(20);
  const [hasMore, setHasMore] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const {height, width} = Dimensions.get('window');
  const f_height = height - 130;

  // 获取数据的函数
  const fetchData = async () => {
    if (loading || !hasMore) return;
    setLoading(true);
    try {
      const response = await pageUserMessageDto(page, itemsPerPage);
      setPage(page => page + 1);
      console.log('zzzzzzzzz');
      console.log(response);
      // 更新数据状态
      setData(response);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  const fetchMoreData = async () => {
    // 如果正在加载或没有更多数据，直接返回
    if ((data.length % itemsPerPage) % 2 == 0) {
      if (loading || !hasMore) return;

      setLoading(true);
      try {
        //console.log(data);
        console.log(page + '===');
        console.log(data.length + '===');
        //console.log(data);
        const response = await pageUserMessageDto(page, itemsPerPage);

        setData(prevData => [...prevData, ...response]);
        setPage(page => page + 1);
      } catch (error) {
        console.warn(error);
      } finally {
        setLoading(false);
      }
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchData().then(() => setInitialized(true));
  }, []);

  if (!initialized) {
    return <ActivityIndicator style={styles.container} />;
  }
  // 渲染列表项
  const renderItem = ({item}) => (
    <View style={styles.itemContainer}>
      <TouchableOpacity
        style={styles.itemContainerChild}
        onPress={() =>
          navigation.navigate('NotificationDetail', {id: item.idString})
        }>
        <View style={styles.infoList}>
          <View style={styles.infoLeft}>
            <View style={styles.ad}>
              <Text style={styles.ad_text}>AD</Text>
            </View>
            <View style={styles.info}>
              <Text style={styles.textinfo_1}>{item.title}</Text>
              <Text style={styles.textinfo_2}>{item.subjectMessage}</Text>
            </View>
          </View>

          <View>
            <Text style={styles.times}>
              {formatTimestamp(item.createTime, 'HH:mm')}
            </Text>
          </View>
        </View>
        <View style={styles.bottomLine}>
          <View style={styles.bottomLineChild}></View>
        </View>
      </TouchableOpacity>
    </View>
  );

  if (data.length <= 0) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: primaryBackgroundColor,
        }}>
        <Image
          source={require('../../../assets/notification/notification_icon.png')}
          style={styles.notification_icon}
        />
        <View
          style={tw.style(
            `absolute bottom-[32px] w-full items-center justify-between`,
          )}>
          <UpNetworkLogo color={primaryColor}></UpNetworkLogo>
        </View>
      </View>
    );
  } else {
    return (
      <View style={styles.container}>
        <View style={{height: f_height}}>
          <FlatList
            data={data}
            renderItem={renderItem}
            keyExtractor={item => item.id.toString()}
            onEndReached={fetchMoreData}
            onEndReachedThreshold={0.1}
            ListFooterComponent={loading ? <ActivityIndicator /> : null}
          />
        </View>

        <View
          style={tw.style(
            `absolute bottom-[32px] w-full items-center justify-between bg-[${primaryBackgroundColor}]`,
          )}>
          <UpNetworkLogo color={primaryColor}></UpNetworkLogo>
        </View>
      </View>
    );
  }
}

// 样式
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: primaryBackgroundColor,
  },

  itemContainer: {
    paddingLeft: 25,
    paddingRight: 25,
    marginBottom: 20,
  },
  itemContainerChild: {},
  bottomLine: {
    marginTop: 19,
    paddingLeft: 7,
  },
  bottomLineChild: {
    borderBottomWidth: 1,
    borderBottomColor: '#D8D8D8',
    opacity: 0.27,
  },
  notification_icon: {
    width: 240,
    height: 300,
  },
  infoList: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  infoLeft: {
    flex: 1,
    flexDirection: 'row',
  },
  info: {
    marginLeft: 20,
  },
  ad: {
    backgroundColor: '#7957B4',
    width: 43,
    height: 43,
    borderRadius: 22,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
  },
  ad_text: {
    fontSize: 16,
    color: '#fff',
  },
  textinfo_1: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  textinfo_2: {
    fontSize: 16,
    color: '#fff',
  },
  itemText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  times: {
    fontSize: 16,
    color: '#fff',
  },
});
