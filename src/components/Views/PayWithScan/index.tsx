import React, {useEffect, useState, useCallback} from 'react';
import {View, Text, StyleSheet, SafeAreaView, Image} from 'react-native';
import {useTranslation} from 'react-i18next';
import {
  primaryBackgroundColor2,
  primaryColor3,
  secondaryHoverBackgroundColor,
} from '../../../theme/default';
import QRCode from 'react-qr-code';
import {useAccountProvider} from '../../../hooks/useAccountProvider';
import {toShortAddress} from '../../../utils/index';
import {useAuthProvider} from '../../../hooks/useAuthProvider';
import {getPosScanOrder} from '../../../api/backend/kyc';
import {RootStackNavigationProp} from '../../Nav/BottomTabs';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {RootStackParamList} from '../../Nav/routes';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

const PayWithScanView = () => {
  const logo = require('../../../assets/appicon/<EMAIL>');
  const {userInfo} = useAccountProvider();
  const {t} = useTranslation();
  const [qrCodeValue, setQrCodeValue] = useState<string | null>(null);
  const [scanTime, setScanTime] = useState<number>(0);
  const {authToken} = useAuthProvider();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  useFocusEffect(
    useCallback(() => {
      if (userInfo && authToken) {
        const newScanTime = Date.now();
        setScanTime(newScanTime);
        setQrCodeValue(userInfo.solanaAddress);

        const refreshPosScanOrder = () => {
          getPosScanOrder(
            {
              limit: 1,
              payerWallet: userInfo.solanaAddress,
              timestamp: newScanTime,
            },
            authToken,
          ).then(result => {
            console.log('getPosScanOrder result', result);
            if (result && result.data && result.data.length > 0) {
              const newOrderData = result.data[0];
              if (newOrderData && newOrderData.orderId) {
                navigation.navigate('PaymentFromPosScan', {
                  orderId: newOrderData.orderId,
                  payerWallet: userInfo.solanaAddress,
                  timestamp: newScanTime,
                });
              }
            }
          });
        };

        refreshPosScanOrder();
        const interval = setInterval(refreshPosScanOrder, 1000);

        return () => {
          clearInterval(interval);
        };
      }
    }, [userInfo, authToken, navigation]),
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.qrWrapper}>
        <View style={styles.qrContainer}>
          {qrCodeValue && (
            <View>
              <QRCode
                size={225}
                style={{
                  height: 'auto',
                  maxWidth: '100%',
                  width: '100%',
                }}
                bgColor={secondaryHoverBackgroundColor}
                fgColor={'#FFF'}
                value={qrCodeValue}
                viewBox={`0 0 225 225`}
              />
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: '#fff',
                  position: 'absolute',
                  width: 40,
                  height: 40,
                  top: '50%',
                  left: '50%',
                  transform: [{translateX: -20}, {translateY: -20}],
                  zIndex: 1000,
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: primaryColor3,
                }}>
                <Image
                  source={logo}
                  style={{
                    width: 32,
                    height: 32,
                    borderRadius: 8,
                    resizeMode: 'contain',
                  }}></Image>
              </View>
            </View>
          )}
        </View>
        <Text style={styles.address}>{toShortAddress(qrCodeValue ?? '')}</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
  },
  headerTitle: {
    fontSize: 22,
    color: '#fff',
    fontWeight: 'bold',
  },
  qrWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrContainer: {
    padding: 18,
    backgroundColor: '#000',
    borderRadius: 24,
    marginBottom: 18,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  address: {
    color: '#aaa',
    fontSize: 16,
    marginTop: 8,
    letterSpacing: 1,
  },
});

export default PayWithScanView;
