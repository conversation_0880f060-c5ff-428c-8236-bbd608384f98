import {
  primaryBackgroundColor,
  secondaryHoverBackgroundColor,
  primaryColor2,
  primaryColor3,
  secondaryBackgroundColor,
} from '../../../theme/default';
import React from 'react';
import {View, Text} from 'react-native';
import tw from 'twrnc';

import type {BottomTabNavigationProp} from '@react-navigation/bottom-tabs';

import {BottomTabParamList} from '../../Nav/routes';
import {useTranslation} from 'react-i18next';

type MiningScreenNavigationProp = BottomTabNavigationProp<
  BottomTabParamList,
  'Mining'
>;

type Props = {
  navigation: MiningScreenNavigationProp;
};

function Mining({navigation}: Readonly<Props>) {
  const {t} = useTranslation();
  return (
    <View
      style={tw.style(
        `bg-[${primaryBackgroundColor}] min-h-full p-4 relative flex flex-col gap-3`,
      )}>
      <View
        style={tw.style(
          `border border-[${secondaryHoverBackgroundColor}] rounded-xl py-[20px] px-[16px] bg-[${secondaryBackgroundColor}]`,
        )}>
        <Text style={tw.style(`text-[${primaryColor2}] text-[14px] font-bold`)}>
          {t('miningStage1Title')}
        </Text>
        <Text
          style={tw.style(
            `text-[${primaryColor3}] text-[14px] mt-[10px] leading-5`,
          )}>
          {t('miningStage1Content')}
        </Text>
      </View>
      <View
        style={tw.style(
          `border border-[${secondaryHoverBackgroundColor}] rounded-xl py-[20px] px-[16px] bg-[${secondaryBackgroundColor}]`,
        )}>
        <Text style={tw.style(`text-[${primaryColor2}] text-[14px] font-bold`)}>
          {t('miningStage2Title')}
        </Text>
        <Text
          style={tw.style(
            `text-[${primaryColor3}] text-[14px] mt-[10px] leading-5`,
          )}>
          {t('miningStage2Content')}
        </Text>
      </View>
      <View
        style={tw.style(
          `border border-[${secondaryHoverBackgroundColor}] rounded-xl py-[20px] px-[16px] bg-[${secondaryBackgroundColor}]`,
        )}>
        <Text style={tw.style(`text-[${primaryColor2}] text-[16px] font-bold`)}>
          {t('miningStage3Title')}
        </Text>
        <Text
          style={tw.style(
            `text-[${primaryColor3}] text-[14px] mt-[10px] leading-5`,
          )}>
          {t('miningStage3Content')}
        </Text>
      </View>
    </View>
  );
}

export default Mining;
