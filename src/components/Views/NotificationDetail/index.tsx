import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Dimensions,
} from 'react-native';
import {
  primaryBackgroundColor,
  primaryColor2,
  secondaryColor,
  primaryColor,
} from '../../../theme/default';
import {useTranslation} from 'react-i18next';
import {userMessageDetail} from '../../../api/backend';
import {UpNetworkLogo} from '../../UI/UpNetworkLogo';
import tw from 'twrnc';
export function NotificationDetailView({route, navigation}: any) {
  const {t} = useTranslation();
  const {height, width} = Dimensions.get('window');
  const f_height = height - 130;
  const {id} = route.params;
  //const id = '1337455598845165568'; //route.params;
  const [data, setData] = useState([]); // 存储接口返回的数据
  const [loading, setLoading] = useState(false); // 加载状态
  console.log(333333);
  console.log(id);

  const fetchData = async () => {
    try {
      const response = await userMessageDetail(id);
      navigation.setOptions({
        title: response.title,
        headerTitle: response.title,
      });
      console.log(response);
      // 更新数据状态
      setData(response);
    } catch (err) {
      setLoading(false);
    }
  };
  // 组件挂载时获取数据
  useEffect(() => {
    fetchData();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <View style={{height: f_height}}>
        <ScrollView style={styles.scrollView}>
          <Text style={styles.text}>{data.content}</Text>
        </ScrollView>
      </View>

      <View
        style={tw.style(
          `absolute bottom-[32px] w-full items-center justify-between`,
        )}>
        <UpNetworkLogo color={primaryColor}></UpNetworkLogo>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: primaryBackgroundColor,
  },
  scrollView: {
    paddingLeft: 25,
    paddingRight: 25,
  },
  text: {
    fontSize: 18,
    color: '#fff',
  },
});
