import {
  StyleSheet,
  View,
  Image,
  Text,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {
  primaryBackgroundColor,
  primaryColor2,
  secondaryBackgroundColor,
  secondaryBorderColor,
} from '../../../theme/default';
import DeviceInfo from 'react-native-device-info';
import { useTranslation } from 'react-i18next';

export default function AboutView() {
  const appVersion = DeviceInfo.getVersion();
  const buildNumber = DeviceInfo.getBuildNumber();
  const { t } = useTranslation();
  const handleOpenUrl = async (url: string) => {
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.topContainer}>
        <Image
          source={require('../../../assets/appicon/app_icon.png')}
          style={styles.logo}></Image>
        <Text
          style={{
            fontSize: 15,
            color: primaryColor2,
            fontWeight: '600',
            marginVertical: 12,
          }}>
          Version {appVersion} (Build {buildNumber})
        </Text>
        <View>
          <Text style={styles.versionText}>Build 230009884</Text>
          <Text style={styles.versionText}>OntaPay.depricated.update3</Text>
        </View>

        <View style={{ marginTop: 100 }}>
          <Text style={styles.versionText}>{`Copyright ${new Date().getFullYear()} OntaNetwork
            All rights reserved`}</Text>
        </View>
      </View>
      <View style={styles.officialChannelContainer}>
        <TouchableOpacity
          style={styles.officialChannelItem}
          onPress={() => {
            handleOpenUrl('https://upnetwork.one');
          }}>
          <Text style={styles.officialChannelText}>{t('owTitle')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
    backgroundColor: primaryBackgroundColor,
  },
  topContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    borderRadius: 12,
    width: 68,
    height: 68,
    borderWidth: 1,
    borderColor: secondaryBorderColor,
  },
  officialChannelContainer: {
    marginTop: 48,
    width: '100%',

  },
  officialChannelItem: {
    borderRadius: 24,
    padding: 16,
    backgroundColor: secondaryBackgroundColor,
  },
  officialChannelText: {
    color: primaryColor2,
    fontSize: 15,
    fontWeight: '600',
  },
  versionText: {
    color: primaryColor2,
    fontSize: 11,
    opacity: 0.4,
    textAlign: 'center',
    lineHeight: 20
  },
});
