import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import NfcManager, {NfcEvents, TagEvent} from 'react-native-nfc-manager';
import {decodeNdefTextPayload} from '../utils';
import {Platform} from 'react-native';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import {getOrderById} from '../api/backend/kyc';
import {useAuthProvider} from './useAuthProvider';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../components/Nav/routes';

export type NdefOrderInfo = {
  timestamp: number;
  orderNo: string;
};

type NdefOrderContextType = {
  orderInfo: NdefOrderInfo | undefined;
  setOrderInfo: (value: NdefOrderInfo | undefined) => void;
  iosReadNdefOnce: (
    navigation: NativeStackNavigationProp<RootStackParamList>,
  ) => Promise<void>;
};

const NdefOrderContext = createContext<NdefOrderContextType | undefined>(
  undefined,
);

export const useNdefOrderProvider = (): NdefOrderContextType => {
  const context = useContext(NdefOrderContext);
  if (!context) {
    throw new Error(
      'useNdefOrderProvider must be used within an NdefOrderProvider',
    );
  }
  return context;
};

export function NdefOrderProvider({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const [orderInfo, setOrderInfo] = useState<NdefOrderInfo | undefined>();
  const {t} = useTranslation();
  const {authToken} = useAuthProvider();

  const initNfc = async () => {
    try {
      const isSupported = await NfcManager.isSupported();
      if (!isSupported) {
        console.warn('NFC is not supported on this device.');
        return;
      }

      const isEnabled = await NfcManager.isEnabled();
      if (!isEnabled) {
        console.warn('Please enable NFC in settings.');
        return;
      }

      await NfcManager.start();

      NfcManager.setEventListener(NfcEvents.DiscoverTag, (tag: TagEvent) => {
        console.log('NFC Tag Discovered:', tag, JSON.stringify(tag));
        handleNfcTag(tag);
      });

      await NfcManager.registerTagEvent();
    } catch (error) {
      console.warn('NFC Error:', error);
    }
  };

  const handleNfcTag = async (tag: TagEvent) => {
    if (tag.ndefMessage.length > 0) {
      const {text} = decodeNdefTextPayload(tag.ndefMessage[0].payload);
      console.log('text', text);
      setOrderInfo({
        timestamp: new Date().getTime(),
        orderNo: text,
      });
    }
  };

  useEffect(() => {
    if (Platform.OS === 'android') {
      initNfc();
      return () => {
        NfcManager.setEventListener(NfcEvents.DiscoverTag, null);
        NfcManager.unregisterTagEvent();
      };
    }
  }, []);

  const iosReadNdefOnce = (
    navigation: NativeStackNavigationProp<RootStackParamList>,
  ): Promise<void> => {
    const cleanUp = () => {
      NfcManager.setEventListener(NfcEvents.DiscoverTag, null);
      NfcManager.setEventListener(NfcEvents.SessionClosed, null);
    };

    return new Promise(async resolve => {
      const isSupported = await NfcManager.isSupported();
      if (!isSupported) {
        console.warn('NFC is not supported on this device.');

        Toast.show({
          type: 'info',
          text1: t('tapToPay'),
          text2: t('notSupportNFC'),
          position: Platform.OS === 'android' ? 'top' : 'bottom',
        });
        resolve();
      }

      const isEnabled = await NfcManager.isEnabled();
      if (!isEnabled) {
        console.warn('Please enable NFC in settings.');
        Toast.show({
          type: 'info',
          text1: t('tapToPay'),
          text2: t('nfcDisabled'),
          position: Platform.OS === 'android' ? 'top' : 'bottom',
        });
        resolve();
      }

      NfcManager.setEventListener(NfcEvents.DiscoverTag, (tag: TagEvent) => {
        if (tag.ndefMessage.length > 0) {
          const {text} = decodeNdefTextPayload(tag.ndefMessage[0].payload);
          console.log('text', text);
          setOrderInfo({
            timestamp: new Date().getTime(),
            orderNo: text,
          });
          if (!orderInfo || orderInfo?.orderNo) {
            Toast.show({
              type: 'info',
              text1: t('tapToPay'),
              text2: t('nfcDisabled'),
              position: Platform.OS === 'android' ? 'top' : 'bottom',
            });
            return;
          }
          if (!authToken) {
            Toast.show({
              type: 'info',
              text1: t('tapToPay'),
              text2: t('nfcDisabled'),
              position: Platform.OS === 'android' ? 'top' : 'bottom',
            });
            return;
          }
          getOrderById(authToken, orderInfo.orderNo).then(uniWebOrderInfo => {
            if (uniWebOrderInfo != null) {
              navigation.navigate('Payment', {orderId: orderInfo.orderNo});
              setOrderInfo(undefined);
            }
          });
        }
        resolve();
        NfcManager.unregisterTagEvent();
      });

      NfcManager.setEventListener(NfcEvents.SessionClosed, (error: any) => {
        if (error) {
          console.warn('error', error);
        }
        cleanUp();
        resolve();
      });
      NfcManager.registerTagEvent();
      // todo 993
      // setOrderInfo({
      //   timestamp: new Date().getTime(),
      //   orderNo: '1357396803448078336',
      // });
      // resolve();
    });
  };

  const contextValue = useMemo(
    () => ({
      orderInfo,
      setOrderInfo,
      iosReadNdefOnce,
    }),
    [orderInfo, setOrderInfo, iosReadNdefOnce],
  );
  return (
    <NdefOrderContext.Provider value={contextValue}>
      {children}
    </NdefOrderContext.Provider>
  );
}
