import { useEffect, useState } from "react";
import { useIdentityProvider } from "./useSIWPIdentity";
import { UserModel, User } from "../db/models/userModel";


export const useAccount = function () {
    const [userInfo, setUserInfo] = useState<User | null>(null);
    const { identityId } = useIdentityProvider();


    const getUserInfo = async (userName: string) => {
        const userModel = new UserModel();
        const _userInfo = await userModel.getByUserName(userName);
        setUserInfo(_userInfo);
    }

    useEffect(() => {
        if (typeof identityId !== "undefined") {
            getUserInfo(identityId);
        }
    }, [identityId]);


    return {
        userInfo
    }

}