import {useEffect, useState, useCallback} from 'react';
import {useAuth<PERSON>rovider} from './useAuthProvider';
import {getInviteCode, getInviteNumber} from '../api/backend';

export interface UseInviteReturn {
  inviteCode: string;
  inviteNumber: number;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for managing invite code and invite number
 * @returns {UseInviteReturn} Object containing invite data and methods
 */
export const useInvite = (): UseInviteReturn => {
  const {authToken} = useAuthProvider();
  const [inviteCode, setInviteCode] = useState<string>('');
  const [inviteNumber, setInviteNumber] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchInviteCode = useCallback(async (): Promise<string | null> => {
    if (!authToken) {
      return null;
    }

    try {
      const result = await getInviteCode(authToken);
      if (result?.code) {
        setInviteCode(result.code);
        return result.code;
      }
      return null;
    } catch (err) {
      console.warn('Failed to fetch invite code:', err);
      setError('Failed to fetch invite code');
      return null;
    }
  }, [authToken]);

  const fetchInviteNumber = useCallback(
    async (code: string): Promise<void> => {
      if (!code) {
        return;
      }

      try {
        const result = await getInviteNumber({code});
        if (result?.number !== undefined) {
          setInviteNumber(result.number);
        }
      } catch (err) {
        console.warn('Failed to fetch invite number:', err);
        setError('Failed to fetch invite number');
      }
    },
    [],
  );

  const refetch = useCallback(async (): Promise<void> => {
    if (!authToken) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // First fetch invite code
      const code = await fetchInviteCode();
      
      // Then fetch invite number if we have a code
      if (code) {
        await fetchInviteNumber(code);
      }
    } catch (err) {
      console.warn('Failed to refetch invite data:', err);
      setError('Failed to refetch invite data');
    } finally {
      setIsLoading(false);
    }
  }, [authToken, fetchInviteCode, fetchInviteNumber]);

  // Initial fetch when authToken is available
  useEffect(() => {
    if (authToken) {
      refetch();
    }
  }, [authToken, refetch]);

  return {
    inviteCode,
    inviteNumber,
    isLoading,
    error,
    refetch,
  };
};
