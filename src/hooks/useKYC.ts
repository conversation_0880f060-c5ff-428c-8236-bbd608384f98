import {useState} from 'react';
import SNSMobileSDK from '@sumsub/react-native-mobilesdk-module';
import {getSumsubToken} from '../api/backend/kyc';
import {useAuthProvider} from '../hooks/useAuthProvider';
import {useAccount} from '../hooks/useAccount';

export function useKYC() {
  const {authToken} = useAuthProvider();
  const {userInfo} = useAccount();

  const [accessToken, setToken] = useState<string>(
    '_act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC0yY2RiZTE4OS1kNTc5LTQ4ZjUtODBhMi1kMDg4MmU3NTRiYTMtdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2',
  );

  const getToken = async () => {
    if (!authToken) {
      console.warn('get token failed, authToken is null');
      return '';
    }
    if (!userInfo) {
      console.warn('get token failed, userInfo is null');
      return '';
    }
    const token = await getSumsubToken(authToken);
    console.log('get sumsub token', token);
    setToken(token);
    return token;
  };

  let launchSNSMobileSDK = (callback?: () => void) => {
    // From your backend get an access token for the applicant to be verified.
    // The token must be generated with `levelName` and `userId`,
    // where `levelName` is the name of a level configured in your dashboard.
    //
    // The sdk will work in the production or in the sandbox environment
    // depend on which one the `accessToken` has been generated on.
    //

    let snsMobileSDK = SNSMobileSDK.init(accessToken, () => {
      // this is a token expiration handler, will be called if the provided token is invalid or got expired
      // call your backend to fetch a new access token (this is just an example)
      return getToken();
    })
      .withHandlers({
        // Optional callbacks you can use to get notified of the corresponding events
        onStatusChanged: event => {
          console.log(
            'onStatusChanged: [' +
              event.prevStatus +
              '] => [' +
              event.newStatus +
              ']',
          );
        },
        onLog: event => {
          //   console.log('onLog: [Idensic] ' + event.message);
        },
      })
      .withDebug(true)
      .withLocale('en') // Optional, for cases when you need to override the system locale
      .build();

    snsMobileSDK
      .launch()
      .then(result => {
        console.log('SumSub SDK State: ' + JSON.stringify(result));
        if (result.success) {
          console.log('SumSub SDK State: success, will update user status');
          callback?.();
        }
      })
      .catch(err => {
        console.log('SumSub SDK Error: ' + JSON.stringify(err));
      });
  };

  return {
    getToken,
    launchSNSMobileSDK,
  };
}
