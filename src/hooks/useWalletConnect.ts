import {useSet<PERSON><PERSON>} from 'jotai';
import {update<PERSON>hain<PERSON>ata<PERSON><PERSON>} from '../store/bridgeAtom';
import {useCallback} from 'react';
import {useICBridge} from './useIcBridge';
import {useSolanaBridge} from './useSolanaBridge';
import {User} from '../db/models/userModel';
import {formatUnits} from '../utils';
export function useWalletConnect(user: User) {
  const updateChainData = useSetAtom(updateChainDataAtom);
  const decimals = 6;
  const {icService} = useICBridge(user);
  const {solanaService} = useSolanaBridge(user);

  const connectWallet = useCallback(
    async (chainId: string): Promise<string> => {
      if (!user) {
        return '';
      }

      let address = '';

      switch (chainId) {
        case 'icp':
          // 获取ICP地址
          address = user.principalId || '';
          break;

        case 'solana':
          // 获取Solana地址
          address = user.solanaAddress || '';
          break;

        default:
          throw new Error(`Unsupported chain: ${chainId}`);
      }

      if (address) {
        // 更新链状态
        updateChainData({
          chainId,
          data: {
            connected: true,
            address,
          },
        });

        // 连接后立即查询余额
        await getBalance(chainId);
      }

      return address;
    },
    [user, updateChainData, icService, solanaService],
  );

  const getBalance = useCallback(
    async (chainId: string): Promise<string> => {
      const symbol = chainId === 'icp' ? 'vUSD' : 'USDC';

      try {
        let balance = '0';

        // 使用服务类的余额查询方法
        switch (chainId) {
          case 'icp':
            // 使用IC桥接服务查询vUSD余额
            if (icService) {
              const rawBalance = await icService.getBalance();
              balance = rawBalance
                ? formatUnits(rawBalance.toString(), decimals)
                : '0';
            }
            break;

          case 'solana':
            // 使用Solana桥接服务查询USDC余额
            if (solanaService) {
              const rawBalance = await solanaService.getTokenBalance();
              balance = rawBalance
                ? formatUnits(rawBalance.toString(), decimals)
                : '0';
              // 同时获取SOL余额用于支付gas费
              try {
                const solBalance = await solanaService.getWalletBalance();
                // 更新SOL余额
                updateChainData({
                  chainId,
                  data: {
                    solBalance: solBalance,
                  },
                });
              } catch (error) {
                console.error('Error fetching SOL balance:', error);
              }
            }
            break;

          default:
            return `0 ${symbol}`;
        }
        const formattedBalance = `${balance} ${symbol}`;

        // 更新链余额
        updateChainData({
          chainId,
          data: {
            balance: formattedBalance,
          },
        });

        return formattedBalance;
      } catch (error) {
        console.error(`Error fetching balance for ${chainId}:`, error);

        // 更新错误状态
        updateChainData({
          chainId,
          data: {
            error: `Failed to fetch balance: ${
              error instanceof Error ? error.message : String(error)
            }`,
          },
        });

        return 'Error';
      }
    },
    [icService, solanaService, updateChainData, user],
  );

  return {
    connectWallet,
    getBalance,
    icService,
    solanaService,
  };
}
