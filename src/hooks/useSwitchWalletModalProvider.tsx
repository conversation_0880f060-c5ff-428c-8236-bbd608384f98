import { createContext, ReactNode, useContext, useMemo, useState } from "react";

interface SwitchWalletModalContextType {
    toggleModal: () => void;
    isSwitchWalletModalVisible: boolean;
}

const SwitchWalletModalContext = createContext<SwitchWalletModalContextType | undefined>(undefined);

export const useSwitchWalletModalProvider = (): SwitchWalletModalContextType => {

    const context = useContext(SwitchWalletModalContext);
    if (!context) {
        throw new Error('useSwitchWalletModalProvider must be used within an SwitchWalletModalProvider');
    }
    return context;
}

export function SwitchWalletModalProvider({ children }: Readonly<{
    children: ReactNode
}>) {

    const [isSwitchWalletModalVisible, setIsSwitchWalletModalVisible] = useState<boolean>(false);
    const toggleModal = () => {
        setIsSwitchWalletModalVisible(!isSwitchWalletModalVisible);
    }

    const contextValue = useMemo(() => ({
        isSwitchWalletModalVisible,
        toggleModal,
    }), [isSwitchWalletModalVisible, toggleModal]);

    return (
        <SwitchWalletModalContext.Provider value={contextValue}>
            {children}
        </SwitchWalletModalContext.Provider>
    );
}