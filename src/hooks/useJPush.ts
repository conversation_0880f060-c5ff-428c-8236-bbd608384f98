import {useEffect} from 'react';
import {PermissionsAndroid, Platform} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {LogLevel, OneSignal} from 'react-native-onesignal';
import {saveAppUserDto} from '../api/backend';
import { MMKV } from 'react-native-mmkv'
export const storage = new MMKV();
export function useJPush() {
  const navigation = useNavigation();

  useEffect(() => {
    checkNotificationPermission();
  }, []);

  //初始化
  const useJPushInit = async (principalId: string) => {
    OneSignal.Debug.setLogLevel(LogLevel.Verbose);
    OneSignal.initialize('************************************');
    // await OneSignal.Notifications.requestPermission(true); //zheli
    console.log('principalId: ', principalId);
    //注册用户 id

    const saveAppUserDtoResult = await saveAppUserDto(principalId);
    storage.set('appUserId', saveAppUserDtoResult.id);
    //console.log(saveAppUserDtoResult);
    //console.log(saveAppUserDtoResult.id)
    OneSignal.login(principalId);
    OneSignal.Notifications.addEventListener('click', notification => {
      console.log('notification', JSON.stringify(notification));
      if (
        notification.notification.additionalData &&
        notification.notification.additionalData?.url //这里应该是个类型的错误
      ) {
        setTimeout(() => {
          navigation.navigate('NotificationDetail', {
            id: notification.notification.additionalData?.url,
          }); //'162944'
        }, 500);
      }
    });
    OneSignal.User.pushSubscription.getTokenAsync().then(token => {
      console.log('token', token);
    });
    OneSignal.User.getExternalId().then(externalId => {
      console.log('externalId', externalId);
    });
  };

  const checkNotificationPermission = async () => {
    try {
      // Android API 级别 33 (Android 13) 及以上需要请求权限
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        const permission = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        return permission === PermissionsAndroid.RESULTS.GRANTED;
      }
      // 较低版本的 Android 默认授予权限
      return true;
    } catch (error) {
      console.warn('权限检查失败:', error);
      return false;
    }
  };

  return {
    useJPushInit,
  };
}
