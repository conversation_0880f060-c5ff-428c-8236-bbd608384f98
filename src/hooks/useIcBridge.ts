import {useIdentity} from './useIdentity';
import {ICBridgeService} from '../utils/ICBridgeService';
import {useEffect, useState} from 'react';
import {HttpAgent} from '@dfinity/agent';
import isoFetch from 'isomorphic-fetch';
import {updateChainData<PERSON>tom} from '../store/bridgeAtom';
import {useSetAtom} from 'jotai';
import {formatUnits} from '../utils';
import Config from 'react-native-config';
import {User} from '../db/models/userModel';

export const useICBridge = (user: User) => {
  const [icService, setIcService] = useState<ICBridgeService | null>(null);
  const updateChainData = useSetAtom(updateChainDataAtom);
  const {identityStore} = useIdentity();
  useEffect(() => {
    const init = async () => {
      if (!identityStore || !user || !user.principalId) {
        return;
      }
      const agent = new HttpAgent({
        host: Config.VITE_APP_CANISTER_HOST,
        fetch: isoFetch,
        identity: identityStore.delegationIdentity,
      });
      const _icService = ICBridgeService.getInstance(agent, user.principalId);
      setIcService(_icService);
      try {
        const balance = (await _icService.getBalance()) as number;
        const formattedBalance = formatUnits(balance.toString(), 6);

        updateChainData({
          chainId: 'icp',
          data: {
            balance: `${formattedBalance} vUSD`,
          },
        });
      } catch (e) {
        console.warn('Failed to get initial ICP balance:', e);
      }
    };

    init();
  }, [user, updateChainData, identityStore]);

  return {
    icService,
  };
};
