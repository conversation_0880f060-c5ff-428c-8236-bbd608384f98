import {useAtom, useAtomValue, useSet<PERSON>tom} from 'jotai';
import {useCallback} from 'react';
import {
  bridgeStateAtom,
  fromChainDataAtom,
  toChainDataAtom,
  fromChainIdAtom,
  toChainIdAtom,
  bridgeAmountAtom,
  swapChainsAtom,
  resetBridgeAtom,
  setBridgeErrorAtom,
  setBridgeProcessingAtom,
  setTxHashAtom,
  updateChainDataAtom,
  fromChainInfoAtom,
  toChainInfoAtom,
} from '../store/bridgeAtom';

/**
 * A hook that centralizes bridge state management
 */
export function useBridgeState() {
  // Read the main state
  const bridgeState = useAtomValue(bridgeStateAtom);

  // Chain info atoms
  const fromChainInfo = useAtomValue(fromChainInfoAtom);
  const toChainInfo = useAtomValue(toChainInfoAtom);

  // Chain data atoms with wallets, balances
  const [fromChainData, setFromChainData] = useAtom(fromChainDataAtom);
  const [toChainData, setToChainData] = useAtom(toChainDataAtom);

  // Chain selection atoms
  const [fromChainId, setFromChainId] = useAtom(fromChainIdAtom);
  const [toChainId, setToChainId] = useAtom(toChainIdAtom);

  // Bridge amount atom
  const [bridgeAmount, setBridgeAmount] = useAtom(bridgeAmountAtom);

  // Action atoms
  const swapChains = useSetAtom(swapChainsAtom);
  const resetBridge = useSetAtom(resetBridgeAtom);
  const setError = useSetAtom(setBridgeErrorAtom);
  const setProcessing = useSetAtom(setBridgeProcessingAtom);
  const setTxHash = useSetAtom(setTxHashAtom);
  const updateChainData = useSetAtom(updateChainDataAtom);

  // Validate the bridge amount
  const validateAmount = useCallback(
    (amount: string): string | null => {
      if (!amount) return 'Amount is required';

      const numValue = parseFloat(amount);
      if (isNaN(numValue)) return 'Invalid amount';
      if (numValue <= 0) return 'Amount must be greater than 0';
      if (numValue > 1000000) return 'Amount must be less than 1,000,000';
      if (numValue < 0.000001) return 'Amount must be greater than 0.000001';

      console.log('fromChainData.balance', fromChainData.balance);
      console.log('numValue', numValue);
      console.log(
        'fromChainData.balance > numValue',
        fromChainData.balance && numValue > parseFloat(fromChainData.balance),
      );
      // Additional validation could be added here (e.g., check against balance)
      if (
        fromChainData.balance &&
        numValue > parseFloat(fromChainData.balance)
      ) {
        return 'Insufficient balance';
      }

      return null;
    },
    [fromChainData.balance],
  );

  // Update wallet connection status
  const setWalletConnected = useCallback(
    (chainId: string, connected: boolean) => {
      updateChainData({
        chainId,
        data: {connected},
      });
    },
    [updateChainData],
  );

  // Update wallet address
  const setWalletAddress = useCallback(
    (chainId: string, address: string | null) => {
      updateChainData({
        chainId,
        data: {address},
      });
    },
    [updateChainData],
  );

  // Update chain balance
  const updateBalance = useCallback(
    (chainId: string, balance: string | null) => {
      updateChainData({
        chainId,
        data: {balance},
      });
    },
    [updateChainData],
  );

  // Set chain error
  const setChainError = useCallback(
    (chainId: string, error: string | null) => {
      updateChainData({
        chainId,
        data: {error},
      });
    },
    [updateChainData],
  );

  return {
    // State
    bridgeState,
    fromChainInfo,
    toChainInfo,
    fromChainData,
    toChainData,
    fromChainId,
    toChainId,
    bridgeAmount,
    isProcessing: bridgeState.processing,
    error: bridgeState.error,
    txHash: bridgeState.txHash,

    // Actions
    setFromChainId,
    setToChainId,
    setBridgeAmount,
    swapChains,
    resetBridge,
    setError,
    setProcessing,
    setTxHash,

    // Chain data actions
    setFromChainData,
    setToChainData,
    updateChainData,
    setWalletConnected,
    setWalletAddress,
    updateBalance,
    setChainError,

    // Helpers
    validateAmount,
  };
}
