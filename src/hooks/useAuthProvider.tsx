import {
  createContext,
  useContext,
  ReactNode,
  useEffect,
  useMemo,
  useCallback,
  useState,
} from 'react';
import { useIdentityProvider } from './useSIWPIdentity';
import { useActorIdentity } from './useActorIdentity';
import { AppState } from 'react-native';
import { SIGNATURE_STORAGE_KEY } from '../constants';
import { AuthSig } from '../types/user';
import Clipboard from '@react-native-clipboard/clipboard';
import { MMKV } from 'react-native-mmkv';
import { bindInviteCode, getInviteCode, verifyPrincipal } from '../api/backend';
import { useAccounts } from './useAccounts';
import { UserModel } from '../db/models/userModel';
export const storage = new MMKV();

type AuthContextType = {
  isLoggedIn: boolean | undefined;
  authToken: string | undefined;
  getAuthSig: (newIdentityId?: string) => Promise<AuthSig | undefined>;
  logout: () => Promise<void>;
  fetchAndBindInviteCode: () => Promise<void>;
  shouldInitBindInviteCode: boolean;
  setShouldInitBindInviteCode: (shouldInitBindInviteCode: boolean) => void;
};

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined,
);

export const useAuthProvider = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthProvider must be used within an AuthProvider');
  }
  return context;
};

type AuthProviderProps = {
  children: ReactNode;
};

export function AuthProvider({ children }: Readonly<AuthProviderProps>) {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>();
  const [authSig, setAuthSig] = useState<AuthSig>();
  const [authToken, setAuthToken] = useState<string>();
  const { identityId, identityActor, isInitializing } = useIdentityProvider();
  const { greet, signPrincipal } = useActorIdentity();
  const [appState, setAppState] = useState(AppState.currentState);
  const [shouldInitBindInviteCode, setShouldInitBindInviteCode] =
    useState(false);

  const fetchAndBindInviteCode = async () => {
    if (typeof authToken === 'undefined') {
      return;
    }
    try {
      console.log('---------------begin bind invite code-----------------');
      const content = await Clipboard.getString();
      const authInviteCodeRs = await getInviteCode(authToken);
      const authInviteCode = authInviteCodeRs?.code;
      console.log('authInviteCode', authInviteCode);
      console.log('content', content);
      const inviteCode = content.match(/[?&]code=([^&]+)/);
      console.log('inviteCode', inviteCode);
      if (inviteCode && inviteCode[1]) {
        // because the auth sig may not be updated, so we need to check the invite code.
        if (inviteCode[1] === authInviteCode) {
          // do nothing
          console.log('invite code is the same, skip bind');
        } else {
          const result = await bindInviteCode(authToken, {
            code: inviteCode[1],
          });
          console.log('bindInviteCode result', result);
          setShouldInitBindInviteCode(false);
        }
      } else {
        console.log('not match invite code', content);
        setShouldInitBindInviteCode(false);
      }
    } catch (error) {
      console.warn('get clipboard content error', error);
    }
    console.log('---------------end bind invite code-----------------');
  };

  const logout = useCallback(async () => {
    setIsLoggedIn(false);
    // if (typeof identityId === 'undefined') {
    //   setIsLoggedIn(false);
    // } else {
    //   try {
    //     const identiyModel = new IdentityModel();
    //     await identiyModel.deleteIdentityByUid(identityId);
    //     const userModel = new UserModel();
    //     await userModel.deleteUserByUserName(identityId);
    //   } catch (error) {}

    //   setIsLoggedIn(false);
    // }
  }, [identityId]);

  const getAuthSig = async (newIdentityId?: string) => {
    if (typeof newIdentityId === 'undefined') {
      if (typeof identityId === 'undefined') {
        return;
      }
      newIdentityId = identityId;
    }
    // from memory
    if (authSig) {
      if (
        authSig.identityId === newIdentityId &&
        authSig.expiresAt &&
        authSig.expiresAt > Date.now()
      ) {
        return authSig;
      }
    }

    // from storage
    const storedSignature = storage.getString(
      `${SIGNATURE_STORAGE_KEY}_${newIdentityId}`,
    );

    if (storedSignature) {
      const parsedSignature = JSON.parse(storedSignature);
      if (parsedSignature.expiresAt > Date.now()) {
        setAuthSig(parsedSignature);
        return parsedSignature;
      }
    }

    // from backend
    const res = await signPrincipal();
    const { message, signature } = res;
    const expiresAt = Date.now() + 24 * 60 * 60 * 1000;

    const result = { message, signature, expiresAt, identityId: newIdentityId };
    setAuthSig(result);
    storage.set(
      `${SIGNATURE_STORAGE_KEY}_${newIdentityId}`,
      JSON.stringify(result),
    );

    return result;
  };

  useEffect(() => {
    if (authToken && shouldInitBindInviteCode) {
      fetchAndBindInviteCode();
    }
  }, [authToken, shouldInitBindInviteCode]);

  useEffect(() => {
    if (isInitializing) {
      return;
    }
    if (
      typeof identityActor === 'undefined' ||
      typeof identityId === 'undefined'
    ) {
      setIsLoggedIn(false);
      setAuthToken(undefined);
    } else {
      getAuthSig(identityId).then(authSig => {
        console.log('authSig result', authSig);
        verifyPrincipal({
          message: authSig.message,
          signature: authSig.signature,
        })
          .then(result => {
            console.log('verifyPrincipal result', result);
            if (result?.authToken) {
              setAuthToken(result.authToken);
            } else {
              setAuthToken(undefined);
            }
          })
          .catch(error => {
            console.warn('verifyPrincipal error 1', error);
          });
      });

      greet()
        .then(rs => {
          console.log('greet result', identityId, rs);
          setIsLoggedIn(true);
        })
        .catch(error => {
          console.log('greet fail', error);
          setIsLoggedIn(false);
        });
    }
  }, [identityActor, identityId, isInitializing]);

  useEffect(() => {
    if (isInitializing) {
      return;
    }
    const handleAppStateChange = async (nextAppState: string) => {
      if (nextAppState === 'active') {
        console.log('App has come to the foreground!');
        greet()
          .then(rs => {
            console.log('greet result 2', identityId, rs);
            setIsLoggedIn(true);
          })
          .catch(error => {
            console.log('greet check fail', error);
            setIsLoggedIn(false);
          });
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, [appState, greet, isInitializing]);

  const contextValue = useMemo(
    () => ({
      isLoggedIn,
      getAuthSig,
      logout,
      authToken,
      fetchAndBindInviteCode,
      shouldInitBindInviteCode,
      setShouldInitBindInviteCode,
    }),
    [
      isLoggedIn,
      getAuthSig,
      logout,
      authToken,
      fetchAndBindInviteCode,
      shouldInitBindInviteCode,
      setShouldInitBindInviteCode,
    ],
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}
