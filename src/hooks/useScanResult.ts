import {useState, useCallback, useRef} from 'react';

interface UseScanResultOptions {
  debounceTime?: number;
  onScanResult?: (value: string) => void;
}

export const useScanResult = (options: UseScanResultOptions = {}) => {
  const {debounceTime = 2000, onScanResult} = options;
  const [isProcessing, setIsProcessing] = useState(false);
  const lastScannedCode = useRef<string | null>(null);
  const lastScanTime = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleScanResult = useCallback(
    (value: string | undefined | null) => {
      // Prevent empty or invalid values
      if (!value || value.trim() === '') {
        console.log('Invalid scan result, ignoring');
        return;
      }

      const currentTime = Date.now();

      // Prevent scanning the same code repeatedly
      if (
        value === lastScannedCode.current &&
        currentTime - lastScanTime.current < debounceTime
      ) {
        console.log('Duplicate scan detected, ignoring');
        return;
      }

      // Prevent duplicate calls while processing
      if (isProcessing) {
        console.log('Already processing scan result, ignoring');
        return;
      }

      console.log('Processing scan result:', value);

      setIsProcessing(true);
      lastScannedCode.current = value;
      lastScanTime.current = currentTime;

      // Clear previous timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      try {
        // Call callback function
        if (onScanResult) {
          onScanResult(value);
        }
      } catch (error) {
        console.error('Error in scan result handler:', error);
      } finally {
        // Set timeout to reset processing state
        timeoutRef.current = setTimeout(() => {
          setIsProcessing(false);
        }, debounceTime);
      }
    },
    [isProcessing, debounceTime, onScanResult],
  );

  // Handle useCodeScanner callback
  const handleCodeScanned = useCallback(
    (codes: any[]) => {
      if (!codes || codes.length === 0) return;
      handleScanResult(codes[0].value);
    },
    [handleScanResult],
  );

  // Cleanup function
  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsProcessing(false);
    lastScannedCode.current = null;
    lastScanTime.current = 0;
  }, []);

  return {
    handleScanResult,
    handleCodeScanned,
    isProcessing,
    cleanup,
  };
};
