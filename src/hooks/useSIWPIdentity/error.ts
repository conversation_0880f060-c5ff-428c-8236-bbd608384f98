export function normalizeError(error: Error | unknown | any): Error {
  if (error instanceof Error) {
    return error;
  } else if (typeof error === 'object') {
    let errorMessage = error?.message ?? 'An unknown error occurred.';
    errorMessage = errorMessage.indexOf('The operation couldn’t be completed')
      ? 'User canceled the operation'
      : errorMessage;
    return new Error(errorMessage);
  } else {
    return new Error('An unknown error occurred.');
  }
}
