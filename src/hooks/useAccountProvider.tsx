import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useIdentityProvider } from './useSIWPIdentity';
import { UserModel, User } from '../db/models/userModel';
import { UserProfile } from '../types/user';
import { getUserProfile } from '../api/backend/kyc';
import { useAuthProvider } from './useAuthProvider';

type AccountContextType = {
  userInfo?: User;
  userProfile: UserProfile | null;
  fetchUserProfile: (refresh?: boolean) => Promise<UserProfile | null>;
};

export const AccountContext = createContext<AccountContextType | undefined>(
  undefined,
);
export const useAccountProvider = () => {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error(
      'useAccounttProvider must be used within an AccountProvider',
    );
  }
  return context;
};

export function AccountProvider({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const [userInfo, setUserInfo] = useState<User | undefined>(undefined);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const { identityId } = useIdentityProvider();
  const { authToken } = useAuthProvider();

  const getUserInfo = async (userName: string) => {
    const userModel = new UserModel();
    const _userInfo = await userModel.getByUserName(userName);
    setUserInfo(_userInfo == null ? undefined : _userInfo);
  };

  const fetchUserProfile = async (refresh?: boolean) => {
    if (!authToken) {
      console.warn('get token failed, authToken is null');
      return null;
    }
    if (!refresh && userProfile) {
      return userProfile;
    }
    const res = await getUserProfile(authToken);
    if (res) {
      setUserProfile(res);
    }
    return res;
  };

  useEffect(() => {
    console.log('useEffect fetchUserProfile', authToken);
    if (authToken) {
      fetchUserProfile(true);
    }
  }, [authToken]);

  useEffect(() => {
    if (typeof identityId !== 'undefined') {
      getUserInfo(identityId);
    }
  }, [identityId]);

  const contextValue = useMemo(
    () => ({
      userInfo,
      userProfile,
      fetchUserProfile,
    }),
    [userInfo, userProfile],
  );

  return (
    <AccountContext.Provider value={contextValue}>
      {children}
    </AccountContext.Provider>
  );
}
