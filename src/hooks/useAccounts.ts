import {useEffect, useState} from 'react';

import {UserModel, UserWithCardNumber, User} from '../db/models/userModel';

export function useAccounts(identityId?: string) {
  const [loading, setLoading] = useState<boolean>(true);
  const [users, setUsers] = useState<UserWithCardNumber[]>([]);

  const getUsers = async () => {
    setLoading(true);
    try {
      const userModel = new UserModel();
      const result = await userModel.getAllWithCardNumber();
      setUsers(result);
      return result;
    } catch (error) {
      return [];
    } finally {
      setLoading(false);
    }
  };

  const saveUser = async (user: User) => {
    try {
      const userModel = new UserModel();
      await userModel.save(user);
      // Refresh the users list after saving and return the latest data
      const rs = await userModel.getAllWithCardNumber();
      console.log('rs_100', rs);
      setUsers(rs);
      return rs;
    } catch (error) {
      console.error('Error saving user:', error);
      return [];
    }
  };

  useEffect(() => {
    if (identityId) {
      getUsers();
    }
  }, [identityId]);

  return {
    loading,
    users,
    saveUser,
    setLoading,
  };
}
