import {useCallback, useMemo, useState} from 'react';
import {parseUnits} from '../utils';
import {useICBridge} from './useIcBridge';
import {useSolanaBridge} from './useSolanaBridge';
import {useBridgeState} from './useBridgeState';
import {ChainName, bridgeStateAtom} from '../store/bridgeAtom';
import {useAtomValue} from 'jotai';
import {User} from '../db/models/userModel';

type TxStatus =
  | 'idle'
  | 'preparing'
  | 'submitting'
  | 'confirming'
  | 'completed'
  | 'failed';

/**
 * Progress event with detailed status information
 */
type ProgressEvent = {
  status: TxStatus;
  message: string;
  progress: number; // 0-100
  txHash?: string;
  error?: Error;
};

/**
 * A hook that manages the bridge transaction process
 */
export function useBridgeTransaction(user: User) {
  const {
    fromChainInfo,
    bridgeAmount,
    fromChainData,
    toChainData,
    setProcessing,
    setTxHash,
    setError,
    resetBridge,
  } = useBridgeState();

  const [status, setStatus] = useState<TxStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState('');
  const processing = useMemo(() => {
    return ![0, 100].includes(progress);
  }, [progress]);

  const icBridge = useICBridge(user);
  const solanaBridge = useSolanaBridge(user);

  const bridgeState = useAtomValue(bridgeStateAtom);
  // const setBridgeError = useSetAtom(setBridgeErrorAtom);
  // const setBridgeProcessing = useSetAtom(setBridgeProcessingAtom);

  /**
   * Check if there's enough SOL balance for gas fees
   */
  const checkSolBalance = useCallback(async (): Promise<boolean> => {
    // Get SOL balance from bridge state
    const solBalance = bridgeState.chains.solana.solBalance;

    if (!solBalance) {
      setError('Unable to get SOL balance. Please refresh and try again.');
      return false;
    }

    if (!solanaBridge.solanaService) {
      setError('Solana service not initialized');
      return false;
    }

    try {
      // Use tokenBalance to determine if token account exists - if > 0 or = 0, account exists
      const tokenBalance = await solanaBridge.solanaService.getTokenBalance();

      // If we get here, the token account exists (even with 0 balance) or doesn't exist
      // Set minimum required SOL balance based on whether we got a balance (meaning account exists)
      const minBalanceRequired = tokenBalance !== null ? '0.001' : '0.003';

      // Compare SOL balance and required minimum
      if (parseFloat(solBalance) < parseFloat(minBalanceRequired)) {
        setError(
          `Insufficient SOL balance for gas fees. Need at least ${minBalanceRequired} SOL.`,
        );
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking token account:', error);
      // If error, use the higher threshold to be safe
      if (parseFloat(solBalance) < 0.003) {
        setError(
          'Insufficient SOL balance for gas fees. Need at least 0.003 SOL.',
        );
        return false;
      }
      return true;
    }
  }, [
    bridgeState.chains.solana.solBalance,
    solanaBridge.solanaService,
    setError,
  ]);

  /**
   * Report progress during the bridge transaction
   */
  const reportProgress = useCallback(
    (event: ProgressEvent) => {
      setStatus(event.status);
      setStatusMessage(event.message);
      setProgress(event.progress);

      if (event.txHash) {
        setTxHash(event.txHash);
      }

      if (event.error) {
        setError(event.error.message);
      }
    },
    [setError, setTxHash],
  );

  /**
   * Execute a bridge transaction from ICP to Solana
   */
  const bridgeFromICPToSolana = useCallback(async () => {
    if (
      !icBridge.icService ||
      !fromChainData.address ||
      !toChainData.address ||
      !bridgeAmount ||
      !solanaBridge.solanaService
    ) {
      setError('Missing required data for bridge transaction');
      return false;
    }

    try {
      setProcessing(true);
      reportProgress({
        status: 'preparing',
        message: 'Preparing transaction...',
        progress: 10,
      });
      console.log('fromChainInfo', fromChainInfo);
      console.log('bridgeAmount', bridgeAmount);

      // Convert amount to proper format
      const amountNumber = Number(
        parseUnits(bridgeAmount, fromChainInfo?.decimals ?? 6),
      );
      console.log('amountNumber', amountNumber);

      // Check if SOL balance is sufficient for the transaction
      const hasSufficientBalance = await checkSolBalance();
      if (!hasSufficientBalance) {
        return false;
      }

      // Approve the tokens to be bridged
      reportProgress({
        status: 'submitting',
        message: 'Approving tokens...',
        progress: 20,
      });

      const approveResult = await icBridge.icService.approve(amountNumber);

      console.log('approveResult', approveResult);

      const allowanceResult = await icBridge.icService.checkAllowance(
        fromChainData.address,
      );

      console.log('allowanceResult', allowanceResult);
      // Execute the bridge transaction
      reportProgress({
        status: 'submitting',
        message: 'Submitting bridge transaction...',
        progress: 40,
      });

      const depositResult = await icBridge.icService.deposit(amountNumber);

      console.log('depositResult', depositResult);

      const bridgeResult = await icBridge.icService.bridgeToSolana(
        amountNumber,
        toChainData.address,
      );

      if (!bridgeResult.Ok) {
        throw new Error(bridgeResult.Err);
      }

      // Extract the transaction info
      const txId = bridgeResult.Ok[0].toString();
      const signature = bridgeResult.Ok[1].toString();

      reportProgress({
        status: 'confirming',
        message: 'Transaction submitted, creating claim...',
        progress: 70,
        txHash: txId,
      });

      // Create a claim on the Solana side
      await solanaBridge.solanaService.createClaimFromICP(
        amountNumber,
        txId,
        signature,
      );

      reportProgress({
        status: 'completed',
        message: 'Bridge transaction completed',
        progress: 100,
        txHash: txId,
      });

      return true;
    } catch (error) {
      reportProgress({
        status: 'failed',
        message: `Bridge failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        progress: 0,
        error: error instanceof Error ? error : new Error(String(error)),
      });
      return false;
    } finally {
      setProcessing(false);
    }
  }, [
    icBridge,
    solanaBridge,
    fromChainData,
    toChainData,
    bridgeAmount,
    fromChainInfo,
    reportProgress,
    setProcessing,
    setError,
    checkSolBalance,
  ]);

  /**
   * Execute a bridge transaction from Solana to ICP
   */
  const bridgeFromSolanaToICP = useCallback(async () => {
    if (
      !solanaBridge.solanaService ||
      !toChainData.address ||
      !bridgeAmount ||
      !icBridge.icService
    ) {
      setError('Missing required data for bridge transaction');
      return false;
    }

    try {
      setProcessing(true);
      reportProgress({
        status: 'preparing',
        message: 'Preparing transaction...',
        progress: 10,
      });
      console.log('___1');
      // Convert amount to proper format
      const amountNumber = Number(
        parseUnits(bridgeAmount, fromChainInfo?.decimals ?? 6),
      );
      console.log('___2');
      // Execute the bridge transaction
      reportProgress({
        status: 'submitting',
        message: 'Submitting bridge transaction...',
        progress: 30,
      });
      console.log('___3');
      const txHash = await solanaBridge.solanaService?.createBridgeDeposit(
        amountNumber,
        toChainData.address,
      );
      console.log('___4');
      reportProgress({
        status: 'confirming',
        message: 'Transaction submitted, waiting for confirmation...',
        progress: 50,
        txHash,
      });

      // Wait for the transaction to be processed
      await solanaBridge.solanaService?.pollBackendUntilProcessed(txHash);
      console.log('___5');
      reportProgress({
        status: 'confirming',
        message: 'Checking ICP balance...',
        progress: 80,
      });
      console.log('___6');
      reportProgress({
        status: 'completed',
        message: 'Bridge transaction completed',
        progress: 100,
        txHash,
      });
      console.log('___7');
      return true;
    } catch (error) {
      reportProgress({
        status: 'failed',
        message: `Bridge failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        progress: 0,
        error: error instanceof Error ? error : new Error(String(error)),
      });
      return false;
    } finally {
      setProcessing(false);
    }
  }, [
    solanaBridge,
    icBridge,
    fromChainInfo,
    toChainData,
    bridgeAmount,
    reportProgress,
    setProcessing,
    setError,
  ]);

  /**
   * Execute a bridge transaction based on the selected chains
   */
  const executeBridge = useCallback(async () => {
    // console.log('----start executeBridge get params----');
    // console.log('fromChainInfo', fromChainInfo);
    // console.log('fromChainData', fromChainData);
    // console.log('toChainData', toChainData);
    // console.log('bridgeAmount', bridgeAmount);
    // console.log('icBridge.icService', icBridge.icService);
    // console.log('solanaBridge.solanaService', solanaBridge.solanaService);
    // console.log('----end executeBridge get params----');

    if (fromChainInfo?.name === ChainName.ICP) {
      return bridgeFromICPToSolana();
    } else if (fromChainInfo?.name === ChainName.SOLANA) {
      return bridgeFromSolanaToICP();
    } else {
      setError(`Unsupported chain: ${fromChainInfo?.name}`);
      return false;
    }
  }, [fromChainInfo, bridgeFromICPToSolana, bridgeFromSolanaToICP, setError]);

  return {
    executeBridge,
    status,
    progress,
    statusMessage,
    reset: resetBridge,
    processing,
  };
}
