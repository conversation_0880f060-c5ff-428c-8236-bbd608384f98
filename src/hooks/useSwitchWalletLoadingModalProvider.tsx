import {createContext, ReactNode, useContext, useMemo, useState} from 'react';
type SwitchWalletLoadingModalContextType = {
  openSwitchWalletLoadingModal: () => void;
  closeSwitchWalletLoadingModal: () => void;
  isSwitchWalletLoadingModalVisible: boolean;
};

const SwitchWalletLoadingModalContext = createContext<
  SwitchWalletLoadingModalContextType | undefined
>(undefined);

export const useSwitchWalletLoadingModalProvider =
  (): SwitchWalletLoadingModalContextType => {
    const context = useContext(SwitchWalletLoadingModalContext);
    if (!context) {
      throw new Error(
        'useSwitchWalletLoadingModalProvider must be used within an SwitchWalletLoadingModalProvider',
      );
    }
    return context;
  };

export function SwitchWalletLoadingModalProvider({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const [
    isSwitchWalletLoadingModalVisible,
    setIsSwitchWalletLoadingModalVisible,
  ] = useState<boolean>(false);
  const openSwitchWalletLoadingModal = () => {
    setIsSwitchWalletLoadingModalVisible(true);
  };

  const closeSwitchWalletLoadingModal = () => {
    setIsSwitchWalletLoadingModalVisible(false);
  };

  const contextValue = useMemo(
    () => ({
      isSwitchWalletLoadingModalVisible,
      openSwitchWalletLoadingModal,
      closeSwitchWalletLoadingModal,
    }),
    [
      isSwitchWalletLoadingModalVisible,
      openSwitchWalletLoadingModal,
      closeSwitchWalletLoadingModal,
    ],
  );

  return (
    <SwitchWalletLoadingModalContext.Provider value={contextValue}>
      {children}
    </SwitchWalletLoadingModalContext.Provider>
  );
}
