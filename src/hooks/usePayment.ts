import {useState} from 'react';
import {PaymentInfo, PaymentOrder, UpdateOrderInfo} from '../types/order';
import {
  coinCalculatorQuery,
  saveAppUserDto,
  coinCalculatorQueryPosScan,
} from '../api/backend';
import {getPosScanOrder, updateOrder} from '../api/backend/kyc';
import {getOrderById} from '../api/backend/kyc';
import {useAuthProvider} from './useAuthProvider';

export function usePayment() {
  const [orderLoading, setOrderLoading] = useState<boolean>(false);
  const [orderInfo, setOrderInfo] = useState<PaymentOrder>();
  const {authToken} = useAuthProvider();

  const getOrderInfoById = async (orderId: string) => {
    setOrderLoading(true);
    if (!authToken) {
      console.warn('get token failed, authToken is null');
      return null;
    }
    const orderInfo = await getOrderById(authToken, orderId);
    console.log('orderInfo got:', JSON.stringify(orderInfo));
    setOrderInfo(orderInfo == null ? undefined : orderInfo);
    setOrderLoading(false);
  };

  const getPosScanOrderInfo = async (
    authToken: string,
    orderId: string,
    payerWallet: string,
    timestamp: number,
  ) => {
    setOrderLoading(true);
    const result = await getPosScanOrder(
      {
        limit: 30,
        payerWallet: payerWallet,
        timestamp: timestamp,
      },
      authToken,
    );
    setOrderLoading(false);
    if (result && result.data && result.data.length > 0) {
      const orderInfo = result.data.find(
        (item: PaymentOrder) => item.orderId === orderId,
      );
      if (orderInfo) {
        setOrderInfo(orderInfo);
      }
    }
  };

  const getPaymentInfo = async (
    orderId: string,
    tokenSymbol: string,
    tokenDecimals: number,
    tokenAddress?: string,
  ): Promise<PaymentInfo | null> => {
    const paymentInfo = await coinCalculatorQuery(
      tokenSymbol,
      orderId,
      tokenDecimals,
    );

    return paymentInfo;
  };

  const getPosScanPaymentInfo = async (
    orderId: string,
    tokenSymbol: string,
    tokenAddress: string,
  ): Promise<PaymentInfo | null> => {
    const paymentInfo = await coinCalculatorQueryPosScan(
      tokenSymbol,
      orderId,
      tokenAddress,
    );
    return paymentInfo;
  };

  const updateOrderPaymentInfo = async (params: UpdateOrderInfo) => {
    if (!authToken) {
      console.warn('get token failed, authToken is null');
      return null;
    }
    return await updateOrder(authToken, params);
  };

  const getSaveAppUserDto = async (pushAppId?: string) => {
    return await saveAppUserDto(pushAppId);
  };

  return {
    orderLoading,
    orderInfo,
    getOrderInfoById,
    getPosScanOrderInfo,
    getPaymentInfo,
    getPosScanPaymentInfo,
    updateOrderPaymentInfo,
    getSaveAppUserDto,
  };
}
