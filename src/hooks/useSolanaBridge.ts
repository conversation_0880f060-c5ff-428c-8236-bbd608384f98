import {useEffect, useState} from 'react';
import {Connection, PublicKey} from '@solana/web3.js';
import {useSetAtom} from 'jotai';
import {SolanaBridgeService, SolanaWallet} from '../utils/SolanaBridgeService';
import {updateChainDataAtom} from '../store/bridgeAtom';
import {RPCS} from '../constants/rpc';
import {formatUnits} from '../utils';
import Config from 'react-native-config';
import {User} from '../db/models/userModel';
import {useActorIdentity} from './useActorIdentity';

export function useSolanaBridge(user: User) {
  const [solanaService, setSolanaService] =
    useState<SolanaBridgeService | null>(null);

  const updateChainData = useSetAtom(updateChainDataAtom);
  const {solanaSignTx} = useActorIdentity();

  useEffect(() => {
    async function initService() {
      try {
        if (!user) {
          console.warn('User not found');
          return;
        }
        if (!user.solanaAddress) {
          console.warn('Solana wallet not available');
          return;
        }

        // const connection = new Connection(RPCS["solana-devnet"], "confirmed");
        const connection = new Connection(
          RPCS[Config.VITE_APP_BRIDGE_SOLANA_NETWORK],
          'confirmed',
        );
        const publicKey = new PublicKey(user.solanaAddress);
        const wallet: SolanaWallet = {
          publicKey,
          signTransaction: async transaction => {
            const signedTx = (await solanaSignTx(
              transaction.compileMessage().serialize().toString('hex'),
            )) as string;
            transaction.addSignature(publicKey, Buffer.from(signedTx, 'hex'));
            return transaction;
          },
        };

        const bridgeService = new SolanaBridgeService(connection, wallet);
        setSolanaService(bridgeService);

        try {
          const balance = await bridgeService.getTokenBalance();
          const formattedBalance = formatUnits(balance.toString(), 6);
          updateChainData({
            chainId: 'solana',
            data: {
              balance: `${formattedBalance} USDC`,
            },
          });
        } catch (e) {
          console.warn('Failed to get initial Solana balance:', e);
        }
      } catch (err) {
        console.error('Failed to initialize Solana bridge service:', err);
      }
    }

    initService();
  }, [user, updateChainData]);

  return {
    solanaService,
  };
}
