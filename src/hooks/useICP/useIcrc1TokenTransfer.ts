import {useCallback, useState} from 'react';
import {useIdentityProvider} from '../useSIWPIdentity';
import {_SERVICE} from '../../libs/icrc1';
import {idlFactory} from '../../libs/icrc1/service';
import {ActorSubclass, HttpAgent, Actor} from '@dfinity/agent';
import {Principal} from '@dfinity/principal';

export function useIcrc1TokenTransfer() {
  const [loading, setLoading] = useState<boolean>(false);
  const {identity} = useIdentityProvider();
  const icrc1TokenTransfer = useCallback(
    async (canisterId: string, receiver: string, amount: bigint) => {
      if (identity == undefined) {
        return;
      }
      if (loading) {
        return;
      }

      setLoading(true);
      try {
        const agent = new HttpAgent({
          identity: identity,
          retryTimes: 2,
        });

        const canisterActor: ActorSubclass<_SERVICE> = Actor.createActor(
          idlFactory as any,
          {
            agent,
            canisterId,
          },
        );
        const principalOwner = Principal.fromText(receiver);
        const mintResult = await canisterActor.icrc1_transfer({
          from_subaccount: [],
          to: {
            owner: principalOwner,
            subaccount: [],
          },
          amount,
          fee: [],
          memo: [],
          created_at_time: [],
        });
        setLoading(false);

        return mintResult;
      } catch (error) {
        setLoading(false);
        throw error;
      }
    },
    [identity, setLoading],
  );

  return {
    loading,
    icrc1TokenTransfer,
  };
}
