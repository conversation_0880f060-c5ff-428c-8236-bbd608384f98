import {useAccount} from '../useAccount';
import {Principal} from '@dfinity/principal';
import {ActorSubclass} from '@dfinity/agent';

import {_SERVICE, createActor} from '../../libs/icrc1';
import {MetadataValue} from '../../libs/icrc1/service';
import {useCallback} from 'react';
import {UserToken} from '../../db/models/userTokenModel';
import {parseValue} from '../../utils';

export function useIcrc1Token() {
  const {userInfo} = useAccount();
  const getTokenInfo = useCallback(
    async (canisterId: string) => {
      const actor: ActorSubclass<_SERVICE> = createActor(canisterId);

      let token: UserToken = {
        chainId: 'icp-main',
        address: canisterId,
        name: '',
        symbol: '',
        decimals: 0,
        owner: userInfo?.principalId ?? '',
        type: 'icrc1',
      };

      const getSymbol = (metadata: Array<[string, MetadataValue]>): string => {
        const symbolEntry = metadata.find(([key]) => key === 'icrc1:symbol');
        if (symbolEntry && symbolEntry[1]) {
          return (symbolEntry[1] as {Text: string}).Text;
        }
        return '';
      };

      const getName = (metadata: Array<[string, MetadataValue]>): string => {
        const nameEntry = metadata.find(([key]) => key === 'icrc1:name');
        if (nameEntry && nameEntry[1]) {
          return (nameEntry[1] as {Text: string}).Text;
        }
        return '';
      };

      const getDecimals = (
        metadata: Array<[string, MetadataValue]>,
      ): bigint => {
        const decimalsEntry = metadata.find(
          ([key]) => key === 'icrc1:decimals',
        );
        if (decimalsEntry && decimalsEntry[1]) {
          return (decimalsEntry[1] as {Nat: bigint}).Nat;
        }
        return 0n;
      };

      const getLogo = (metadata: Array<[string, MetadataValue]>): string => {
        const logoEntry = metadata.find(([key]) => key === 'icrc1:logo');
        if (logoEntry && logoEntry[1]) {
          return (logoEntry[1] as {Text: string}).Text;
        }
        return '';
      };

      try {
        const metadata = await actor.icrc1_metadata();
        token.symbol = getSymbol(metadata);
        token.name = getName(metadata);
        token.decimals = Number(getDecimals(metadata).toString());
        token.logo = getLogo(metadata);

        const principalOwner = Principal.fromText(userInfo?.principalId ?? '');
        const balance = await actor.icrc1_balance_of({
          owner: principalOwner,
          subaccount: [],
        });
        token.amount = balance.toString();
        token.uiAmount = parseValue(token.amount, token.decimals);
      } catch (error) {
        console.warn('get token info error', error);
      }

      return token;
    },
    [userInfo],
  );

  return {
    getTokenInfo,
  };
}
