import {useState} from 'react';
import {_SERVICE, createActor} from '../../libs/icrc1/index_service';
import {ActorSubclass} from '@dfinity/agent';
import {Principal} from '@dfinity/principal';
import {getIndexCanisterId} from '../../libs/icrc1/index_canister';

export function useIcrc1TokenTransactions() {
  const [loading, setLoading] = useState<boolean>(false);
  const [oldestTxId, setOldestTxId] = useState<[] | [bigint]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const getIcrc1TokenTransactions = async (
    principalId: string,
    canisterId: string,
    start: [] | [bigint],
    length: bigint,
  ) => {
    setLoading(true);
    try {
      const indexCanisterId = getIndexCanisterId(canisterId);
      const canisterActor: ActorSubclass<_SERVICE> =
        createActor(indexCanisterId);
      console.log('-=====================oldestTxId', oldestTxId);
      const result: any = await canisterActor.get_account_transactions({
        account: {
          owner: Principal.fromText(principalId),
          subaccount: [],
        },
        start: start,
        max_results: length,
      });

      setHasMore(result.Ok.oldest_tx_id[0] !== oldestTxId[0]);
      setOldestTxId(result.Ok.oldest_tx_id);
      setLoading(false);
      return result.Ok.transactions;
    } catch (error) {
      console.warn('get account transactions error', error);
      setLoading(false);
    }

    return [];
  };

  return {
    loading,
    hasMore,
    oldestTxId,
    getIcrc1TokenTransactions,
  };
}
