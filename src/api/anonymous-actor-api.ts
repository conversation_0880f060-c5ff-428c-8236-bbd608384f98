import {actor} from '../AnonymousActor';

export async function startRegisterUsername(
  registName: string,
  passkeyTitle: string,
  serverSig: {
    server_sig: string;
    timestamp: string;
  },
  displayName: string,
) {
  return await actor.start_register_username(
    registName,
    passkeyTitle,
    serverSig,
    displayName,
  );
}

export async function finishRegisterUsername(registrationResult: string) {
  return await actor.finish_register_username(registrationResult);
}

export async function startRegisterFast(
  passkeyTitle: string,
  displayName: string,
) {
  return await actor.start_register(passkeyTitle, displayName);
}

export async function finishRegisterFast(registrationResult: string) {
  return await actor.finish_register(registrationResult);
}

export async function startAuthenticationUsername(username: string) {
  return await actor.siwp_prepare_login_username(username);
}

export async function finishAuthenticationUsername(signResult: string) {
  return await actor.finish_authentication_username(signResult);
}

export async function startDeletePasskey(
  username: string,
  passkeyTitle: string,
) {
  return await actor.start_delete_passkey(username, passkeyTitle);
}

export async function finishDeletePasskey(deleteResult: string) {
  return await actor.finish_delete_passkey(deleteResult);
}

export async function startBindingVerificationCode(
  bindId: string,
  validCode: string,
) {
  return await actor.start_binding_verification_code(bindId, validCode);
}

export async function finishBindingVerificationCode(bindResult: string) {
  return await actor.finish_binding_verification_code(bindResult);
}

export async function convertSolanaAddress(username: string) {
  const data = await actor.solana_address(username);

  if ('Ok' in data) {
    return data.Ok;
  } else {
    throw new Error(data.Err);
  }
}
