import {
  PaymentInfo,
  PaymentOrder,
  SupportToken,
  UpdateOrderInfo,
} from '../../types/order';
import {AuthSig} from '../../types/user';
import Config from 'react-native-config';
import {parseValue} from '../../utils/index';
import {TokenPrice} from '../../types/token';
import {POS_SCAN_UNI_URL} from '../../config';

export const registerCallback = async (authSig: AuthSig) => {
  const response = await fetch(Config.BACKEND_HOST + '/api/register/callback', {
    method: 'POST',
    headers: {
      message: authSig.message,
      signature: authSig.signature,
    },
  });
  return await response.json();
};

export const requestPrepareSignUsername = async (username: string) => {
  try {
    const response = await fetch(
      Config.BACKEND_HOST + '/api/register/username/prepare_sign',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({username}),
      },
    );
    return await response.json();
  } catch (error) {
    console.log(error);
  }
};

export const selectUniwebOrder = async (orderId: string) => {
  try {
    const response = await fetch(
      Config.UP_HOST +
        '/zcloak-rest/uniwebOrder/selectUniwebOrder?id=' +
        orderId,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const resObj = await response.json();
    if (resObj['code'] == '000000') {
      console.log('selectUniwebOrder', JSON.stringify(resObj));
      const data = resObj['data'];
      const tokenList = data['storeCryptoMethodDtoList'];
      const supportTokenList: SupportToken[] = [];
      for (let i = 0; i < tokenList.length; i++) {
        const token = tokenList[i];
        supportTokenList.push({
          symbol: token['cryptoSymbol'],
          isNative: (token['cryptoSymbol'] as string).toUpperCase() == 'SOL',
          address: token['cryptoAddress'],
          // address:'5i77q-xjo7v-pvksq-deycb-dgobd-j4z47-4hia4-2ccnc-xq64a-jqypg-gqe'
        });
      }

      const orderInfo: PaymentOrder = {
        orderId: orderId,
        orderValue: parseValue(Number(data['amount']) + '', 2),
        currency: data['currency'],
        secondVerification: data['secondVerification'] != 'NO',
        merchantName: data['storeName'],
        paymentStatus: data['paymentStatus'],
        merchantSolanaAddress: data['vusdAddress'],
        defaultPaymentToken: supportTokenList[1].symbol,
        supportTokenList: supportTokenList,
      };
      console.log('orderInfo', JSON.stringify(orderInfo));
      return orderInfo;
    } else {
      return null;
    }
  } catch (error) {
    console.log('selectUniwebOrder error', error);
  }

  return null;
};

export const getCheckoutConfiguration = async (): Promise<string | null> => {
  try {
    const response = await fetch(
      Config.UP_HOST + '/zcloak-rest/checkout/getConfiguration',
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const result = await response.json();
    if (result['code'] == '000000') {
      return result['data']['checkoutPublicApikey'];
    } else {
      return null;
    }
  } catch (error) {
    console.log('getCheckoutConfiguration', error);
  }

  return null;
};

export const coinPriceQuery = async (
  tokenSymbol: string,
  tokenAddress?: string,
) => {
  try {
    const response = await fetch(
      Config.UP_HOST +
        '/zcloak-rest/coinMarketCap/coinPriceQuery?symbol=' +
        tokenSymbol,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const resObj = await response.json();
    console.log('resObj', resObj);
    if (resObj['code'] == '000000') {
      const data = resObj['data'];
      const result: TokenPrice[] = [];
      for (let i = 0; i < data.length; i++) {
        result.push({
          symbol: data[i]['symbol'],
          price: data[i]['sgdPrice'],
          priceSymbol: 'SGD',
        });
      }
      return result;
    } else {
      return [];
    }
  } catch (error) {
    console.log('coinPriceQuery', error);
  }

  return [];
};

export async function coinCalculatorQueryPosScan(
  symbol: string,
  orderId: string,
  tokenAddress: string,
): Promise<PaymentInfo | null> {
  try {
    const response = await fetch(
      `${POS_SCAN_UNI_URL}/crypto/coin_calculator?symbol=${symbol}&id=${orderId}&tokenAddress=${tokenAddress}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const resObj = await response.json();
    console.log('resObj', resObj);
    if (resObj['code'] == '200') {
      const data = resObj['data'];
      return {
        payTokenAmount: parseValue(
          data.payTokenAmount + '',
          0,
          data.payTokenDecimal,
        ),
        payTokenSymbol: symbol,
        tokenPrice: data.tokenPrice,
      };
    } else {
      return null;
    }
  } catch (error) {
    console.log('coinCalculatorQuery', error);
  }
  return null;
}

export async function coinCalculatorQuery(
  symbol: string,
  orderId: string,
  decimals: number,
): Promise<PaymentInfo | null> {
  try {
    const response = await fetch(
      Config.UP_HOST +
        '/zcloak-rest/coinMarketCap/coinCalculatorQuery?symbol=' +
        symbol +
        '&transactionId=' +
        orderId,

      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const resObj = await response.json();
    if (resObj['code'] == '000000') {
      const data = resObj['data'];
      return {
        payTokenAmount: parseValue(data['tokenAmount'] + '', 0, decimals),
        payTokenSymbol: symbol,
        tokenPrice: data['sgdPrice'],
      };
    } else {
      return null;
    }
  } catch (error) {
    console.log('coinCalculatorQuery', error);
  }
  return null;
}

export async function updateOrder(params: UpdateOrderInfo): Promise<boolean> {
  try {
    console.log(params);
    console.log('update');
    const response = await fetch(
      Config.UP_HOST + '/zcloak-rest/uniwebOrder/updateOrder',
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      },
    );
    const resObj = await response.json();
    if (resObj['code'] == '000000') {
      return resObj;
    } else {
      return false;
    }
  } catch (error) {
    console.log('updateOrder error', error);
  }

  return false;
}

export const saveAppUserDto = async (pushAppId?: string) => {
  try {
    const response = await fetch(
      Config.UP_HOST + '/zcloak-rest/appUser/saveAppUserDto',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({pushAppId: pushAppId}),
      },
    );
    const result = await response.json();
    if (result['code'] == '000000') {
      return result['data'];
    } else {
      return null;
    }
  } catch (error) {
    console.log('saveAppUserDto', error);
  }

  return null;
};

export const pageUserMessageDto = async (
  currentPage: number,
  itemsPerPage: number,
) => {
  try {
    const response = await fetch(
      Config.UP_HOST +
        '/zcloak-rest/userMessage/pageUserMessageDto?index=' +
        currentPage +
        '&pageSize=' +
        itemsPerPage,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const result = await response.json();
    if (result['code'] == '000000') {
      return result['data']['records'];
    } else {
      return null;
    }
  } catch (error) {
    console.log('getCheckoutConfiguration', error);
  }

  return null;
};

export const userMessageDetail = async (id: string) => {
  try {
    const response = await fetch(
      Config.UP_HOST + '/zcloak-rest/userMessage/userMessageDetail?id=' + id,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const result = await response.json();
    if (result['code'] == '000000') {
      return result['data'];
    } else {
      return null;
    }
  } catch (error) {
    console.log('getCheckoutConfiguration', error);
  }

  return null;
};

// /api/auth/verify_principal
export interface RequestVerifyPrincipal {
  message: string;
  signature: string;
}

export interface ResponseVerifyPrincipal {
  message: string;
  authToken: string;
}

export const verifyPrincipal = async (
  params: RequestVerifyPrincipal,
): Promise<ResponseVerifyPrincipal | null> => {
  try {
    const response = await fetch(
      Config.INVITE_HOST + '/api/auth/verify_principal',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      },
    );
    const result = await response.json();
    if (response.status == 200) {
      return result;
    } else {
      return null;
    }
  } catch (error) {
    console.warn('verifyPrincipal err 0', error);
  }

  return null;
};

export interface ResponseGetInviteCode {
  message: string;
  code: string;
}

export const getInviteCode = async (
  authToken: string,
): Promise<ResponseGetInviteCode | null> => {
  try {
    const response = await fetch(
      Config.INVITE_HOST + '/api/user/get_invite_code',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken,
        },
        body: JSON.stringify({}),
      },
    );

    console.log('getInviteCode response', response);
    const result = await response.json();
    console.log('getInviteCode result 1', result);
    if (response.status == 200) {
      return result;
    } else {
      return null;
    }
  } catch (error) {
    console.warn('getInviteCode', error);
  }

  return null;
};

export interface RequestGetInviteNumber {
  code: string;
}

export interface ResponseGetInviteNumber {
  message: string;
  number: number;
}

export const getInviteNumber = async (
  params: RequestGetInviteNumber,
): Promise<ResponseGetInviteNumber | null> => {
  try {
    const response = await fetch(
      Config.INVITE_HOST + '/api/user/get_invitee_number',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      },
    );
    const result = await response.json();
    console.log('getInviteNumber result', result);
    if (response.status == 200) {
      return result;
    } else {
      return null;
    }
  } catch (error) {
    console.warn('getInviteNumber', error);
  }

  return null;
};

export interface RequestBindInviteCode {
  code: string;
}

export interface ResponseBindInviteCode {
  message: string;
}
export const bindInviteCode = async (
  authToken: string,
  params: RequestBindInviteCode,
): Promise<ResponseBindInviteCode | null> => {
  try {
    const response = await fetch(
      Config.INVITE_HOST + '/api/user/bind_invite_code',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken,
        },
        body: JSON.stringify(params),
      },
    );
    const result = await response.json();
    console.log('bindInviteCode result', result);
    if (response.status == 200) {
      return result;
    } else {
      return null;
    }
  } catch (error) {
    console.warn('bindInviteCode', error);
  }

  return null;
};

export interface ResponseNFT {
  message: string;
  data: number;
}

export const getNFT = async (
  authToken: string,
): Promise<ResponseNFT | null> => {
  try {
    const response = await fetch(Config.INVITE_HOST + '/api/user/nft', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'auth-token': authToken,
      },
    });
    const result = await response.json();
    if (response.status == 200) {
      return result;
    } else {
      return null;
    }
  } catch (error) {
    console.warn('getNFT', error);
  }

  return null;
};
