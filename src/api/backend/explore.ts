import Config from 'react-native-config';
import {HistoryResponse} from '../../types/history';

// 获取交易记录
export const getICRC1Transactions = async (params: {
  account: string;
  token: string;
  limit?: number;
  skip?: number;
}): Promise<HistoryResponse | null> => {
  const response = await fetch(
    `${Config.VITE_APP_EXPLORER_SERVICE}/api/transactions/${
      params.account
    }?token=${params.token}&limit=${params.limit || 10}&skip=${
      params.skip || 0
    }`,
  );

  const resObj = await response.json();
  if (resObj['code'] == '200') {
    return resObj['data'];
  } else {
    console.warn('getTransactions failed', resObj);
    return null;
  }
};
