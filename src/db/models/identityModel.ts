import {BaseModel} from './baseModel';

export interface MIdentity {
  uid: string;
  data: string;
  isSelected: number;
}

export class IdentityModel extends BaseModel {
  async createTable(): Promise<void> {
    try {
      const db = await BaseModel.getDB();
      const query = `
                CREATE TABLE IF NOT EXISTS identity (
                    uid TEXT NOT NULL PRIMARY KEY,
                    data TEXT NOT NULL,
                    is_selected INTEGER NOT NULL DEFAULT 0
                );
            `;
      await db.executeSql(query);
    } catch (error) {
      console.log('create table error', error);
    }
  }

  async getAll(): Promise<MIdentity[]> {
    const db = await BaseModel.getDB();
    const query = 'select uid, data, is_selected as isSelected from identity';
    const [results] = await db.executeSql(query);
    return results.rows.raw() as MIdentity[];
  }

  async dropTable(): Promise<void> {
    const db = await BaseModel.getDB();
    await db.executeSql('DROP TABLE IF EXISTS identity;');
  }

  async insert(identity: MIdentity): Promise<number> {
    const db = await BaseModel.getDB();

    const query =
      'INSERT INTO identity (uid, data, is_selected) VALUES (?, ?, ?)';
    const result = await db.executeSql(query, [
      identity.uid,
      identity.data,
      identity.isSelected,
    ]);
    return result[0].insertId;
  }

  async update(identity: MIdentity): Promise<void> {
    const db = await BaseModel.getDB();
    const query = 'UPDATE identity set data = ?, is_selected = ? WHERE uid = ?';
    await db.executeSql(query, [
      identity.data,
      identity.isSelected,
      identity.uid,
    ]);
  }

  async getByUid(uid: string): Promise<MIdentity | null> {
    const db = await BaseModel.getDB();
    const query =
      'select uid, data, is_selected as isSelected from identity where uid = ?';
    const [results] = await db.executeSql(query, [uid]);
    const identities = results.rows.raw() as MIdentity[];
    return identities.length > 0 ? identities[0] : null;
  }

  async getSelected(): Promise<MIdentity | null> {
    const db = await BaseModel.getDB();
    const query =
      'select uid, data, is_selected as isSelected from identity where is_selected = ?';
    const [results] = await db.executeSql(query, [1]);
    const identities = results.rows.raw() as MIdentity[];
    return identities.length > 0 ? identities[0] : null;
  }

  async save(identity: MIdentity): Promise<void> {
    const tmpIdentity = await this.getByUid(identity.uid);

    if (tmpIdentity != null) {
      await this.update(identity);
    } else {
      await this.insert(identity);
    }
  }

  async setSelected(uid: string): Promise<void> {
    const db = await BaseModel.getDB();
    db.transaction(
      tx => {
        tx.executeSql('update identity set is_selected = 0 where uid <> ?', [
          uid,
        ]);
        tx.executeSql('update identity set is_selected = 1 where uid = ?', [
          uid,
        ]);
      },
      error => {},
      () => {},
    );
  }

  async deleteAll(): Promise<void> {
    const db = await BaseModel.getDB();
    const query = 'DELETE FROM identity';
    await db.executeSql(query);
  }

  async deleteIdentity(identity: MIdentity): Promise<void> {
    const db = await BaseModel.getDB();
    const query = 'DELETE FROM identity where uid = ?';
    await db.executeSql(query, [identity.uid]);
  }

  async deleteIdentityByUid(uid: string) {
    const db = await BaseModel.getDB();
    const query = 'DELETE FROM identity where uid = ?';
    await db.executeSql(query, [uid]);
  }
}
