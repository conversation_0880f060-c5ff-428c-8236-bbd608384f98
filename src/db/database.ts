import SQLite from 'react-native-sqlite-storage';

SQLite.DEBUG(false);
SQLite.enablePromise(true);

const initDatabase = async () => {
  return await SQLite.openDatabase({
    name: 'up-network.db',
    location: 'default',
  });
};

export const getDatabase = (() => {
  let dbInstance: SQLite.SQLiteDatabase | null = null;

  return async (): Promise<SQLite.SQLiteDatabase> => {
    if (!dbInstance) {
      dbInstance = await initDatabase();
    }
    return dbInstance;
  };
})();
