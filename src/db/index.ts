import {UserModel} from './models/userModel';
import {UserTokenModel} from './models/userTokenModel';
import {IdentityModel} from './models/identityModel';
import {CardModel} from './models/cardModel';
export async function setupDatabase() {
  const userModel = new UserModel();
  await userModel.createTable();

  const userTokenModel = new UserTokenModel();
  await userTokenModel.createTable();

  const identityModel = new IdentityModel();
  await identityModel.createTable();

  const cardModel = new CardModel();
  await cardModel.createTable();

  // console.log(await cardModel.getAllBindWithAddress());
  // console.log('init get total number', await userModel.getTotalNumber());
  // console.log('-----------------', await userModel.getAllWithCardNumber());
  // console.log('init get all identities', await identityModel.getAll());
}
