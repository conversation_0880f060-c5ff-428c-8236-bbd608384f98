import {atom} from 'jotai';

// Chain definitions
export enum ChainName {
  ICP = 'VLY',
  SOLANA = 'Solana',
}

export enum TokenSymbol {
  vUSD = 'vUSD',
  USDC = 'USDC',
}

export type Chain = {
  id: string;
  name: ChainName;
  icon: any;
  tokenSymbol: TokenSymbol;
  decimals: number;
};

// Chain & wallet data
export type ChainWalletData = {
  connected: boolean;
  address: string | null;
  balance: string | null;
  solBalance?: string | null;
  error: string | null;
};

// const initBridgeAmount = '0.00001';
const initBridgeAmount = '0';

export type BridgeState = {
  chains: {
    [chainId: string]: ChainWalletData;
  };
  fromChainId: string;
  toChainId: string;
  bridgeAmount: string;
  processing: boolean;
  error: string | null;
  txHash: string | null;
  // Pool balances for information display
  poolBalances: {
    [chainId: string]: string;
  };
};

// Initial state
export const initialBridgeState: BridgeState = {
  chains: {
    icp: {connected: false, address: null, balance: null, error: null},
    solana: {connected: false, address: null, balance: null, error: null},
  },
  fromChainId: 'solana',
  toChainId: 'icp',
  bridgeAmount: initBridgeAmount,
  processing: false,
  error: null,
  txHash: null,
  poolBalances: {
    icp: '',
    solana: '',
  },
};

// Main bridge state atom
export const bridgeStateAtom = atom<BridgeState>(initialBridgeState);

// Available chains
export const supportedChainsAtom = atom<Chain[]>([]);

// Convenience derived atoms
export const fromChainIdAtom = atom(
  get => get(bridgeStateAtom).fromChainId,
  (get, set, chainId: string) => {
    set(bridgeStateAtom, {
      ...get(bridgeStateAtom),
      fromChainId: chainId,
    });
  },
);

export const toChainIdAtom = atom(
  get => get(bridgeStateAtom).toChainId,
  (get, set, chainId: string) => {
    set(bridgeStateAtom, {
      ...get(bridgeStateAtom),
      toChainId: chainId,
    });
  },
);

export const bridgeAmountAtom = atom(
  get => get(bridgeStateAtom).bridgeAmount,
  (get, set, amount: string) => {
    set(bridgeStateAtom, {
      ...get(bridgeStateAtom),
      bridgeAmount: amount,
    });
  },
);

// Computed atoms for UI
export const fromChainInfoAtom = atom(get => {
  const {fromChainId} = get(bridgeStateAtom);
  const chains = get(supportedChainsAtom);
  return chains.find(chain => chain.id === fromChainId) || null;
});

export const toChainInfoAtom = atom(get => {
  const {toChainId} = get(bridgeStateAtom);
  const chains = get(supportedChainsAtom);
  return chains.find(chain => chain.id === toChainId) || null;
});

export const fromChainDataAtom = atom(
  get => get(bridgeStateAtom).chains[get(bridgeStateAtom).fromChainId],
  (get, set, data: Partial<ChainWalletData>) => {
    const state = get(bridgeStateAtom);
    const chainId = state.fromChainId;

    set(bridgeStateAtom, {
      ...state,
      chains: {
        ...state.chains,
        [chainId]: {
          ...state.chains[chainId],
          ...data,
        },
      },
    });
  },
);

export const toChainDataAtom = atom(
  get => get(bridgeStateAtom).chains[get(bridgeStateAtom).toChainId],
  (get, set, data: Partial<ChainWalletData>) => {
    const state = get(bridgeStateAtom);
    const chainId = state.toChainId;

    set(bridgeStateAtom, {
      ...state,
      chains: {
        ...state.chains,
        [chainId]: {
          ...state.chains[chainId],
          ...data,
        },
      },
    });
  },
);

// Actions
export const updateChainDataAtom = atom(
  null,
  (get, set, params: {chainId: string; data: Partial<ChainWalletData>}) => {
    const {chainId, data} = params;
    const state = get(bridgeStateAtom);

    set(bridgeStateAtom, {
      ...state,
      chains: {
        ...state.chains,
        [chainId]: {
          ...state.chains[chainId],
          ...data,
        },
      },
    });
  },
);

export const setBridgeErrorAtom = atom(
  null,
  (get, set, error: string | null) => {
    set(bridgeStateAtom, {
      ...get(bridgeStateAtom),
      error,
    });
  },
);

export const setBridgeProcessingAtom = atom(
  null,
  (get, set, processing: boolean) => {
    set(bridgeStateAtom, {
      ...get(bridgeStateAtom),
      processing,
    });
  },
);

export const setTxHashAtom = atom(null, (get, set, txHash: string | null) => {
  set(bridgeStateAtom, {
    ...get(bridgeStateAtom),
    txHash,
  });
});

// Swap chains action
export const swapChainsAtom = atom(null, (get, set) => {
  const state = get(bridgeStateAtom);
  const {fromChainId, toChainId} = state;

  set(bridgeStateAtom, {
    ...state,
    fromChainId: toChainId,
    toChainId: fromChainId,
    // Reset amount when swapping chains
    bridgeAmount: '',
  });
});

// Reset bridge state
export const resetBridgeAtom = atom(null, (get, set) => {
  const state = get(bridgeStateAtom);
  // Keep chain connections but reset bridging data
  set(bridgeStateAtom, {
    ...state,
    bridgeAmount: initBridgeAmount,
    processing: false,
    error: null,
    txHash: null,
  });
});
