import {atom} from 'jotai';
import storage from '../Storage';

const STORAGE_KEY = 'showAssetVisible';

const initial = (() => {
  const stored = storage.getString(STORAGE_KEY);
  if (stored !== undefined) return stored === 'true';
  return true;
})();

export const assetVisibleAtom = atom<boolean>(initial);

export const assetVisibleWithStorageAtom = atom(
  get => get(assetVisibleAtom),
  (get, set, nextValue: boolean) => {
    set(assetVisibleAtom, nextValue);
    storage.set(STORAGE_KEY, nextValue ? 'true' : 'false');
  },
);
