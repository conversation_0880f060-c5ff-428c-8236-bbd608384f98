export function parseEMVCoQRCode(qrString: string): Record<string, string> {
  let i = 0;
  const result: Record<string, string> = {};

  while (i < qrString.length - 4) {
    // 保留最后4位做CRC校验
    const tag = qrString.substring(i, i + 2);
    const length = parseInt(qrString.substring(i + 2, i + 4), 10);
    const value = qrString.substring(i + 4, i + 4 + length);

    result[tag] = value;

    i += 4 + length;
  }

  return result;
}

export function isLikelyPayNowQR(qrData: string): boolean {
  const paynowAIDRegex = /A000000727/;
  const sgDomainRegex = /SG\.PAYNOW/;
  const countryCodeRegex = /5802SG/;
  const crcRegex = /6304[a-fA-F0-9]{4}/;

  return (
    (paynowAIDRegex.test(qrData) || sgDomainRegex.test(qrData)) &&
    countryCodeRegex.test(qrData) &&
    crcRegex.test(qrData)
  );
}
