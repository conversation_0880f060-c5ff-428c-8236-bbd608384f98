export const idlFactory = ({ IDL }) => {
  const Subaccount = IDL.Vec(IDL.Nat8);
  const PrincipalTxRecord = IDL.Record({
    transaction_id: IDL.Nat64,
    signature: IDL.Text,
    timestamp: IDL.Nat64,
    solana_address: IDL.Text,
    amount: IDL.Nat64,
  });
  return IDL.Service({
    bridge_to_solana: IDL.Func(
      [IDL.Nat64, IDL.Text],
      [
        IDL.Variant({
          Ok: IDL.Tuple(IDL.Nat64, IDL.Text),
          Err: IDL.Text,
        }),
      ],
      []
    ),
    deposit_to_pool: IDL.Func(
      [
        IDL.Opt(Subaccount),
        IDL.Nat64,
        IDL.Opt(Subaccount),
        IDL.Opt(IDL.Vec(IDL.Nat8)),
      ],
      [IDL.Variant({ Ok: IDL.Nat64, Err: IDL.Text })],
      []
    ),
    get_canister_id: IDL.Func([], [IDL.Text], []),
    get_decimals: IDL.Func([], [IDL.Nat8], ["query"]),
    get_pool_balance: IDL.Func([], [IDL.Nat64], []),
    get_principal_tx_history: IDL.Func(
      [],
      [IDL.Vec(PrincipalTxRecord)],
      ["query"]
    ),
    get_rpc_urls: IDL.Func([], [IDL.Vec(IDL.Text)], ["query"]),
    get_solana_transaction: IDL.Func(
      [IDL.Text],
      [
        IDL.Variant({
          Ok: IDL.Tuple(IDL.Nat, IDL.Text),
          Err: IDL.Text,
        }),
      ],
      []
    ),
    get_token_authorized: IDL.Func(
      [IDL.Nat],
      [IDL.Variant({ Ok: IDL.Nat, Err: IDL.Text })],
      []
    ),
    get_user_balance: IDL.Func([IDL.Principal], [IDL.Nat64], []),
    mint_tokens: IDL.Func(
      [IDL.Text],
      [
        IDL.Variant({
          Ok: IDL.Tuple(IDL.Nat, IDL.Nat, IDL.Text),
          Err: IDL.Text,
        }),
      ],
      []
    ),
    schnorr_public_key: IDL.Func(
      [],
      [
        IDL.Variant({
          Ok: IDL.Record({ public_key_hex: IDL.Text }),
          Err: IDL.Text,
        }),
      ],
      []
    ),
    set_rpc_urls: IDL.Func(
      [IDL.Vec(IDL.Text)],
      [IDL.Variant({ Ok: IDL.Null, Err: IDL.Text })],
      []
    ),
    solana_address: IDL.Func(
      [],
      [IDL.Variant({ Ok: IDL.Text, Err: IDL.Text })],
      []
    ),
  });
};
export const init = ({ IDL }) => {
  return [];
};
