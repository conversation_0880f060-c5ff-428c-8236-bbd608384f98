import type { Principal } from "@dfinity/principal";
import type { ActorMethod } from "@dfinity/agent";
import type { IDL } from "@dfinity/candid";

export interface Account {
  owner: Principal;
  subaccount: [] | [Subaccount];
}
export type Memo = Uint8Array | number[];
export interface PrincipalTxRecord {
  transaction_id: bigint;
  signature: string;
  timestamp: bigint;
  solana_address: string;
  amount: bigint;
}
export type Subaccount = Uint8Array | number[];
export type TransferError =
  | {
      GenericError: { message: string; error_code: bigint };
    }
  | { TemporarilyUnavailable: null }
  | { BadBurn: { min_burn_amount: bigint } }
  | { Duplicate: { duplicate_of: bigint } }
  | { BadFee: { expected_fee: bigint } }
  | { CreatedInFuture: { ledger_time: bigint } }
  | { TooOld: null }
  | { InsufficientFunds: { balance: bigint } };
export interface TransferFromArgs {
  to: Account;
  fee: [] | [bigint];
  spender_subaccount: [] | [Subaccount];
  from: Account;
  memo: [] | [Memo];
  created_at_time: [] | [bigint];
  amount: bigint;
}
export interface UserBalance {
  amount: bigint;
}
export interface _SERVICE {
  bridge_to_solana: ActorMethod<
    [bigint, string],
    { Ok: [bigint, string] } | { Err: string }
  >;
  deposit_to_pool: ActorMethod<
    [
      [] | [Subaccount],
      bigint,
      [] | [Subaccount],
      [] | [Uint8Array | number[]]
    ],
    { Ok: bigint } | { Err: string }
  >;
  get_canister_id: ActorMethod<[], string>;
  get_decimals: ActorMethod<[], number>;
  get_pool_balance: ActorMethod<[], bigint>;
  get_principal_tx_history: ActorMethod<[], Array<PrincipalTxRecord>>;
  get_rpc_urls: ActorMethod<[], Array<string>>;
  get_solana_transaction: ActorMethod<
    [string],
    { Ok: [bigint, string] } | { Err: string }
  >;
  get_token_authorized: ActorMethod<[bigint], { Ok: bigint } | { Err: string }>;
  get_user_balance: ActorMethod<[Principal], bigint>;
  mint_tokens: ActorMethod<
    [string],
    { Ok: [bigint, bigint, string] } | { Err: string }
  >;
  schnorr_public_key: ActorMethod<
    [],
    { Ok: { public_key_hex: string } } | { Err: string }
  >;
  set_rpc_urls: ActorMethod<[Array<string>], { Ok: null } | { Err: string }>;
  solana_address: ActorMethod<[], { Ok: string } | { Err: string }>;
}
export declare const idlFactory: IDL.InterfaceFactory;
export declare const init: (args: { IDL: typeof IDL }) => IDL.Type[];
