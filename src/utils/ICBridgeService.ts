import {Actor, ActorSubclass, HttpAgent} from '@dfinity/agent';
import {Principal} from '@dfinity/principal';
import {idlFactory as bridgeIdlFactory} from './bridge-idl/icp-bridge-backend.did';
import {idlFactory as idlFactoryICRC} from './icrc1_ledger_canister_backend';
import {getAssociatedTokenAddress} from '@solana/spl-token';
import {PublicKey} from '@solana/web3.js';
import {MINT_ADDRESS} from './SolanaBridgeService';
import {UnfinishedTransactionData} from '../types/bridge';
import Config from 'react-native-config';

// Canister ID
// const BRIDGE_CANISTER_ID = "hybaz-7qaaa-aaaam-aegwq-cai";
const BRIDGE_CANISTER_ID = Config.VITE_APP_BRIDGE_ICP_POOL;
// vUSD canister ID
// const ICRC_CANISTER_ID = "hrclf-jyaaa-aaaam-aegxa-cai";
// const ICRC_CANISTER_ID = "4x2jw-rqaaa-aaaak-qufjq-cai";
const ICRC_CANISTER_ID = Config.VITE_APP_BRIDGE_ICP_VUSD;

export class ICBridgeService {
  private bridgeActor: ActorSubclass;
  private icrcActor: ActorSubclass;

  constructor(private agent: HttpAgent, private principalId: string) {
    this.bridgeActor = this.createBridgeActor();
    this.icrcActor = this.createICRCActor();
  }

  static getInstance(agent: HttpAgent, principalId: string) {
    return new ICBridgeService(agent, principalId);
  }

  private createBridgeActor(): ActorSubclass {
    return Actor.createActor(bridgeIdlFactory as any, {
      agent: this.agent,
      canisterId: BRIDGE_CANISTER_ID,
    });
  }

  private createICRCActor(): ActorSubclass {
    return Actor.createActor(idlFactoryICRC as any, {
      agent: this.agent,
      canisterId: ICRC_CANISTER_ID,
    });
  }

  /**
   * 授权给处理账户
   * @param handlerAccount 处理账户的Principal ID
   * @param amount 授权金额
   */
  async approve(amount: number) {
    const handlerAccount = BRIDGE_CANISTER_ID;
    try {
      const principalOwner = Principal.fromText(handlerAccount);
      const mintResult = await this.icrcActor.icrc2_approve({
        spender: {
          owner: principalOwner,
          subaccount: [],
        },
        amount: amount,
        fee: [],
        memo: [],
        from_subaccount: [],
        created_at_time: [],
        expected_allowance: [],
        expires_at: [],
      });

      console.log(
        `Successfully approve to ${handlerAccount}. The amount is ${amount}. The TransactionID is: ${JSON.stringify(
          mintResult,
          (_, value) => (typeof value === 'bigint' ? value.toString() : value),
        )}`,
      );

      return mintResult;
    } catch (error) {
      console.error('Error in approve:', error);
      throw error;
    }
  }

  /**
   * 获取账户余额
   * @param owner 账户的Principal ID
   */
  async getBalance() {
    try {
      const principalOwner = Principal.fromText(this.principalId);
      const balance = await this.icrcActor.icrc1_balance_of({
        owner: principalOwner,
        subaccount: [],
      });
      console.log(`Balance: ${balance}`);
      return balance;
    } catch (error) {
      console.error('Error fetching balance:', error);
      throw error;
    }
  }

  /**
   * 检查授权
   * @param handlerAccount 处理账户的Principal ID
   * @param userAccount 用户账户的Principal ID
   */
  async checkAllowance(userAccount: string) {
    try {
      const handlerAccount = BRIDGE_CANISTER_ID;
      const handlerAccountPrincipal = Principal.fromText(handlerAccount);
      const userAccountPrincipal = Principal.fromText(userAccount);

      const allowanceData = await this.icrcActor.icrc2_allowance({
        account: {owner: userAccountPrincipal, subaccount: []},
        spender: {owner: handlerAccountPrincipal, subaccount: []},
      });

      console.log(
        `Allowance of ${userAccount} are: ${JSON.stringify(
          allowanceData,
          (_, value) => (typeof value === 'bigint' ? value.toString() : value),
        )}`,
      );

      return allowanceData;
    } catch (error) {
      console.error('Error checking allowance:', error);
      throw error;
    }
  }

  /**
   * 存款到池
   * @param amount 存款金额
   */
  async deposit(amount: number) {
    try {
      const mintResult = await this.bridgeActor.deposit_to_pool(
        [],
        amount,
        [],
        [],
      );

      console.log(
        `Successfully Deposit to ICP Pool. The TransactionID is: ${JSON.stringify(
          mintResult,
          (_, value) => (typeof value === 'bigint' ? value.toString() : value),
        )}`,
      );

      return mintResult;
    } catch (error) {
      console.error('Error in deposit:', error);
      throw error;
    }
  }

  /**
   * 桥接到Solana
   * @param amount 桥接金额
   * @param solanaAddress Solana目标地址
   */
  async bridgeToSolana(
    amount: number,
    solanaAddress: string,
  ): Promise<{
    Ok?: {0: bigint; 1: string};
    Err?: string;
  }> {
    try {
      const tokenAccount = await getAssociatedTokenAddress(
        MINT_ADDRESS,
        new PublicKey(solanaAddress),
      );

      const result = await this.bridgeActor.bridge_to_solana(
        BigInt(amount),
        tokenAccount.toBase58(),
      );
      console.log('Bridge result:', result);
      return result as any;
    } catch (error) {
      console.error('Error bridging to Solana:', error);
      throw error;
    }
  }

  /**
   * 获取用户余额
   * @param userAddress 用户的Principal ID
   */
  async getUserBalance(userAddress: string) {
    try {
      const userPrincipal = Principal.fromText(userAddress);
      const balance = await this.bridgeActor.get_user_balance(userPrincipal);
      return balance;
    } catch (error) {
      console.error('Error getting user balance:', error);
      throw error;
    }
  }

  // TODO: 获取未完成的交易
  async getUnfinishedTransactions(): Promise<UnfinishedTransactionData[]> {
    const result = await this.bridgeActor.get_principal_tx_history();
    if (Array.isArray(result)) {
      return result as UnfinishedTransactionData[];
    } else {
      return [];
    }
  }

  // 检测交易是否完成
  async checkOnChainIDs(ids: bigint[]) {
    const numberIds = ids.map(id => Number(id));
    return fetch(`${Config.VITE_APP_BRIDGE_SERVICE}/check-onchainids`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({onchainIDs: numberIds}),
    }).then(res => res.json());
  }
}
