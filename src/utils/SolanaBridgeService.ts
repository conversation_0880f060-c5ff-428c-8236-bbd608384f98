import {
  Connection,
  PublicKey,
  Transaction,
  SYSVAR_RENT_PUBKEY,
} from '@solana/web3.js';
import {Program, BN, web3} from '@project-serum/anchor';
import {getAssociatedTokenAddress, TOKEN_PROGRAM_ID} from '@solana/spl-token';
import {DevSolanaVaultIDL, ProdSolanaVaultIDL} from '../consts';
import {ContinueBridgeFromICPData} from '../types/bridge';
import Config from 'react-native-config';
import CryptoJS from 'crypto-js';
import {createAssociatedTokenAccountInstruction} from '@solana/spl-token';

// 常量定义
// const PROGRAM_ID = new PublicKey(
//   // "7Sg2fBM9J769SDSWeeCcC2UfbuZatdPwJqqMjFcNoE5S"
//   "2DZsuSS9Ywf4tDS3b2r1cxBHqwkorncmMhanvcb8bNJ4"
// );

const isProd = 'true';

const IDL = isProd ? ProdSolanaVaultIDL : DevSolanaVaultIDL;

export const PROGRAM_ID = new PublicKey(Config.VITE_APP_SOLANA_PROGRAM);

// USDC address
export const MINT_ADDRESS = new PublicKey(
  Config.VITE_APP_BRIDGE_SOLANA_USDC_MINT,
);

// 钱包接口定义
export interface SolanaWallet {
  publicKey: PublicKey;
  signTransaction: (transaction: Transaction) => Promise<Transaction>;
}

/**
 * Solana桥接服务类
 * 提供从Solana到ICP以及从ICP到Solana的桥接功能
 */
export class SolanaBridgeService {
  private program: Program;

  /**
   * 创建Solana桥接服务实例
   * @param connection Solana网络连接
   * @param wallet 用户钱包
   */
  constructor(private connection: Connection, private wallet: SolanaWallet) {
    // 确保钱包已连接
    if (!this.wallet.publicKey) {
      throw new Error('Wallet not connected');
    }

    // 创建程序实例
    const provider = {
      connection: this.connection,
      publicKey: this.wallet.publicKey,
      signTransaction: this.wallet.signTransaction,
    };

    this.program = new Program(IDL as any, PROGRAM_ID, provider as any);
  }

  /**
   * 创建从Solana到ICP的桥接存款交易
   * @param amount 存款金额
   * @param icpAddress ICP目标地址
   * @returns 交易签名
   */
  async createBridgeDeposit(
    amount: number,
    icpAddress: string,
  ): Promise<string> {
    try {
      const depositAmount = new BN(amount);

      // 生成必要的PDA地址
      const [vaultPDA] = await PublicKey.findProgramAddressSync(
        [Buffer.from('vault'), MINT_ADDRESS.toBuffer()],
        this.program.programId,
      );

      const [vaultAuthPDA] = await PublicKey.findProgramAddressSync(
        [Buffer.from('authority'), vaultPDA.toBuffer()],
        this.program.programId,
      );

      const [vaultTokenPDA] = await PublicKey.findProgramAddressSync(
        [Buffer.from('tokens'), vaultPDA.toBuffer()],
        this.program.programId,
      );

      // 获取用户的代币账户
      const userTokenAccount = await getAssociatedTokenAddress(
        MINT_ADDRESS,
        this.wallet.publicKey,
      );

      const [configPDA] = await PublicKey.findProgramAddressSync(
        [Buffer.from('config')],
        this.program.programId,
      );

      // 创建交易指令
      const instruction = await this.program.methods
        .deposit(depositAmount, icpAddress)
        .accounts(
          isProd
            ? {
                depositor: this.wallet.publicKey,
                depositorTokenAccount: userTokenAccount,
                mint: MINT_ADDRESS,
                vault: vaultPDA,
                vaultAuthority: vaultAuthPDA,
                config: configPDA,
                vaultTokenAccount: vaultTokenPDA,
                tokenProgram: TOKEN_PROGRAM_ID,
              }
            : {
                depositor: this.wallet.publicKey,
                depositorTokenAccount: userTokenAccount,
                mint: MINT_ADDRESS,
                vault: vaultPDA,
                vaultAuthority: vaultAuthPDA,
                vaultTokenAccount: vaultTokenPDA,
                tokenProgram: TOKEN_PROGRAM_ID,
              },
        )
        .instruction();

      // 创建交易
      const transaction = new Transaction().add(instruction);
      transaction.feePayer = this.wallet.publicKey;

      // 获取最新的 blockhash
      const latestBlockhash = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = latestBlockhash.blockhash;

      // 让钱包签名交易
      const signedTransaction = await this.wallet.signTransaction(transaction);

      // 发送交易
      const signature = await this.connection.sendRawTransaction(
        signedTransaction.serialize(),
      );

      // // 等待交易确认
      // await this.connection.confirmTransaction({
      //   signature,
      //   blockhash: latestBlockhash.blockhash,
      //   lastValidBlockHeight: latestBlockhash.lastValidBlockHeight,
      // });

      return signature;
    } catch (error) {
      console.error('Error in createBridgeDeposit:', error);
      throw error;
    }
  }

  /**
   * 创建从ICP到Solana的代币提取交易
   * @param amount 提取金额
   * @param onChainId ICP上的交易ID
   * @param signature ICP桥接的签名数据
   * @returns 交易签名
   */
  async createClaimFromICP(
    amount: number,
    onChainId: string,
    signature: string,
  ): Promise<string> {
    try {
      const claimAmount = new BN(amount);

      // 生成必要的PDA地址
      const [vaultPDA] = PublicKey.findProgramAddressSync(
        [Buffer.from('vault'), MINT_ADDRESS.toBuffer()],
        this.program.programId,
      );

      const [vaultAuthPDA] = PublicKey.findProgramAddressSync(
        [Buffer.from('authority'), vaultPDA.toBuffer()],
        this.program.programId,
      );

      const [vaultTokenPDA] = PublicKey.findProgramAddressSync(
        [Buffer.from('tokens'), vaultPDA.toBuffer()],
        this.program.programId,
      );

      // 计算 storage PDA
      const storageIndex = Math.floor(parseInt(onChainId) / 8192);
      const storageIndexBuffer = Buffer.alloc(8);
      storageIndexBuffer.writeBigUInt64LE(BigInt(storageIndex));

      // 使用sha256哈希
      // const crypto = require("crypto");
      // const hashResult = crypto
      //   .createHash("sha256")
      //   .update(storageIndexBuffer)
      //   .digest();
      // const hashBuffer = await crypto.subtle.digest(
      //   'SHA-256',
      //   storageIndexBuffer,
      // );

      // const hashResult = new Uint8Array(hashBuffer);
      // const hashBytes = Buffer.from(hashResult.slice(0, 7));
      // 使用 CryptoJS 进行 SHA-256 哈希
      const hashResult = CryptoJS.SHA256(
        CryptoJS.lib.WordArray.create(storageIndexBuffer),
      );
      const hashBytes = Buffer.from(
        hashResult.toString(CryptoJS.enc.Hex),
        'hex',
      ).slice(0, 7);

      const [storagePDA] = PublicKey.findProgramAddressSync(
        [Buffer.from('storage'), vaultPDA.toBuffer(), hashBytes],
        this.program.programId,
      );

      // 检查 storage 账户是否存在
      const storageAccount = await this.connection.getAccountInfo(storagePDA);

      // 如果 storage 账户不存在，先初始化它
      if (!storageAccount) {
        console.log('Initializing storage account...');
        const initInstruction = await this.program.methods
          .initializeStorage(new BN(onChainId))
          .accounts({
            initializer: this.wallet.publicKey,
            mint: MINT_ADDRESS,
            vault: vaultPDA,
            storage: storagePDA,
            systemProgram: web3.SystemProgram.programId,
            rent: SYSVAR_RENT_PUBKEY,
          })
          .instruction();

        // 创建初始化交易
        const initTransaction = new Transaction().add(initInstruction);
        initTransaction.feePayer = this.wallet.publicKey;

        // 获取最新的 blockhash
        const latestBlockhash = await this.connection.getLatestBlockhash();
        initTransaction.recentBlockhash = latestBlockhash.blockhash;

        // 让钱包签名交易
        const signedInitTransaction = await this.wallet.signTransaction(
          initTransaction,
        );

        // 发送交易
        const initSignature = await this.connection.sendRawTransaction(
          signedInitTransaction.serialize(),
        );

        // 等待交易确认
        // await this.connection.confirmTransaction({
        //   signature: initSignature,
        //   blockhash: latestBlockhash.blockhash,
        //   lastValidBlockHeight: latestBlockhash.lastValidBlockHeight,
        // });

        console.log('Storage initialized:', initSignature);
      }

      // 获取 config PDA
      const [configPDA] = await PublicKey.findProgramAddressSync(
        [Buffer.from('config')],
        this.program.programId,
      );

      // 获取接收者的 token account
      const receiverTokenAccount = await getAssociatedTokenAddress(
        MINT_ADDRESS,
        this.wallet.publicKey,
      );

      // 创建交易对象
      const transaction = new Transaction();

      // 检查 token account 是否存在
      const tokenAccountInfo = await this.connection.getAccountInfo(
        receiverTokenAccount,
      );

      // 如果 token account 不存在，添加创建指令
      if (!tokenAccountInfo) {
        console.log('Creating token account...');

        // 创建 ATA 指令
        const createATAInstruction = createAssociatedTokenAccountInstruction(
          this.wallet.publicKey, // payer
          receiverTokenAccount, // associatedToken
          this.wallet.publicKey, // owner
          MINT_ADDRESS, // mint
        );

        // 添加创建 token account 指令
        transaction.add(createATAInstruction);
      }

      // let fee_receiver = new PublicKey(
      //   import.meta.env.VITE_APP_BRIDGE_SOLANA_FEE_RECEIVER
      // );

      // const feeReceiverTokenAccount = await getAssociatedTokenAddress(
      //   MINT_ADDRESS,
      //   fee_receiver,
      //   true // allowOwnerOffCurve 设置为 true 以允许非曲线上的地址
      // );

      // 创建提款指令
      const withdrawInstruction = await this.program.methods
        .withdraw(new BN(onChainId), claimAmount, signature)
        .accounts(
          isProd
            ? {
                receiver: this.wallet.publicKey,
                receiverTokenAccount: receiverTokenAccount,
                mint: MINT_ADDRESS,
                vault: vaultPDA,
                vaultAuthority: vaultAuthPDA,
                vaultTokenAccount: vaultTokenPDA,
                storage: storagePDA,
                config: configPDA,
                tokenProgram: TOKEN_PROGRAM_ID,
                // feeReceiverTokenAccount: feeReceiverTokenAccount,
              }
            : {
                receiver: this.wallet.publicKey,
                receiverTokenAccount: receiverTokenAccount,
                mint: MINT_ADDRESS,
                vault: vaultPDA,
                vaultAuthority: vaultAuthPDA,
                vaultTokenAccount: vaultTokenPDA,
                storage: storagePDA,
                config: configPDA,
                tokenProgram: TOKEN_PROGRAM_ID,
              },
        )
        .instruction();

      // 添加提款指令
      transaction.add(withdrawInstruction);
      transaction.feePayer = this.wallet.publicKey;

      // 获取最新的 blockhash
      const {blockhash} = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;

      // 让钱包签名交易
      const signedTransaction = await this.wallet.signTransaction(transaction);

      // 发送交易
      const txSignature = await this.connection.sendRawTransaction(
        signedTransaction.serialize(),
      );

      // 等待交易确认
      // await this.connection.confirmTransaction({
      //   signature: txSignature,
      //   blockhash,
      //   lastValidBlockHeight,
      // });
      return txSignature;
    } catch (error) {
      console.error('Error in createClaimFromICP:', error);
      throw error;
    }
  }
  async executeInOrder(promiseFuncs: (() => Promise<void>)[]) {
    for (const func of promiseFuncs) {
      await func(); // 严格按数组顺序等待执行
    }
  }
  /**
   * 继续进行ICP到Solana中断的交易
   * @param amount 提取金额
   * @param onChainId ICP上的交易ID
   * @param signature ICP桥接的签名数据
   * @returns 交易签名
   */
  async continueBridgeFromICP(data: ContinueBridgeFromICPData[]) {
    try {
      const len = data?.length || 0;
      if (!len) {
        throw new Error('data is empty');
      }
      // 获取接收者的 token account
      const receiverTokenAccount = await getAssociatedTokenAddress(
        MINT_ADDRESS,
        this.wallet.publicKey,
      );

      // 创建交易对象
      let transaction = new Transaction();

      // 检查 token account 是否存在
      const tokenAccountInfo = await this.connection.getAccountInfo(
        receiverTokenAccount,
      );

      // 如果 token account 不存在，添加创建指令
      if (!tokenAccountInfo) {
        console.log('Creating token account...');

        // 创建 ATA 指令
        const createATAInstruction = createAssociatedTokenAccountInstruction(
          this.wallet.publicKey, // payer
          receiverTokenAccount, // associatedToken
          this.wallet.publicKey, // owner
          MINT_ADDRESS, // mint
        );

        // 添加创建 token account 指令
        transaction.add(createATAInstruction);
      }

      const transQueue = [...data].map((item, index) => {
        return async () => {
          try {
            const {onChainId, amount, signature} = item;
            const claimAmount = new BN(amount);
            // 生成必要的PDA地址
            const [vaultPDA] = PublicKey.findProgramAddressSync(
              [Buffer.from('vault'), MINT_ADDRESS.toBuffer()],
              this.program.programId,
            );

            const [vaultAuthPDA] = PublicKey.findProgramAddressSync(
              [Buffer.from('authority'), vaultPDA.toBuffer()],
              this.program.programId,
            );

            const [vaultTokenPDA] = PublicKey.findProgramAddressSync(
              [Buffer.from('tokens'), vaultPDA.toBuffer()],
              this.program.programId,
            );

            // 计算 storage PDA
            const storageIndex = Math.floor(parseInt(onChainId) / 8192);
            const storageIndexBuffer = Buffer.alloc(8);
            storageIndexBuffer.writeBigUInt64LE(BigInt(storageIndex));

            // const hashBuffer = await crypto.subtle.digest(
            //   'SHA-256',
            //   storageIndexBuffer,
            // );
            // const hashResult = new Uint8Array(hashBuffer);
            // const hashBytes = Buffer.from(hashResult.slice(0, 7));
            const hashResult = CryptoJS.SHA256(
              CryptoJS.lib.WordArray.create(storageIndexBuffer),
            );
            const hashBytes = Buffer.from(
              hashResult.toString(CryptoJS.enc.Hex),
              'hex',
            ).slice(0, 7);

            const [storagePDA] = PublicKey.findProgramAddressSync(
              [Buffer.from('storage'), vaultPDA.toBuffer(), hashBytes],
              this.program.programId,
            );

            // 获取 config PDA
            const [configPDA] = await PublicKey.findProgramAddressSync(
              [Buffer.from('config')],
              this.program.programId,
            );
            // 创建提款指令
            const withdrawInstruction = await this.program.methods
              .withdraw(new BN(onChainId), claimAmount, signature)
              .accounts({
                receiver: this.wallet.publicKey,
                receiverTokenAccount: receiverTokenAccount,
                mint: MINT_ADDRESS,
                vault: vaultPDA,
                vaultAuthority: vaultAuthPDA,
                vaultTokenAccount: vaultTokenPDA,
                storage: storagePDA,
                config: configPDA,
                tokenProgram: TOKEN_PROGRAM_ID,
              })
              .instruction();

            // 添加提款指令
            transaction.add(withdrawInstruction);
            console.info('add success----', onChainId);

            // 每满三笔交易发起一次交易
            if (transaction.instructions?.length === 3 || index === len - 1) {
              transaction.feePayer = this.wallet.publicKey;

              console.info(
                '发起交易前的transaction数量------',
                transaction.instructions.length,
              );
              // 获取最新的 blockhash
              const {blockhash} = await this.connection.getLatestBlockhash();
              transaction.recentBlockhash = blockhash;

              // 让钱包签名交易
              const signedTransaction = await this.wallet.signTransaction(
                transaction,
              );

              // 发送交易
              const signature = await this.connection.sendRawTransaction(
                signedTransaction.serialize(),
              );
              console.log('continue-发送交易后获得的signature----', signature);
              transaction = new Transaction();
              console.info(
                '发起交易后的transaction数量------',
                transaction.instructions.length,
              );
            }
          } catch (err) {
            throw err;
          }
        };
      });

      await this.executeInOrder(transQueue);
    } catch (error) {
      console.error('Error in createClaimFromICP:', error);
      throw error;
    }
  }

  /**
   * 获取交易状态
   * @param signature 交易签名
   * @returns 交易状态
   */
  async getTransactionStatus(signature: string) {
    try {
      return await this.connection.getSignatureStatus(signature);
    } catch (error) {
      console.error('Error getting transaction status:', error);
      throw error;
    }
  }

  /**
   * 获取钱包余额
   * @returns 钱包SOL余额（以SOL为单位）
   */
  async getWalletBalance(): Promise<string> {
    try {
      const balance = await this.connection.getBalance(this.wallet.publicKey);
      // 将lamports转换为SOL (1 SOL = 1,000,000,000 lamports)
      const solBalance = balance / 1_000_000_000;
      return solBalance.toFixed(9); // 返回9位小数的字符串
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      throw error;
    }
  }

  /**
   * 估算交易的gas费用
   *
   * @param transactionType - 交易类型: 'deposit' 或 'claim'
   * @returns 估算的gas费用(以SOL为单位)的字符串表示
   */
  async estimateGasFee(
    transactionType: 'deposit' | 'claim' = 'deposit',
  ): Promise<string> {
    try {
      // 创建一个模拟交易来估算费用
      const transaction = new Transaction();

      // 根据交易类型添加预计的指令数量
      // deposit: 1个主要指令
      // claim: 1个主要指令 + 1个可能的初始化指令
      const numInstructions = transactionType === 'deposit' ? 1 : 2;

      // 添加模拟指令
      for (let i = 0; i < numInstructions; i++) {
        transaction.add({
          keys: [
            {pubkey: this.wallet.publicKey, isSigner: true, isWritable: true},
          ],
          programId: PROGRAM_ID,
          data: Buffer.from([]),
        });
      }

      // 获取最新的区块哈希
      const {blockhash} = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = this.wallet.publicKey;

      // 获取交易费用估算
      const fee = await this.connection.getFeeForMessage(
        transaction.compileMessage(),
        'confirmed',
      );

      if (!fee || fee.value === null) {
        throw new Error('Failed to estimate fee');
      }

      // 转换为SOL(1 SOL = 1,000,000,000 lamports)
      let totalFeeInSol = fee.value / 1_000_000_000;

      // 对于claim交易类型，检查是否需要创建token账户
      if (transactionType === 'claim') {
        try {
          // 获取接收者的 token account
          const receiverTokenAccount = await getAssociatedTokenAddress(
            MINT_ADDRESS,
            this.wallet.publicKey,
          );

          // 检查token account是否存在
          const tokenAccountInfo = await this.connection.getAccountInfo(
            receiverTokenAccount,
          );

          // 如果token account不存在，添加创建token account的费用
          if (!tokenAccountInfo) {
            // 创建USDC token account的固定费用约为0.******** SOL
            totalFeeInSol += 0.********;
          }
        } catch (error) {
          console.error('Error checking token account:', error);
          // 如果出错，为安全起见，添加token account创建费用
          totalFeeInSol += 0.********;
        }
      }

      // 格式化为最多7位小数
      return totalFeeInSol.toFixed(7);
    } catch (error) {
      console.error('Error estimating gas fee:', error);
      // 如果估算失败，返回默认费用，包含可能的token account创建费用
      return transactionType === 'claim' ? '0.002044' : '0.000005';
    }
  }

  /**
   * 获取代币余额
   * @returns 代币余额
   */
  async getTokenBalance(): Promise<number> {
    try {
      const tokenAccount = await getAssociatedTokenAddress(
        MINT_ADDRESS,
        this.wallet.publicKey,
      );

      try {
        const balance = await this.connection.getTokenAccountBalance(
          tokenAccount,
        );
        return Number(balance.value.amount);
        // return (
        //   Number(balance.value.amount) / Math.pow(10, balance.value.decimals)
        // );
      } catch (e) {
        // 账户可能不存在
        return 0;
      }
    } catch (error) {
      console.error('Error getting token balance:', error);
      throw error;
    }
  }

  /**
   * 通知后端处理交易
   * @param txHash 交易哈希
   * @returns 后端响应
   */
  async notifyBackend(txHash: string): Promise<any> {
    try {
      const response = await fetch(
        `${Config.VITE_APP_BRIDGE_SERVICE}/process-transaction`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({txHash}),
        },
      );

      if (!response.ok) {
        throw new Error(
          `Backend notification failed: ${response.status} ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error('Error notifying backend:', error);
      throw error;
    }
  }

  /**
   * 轮询后端直到交易处理成功
   * @param txHash 交易哈希
   * @param timeoutMs 超时时间(毫秒)，默认5分钟
   * @param intervalMs 轮询间隔(毫秒)，默认2秒
   * @returns 最终的后端响应
   */
  async pollBackendUntilProcessed(
    txHash: string,
    timeoutMs: number = 5 * 60 * 1000,
    intervalMs: number = 1000,
  ): Promise<any> {
    const startTime = Date.now();

    // 创建一个休眠函数
    const sleep = (ms: number) =>
      new Promise(resolve => setTimeout(resolve, ms));

    while (Date.now() - startTime < timeoutMs) {
      try {
        // 发送查询请求
        const response = await this.notifyBackend(txHash);

        // 检查是否得到了期望的成功消息
        if (
          response.success &&
          response.message === 'Transaction already processed successfully'
        ) {
          return response;
        }

        // // 如果是第一次处理成功但没有message字段，也算成功
        // if (response.success && response.txID && !response.message) {
        //   return response;
        // }

        // 等待指定的间隔时间
        await sleep(intervalMs);
      } catch (error) {
        console.error('Error during polling:', error);
        // 出错后继续尝试
        await sleep(intervalMs);
      }
    }

    throw new Error(
      `Polling timed out after ${
        timeoutMs / 1000
      } seconds. Transaction not confirmed.`,
    );
  }
}
