<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_4_9)">
<circle cx="25" cy="25" r="16" fill="#0F0F0F"/>
<circle cx="25" cy="25" r="16.2" stroke="#393939" stroke-width="0.4"/>
</g>
<path d="M30 23L25 28L20 23" stroke="#393939" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<filter id="filter0_d_4_9" x="0.6" y="0.599998" width="48.8" height="48.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4_9"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4_9" result="shape"/>
</filter>
</defs>
</svg>
