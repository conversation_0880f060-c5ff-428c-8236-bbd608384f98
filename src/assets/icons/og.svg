<svg width="72" height="25" viewBox="0 0 72 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect y="25" width="25" height="72" rx="4.21513" transform="rotate(-90 0 25)" fill="url(#paint0_linear_73_348)" fill-opacity="0.15"/>
<rect x="0.14245" y="24.8575" width="24.7151" height="71.7151" rx="4.07268" transform="rotate(-90 0.14245 24.8575)" stroke="url(#paint1_linear_73_348)" stroke-opacity="0.28" stroke-width="0.284901"/>
<rect x="4" y="12.4397" width="15.5957" height="15.5957" rx="3.65906" transform="rotate(-45 4 12.4397)" fill="url(#paint2_linear_73_348)" fill-opacity="0.15"/>
<rect x="4.23521" y="12.4397" width="15.2631" height="15.2631" rx="3.49274" transform="rotate(-45 4.23521 12.4397)" stroke="url(#paint3_linear_73_348)" stroke-opacity="0.28" stroke-width="0.332642"/>
<g filter="url(#filter0_ddi_73_348)">
<g clip-path="url(#paint4_diamond_73_348_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0067909 -0.0130285 0.0130285 0.0067909 12.3138 18.1044)"><rect x="0" y="0" width="837.355" height="424.48" fill="url(#paint4_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="837.355" height="424.48" transform="scale(1 -1)" fill="url(#paint4_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="837.355" height="424.48" transform="scale(-1 1)" fill="url(#paint4_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="837.355" height="424.48" transform="scale(-1)" fill="url(#paint4_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/></g></g><rect x="8.7041" y="12.4396" width="8.94287" height="8.94287" rx="1.99585" transform="rotate(-45 8.7041 12.4396)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.63921570777893066,&#34;g&#34;:0.28627452254295349,&#34;b&#34;:0.93725490570068359,&#34;a&#34;:1.0},&#34;position&#34;:0.4218750},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.39550781250,&#34;b&#34;:0.90328133106231689,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:13.581797599792480,&#34;m01&#34;:26.056980133056641,&#34;m02&#34;:-7.5055837631225586,&#34;m10&#34;:-26.056980133056641,&#34;m11&#34;:13.581797599792480,&#34;m12&#34;:24.342027664184570},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint5_diamond_73_348_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.00225277 -0.00663975 0.00351516 0.00119264 12.1228 11.9455)"><rect x="0" y="0" width="983.992" height="2517.09" fill="url(#paint5_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="983.992" height="2517.09" transform="scale(1 -1)" fill="url(#paint5_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="983.992" height="2517.09" transform="scale(-1 1)" fill="url(#paint5_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="983.992" height="2517.09" transform="scale(-1)" fill="url(#paint5_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/></g></g><rect x="8.7041" y="12.4396" width="8.94287" height="8.94287" rx="1.99585" transform="rotate(-45 8.7041 12.4396)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.6250,&#34;b&#34;:0.91750001907348633,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:4.5055403709411621,&#34;m01&#34;:7.0303230285644531,&#34;m02&#34;:6.3548483848571777,&#34;m10&#34;:-13.279491424560547,&#34;m11&#34;:2.3852875232696533,&#34;m12&#34;:17.392648696899414},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<rect x="8.7041" y="12.4396" width="8.94287" height="8.94287" rx="1.99585" transform="rotate(-45 8.7041 12.4396)" fill="url(#paint6_linear_73_348)"/>
</g>
<rect x="9.66211" y="12.4395" width="7.58828" height="7.58828" rx="1.33057" transform="rotate(-45 9.66211 12.4395)" fill="black" fill-opacity="0.2"/>
<g filter="url(#filter1_ddi_73_348)">
<g clip-path="url(#paint7_diamond_73_348_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.00416245 -0.00798575 0.00798575 0.00416245 13.3639 15.9117)"><rect x="0" y="0" width="855.284" height="442.41" fill="url(#paint7_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="855.284" height="442.41" transform="scale(1 -1)" fill="url(#paint7_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="855.284" height="442.41" transform="scale(-1 1)" fill="url(#paint7_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="855.284" height="442.41" transform="scale(-1)" fill="url(#paint7_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/></g></g><rect x="11.1514" y="12.4395" width="5.48149" height="5.48149" rx="0.997926" transform="rotate(-45 11.1514 12.4395)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.63921570777893066,&#34;g&#34;:0.28627452254295349,&#34;b&#34;:0.93725490570068359,&#34;a&#34;:1.0},&#34;position&#34;:0.4218750},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.39550781250,&#34;b&#34;:0.90328133106231689,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:8.3249006271362305,&#34;m01&#34;:15.971506118774414,&#34;m02&#34;:1.2157152891159058,&#34;m10&#34;:-15.971506118774414,&#34;m11&#34;:8.3249006271362305,&#34;m12&#34;:19.735006332397461},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint8_diamond_73_348_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.00138083 -0.0040698 0.0021546 0.000731025 13.2468 12.1366)"><rect x="0" y="0" width="1024.11" height="2592.87" fill="url(#paint8_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1024.11" height="2592.87" transform="scale(1 -1)" fill="url(#paint8_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1024.11" height="2592.87" transform="scale(-1 1)" fill="url(#paint8_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1024.11" height="2592.87" transform="scale(-1)" fill="url(#paint8_diamond_73_348)" opacity="1" shape-rendering="crispEdges"/></g></g><rect x="11.1514" y="12.4395" width="5.48149" height="5.48149" rx="0.997926" transform="rotate(-45 11.1514 12.4395)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.6250,&#34;b&#34;:0.91750001907348633,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:2.7616500854492188,&#34;m01&#34;:4.30920410156250,&#34;m02&#34;:9.7114028930664062,&#34;m10&#34;:-8.1396036148071289,&#34;m11&#34;:1.4620509147644043,&#34;m12&#34;:15.475416183471680},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<rect x="11.1514" y="12.4395" width="5.48149" height="5.48149" rx="0.997926" transform="rotate(-45 11.1514 12.4395)" fill="url(#paint9_linear_73_348)"/>
</g>
<path d="M39.3139 13.0482C39.3139 13.9573 39.152 14.7485 38.8281 15.4218C38.5043 16.0923 38.0582 16.6121 37.4901 16.9815C36.9247 17.3479 36.2741 17.5312 35.5384 17.5312C34.8026 17.5312 34.1506 17.3479 33.5824 16.9815C33.017 16.6121 32.5724 16.0923 32.2486 15.4218C31.9276 14.7485 31.767 13.9573 31.767 13.0482C31.767 12.1391 31.9276 11.3494 32.2486 10.6789C32.5724 10.0056 33.0185 9.48573 33.5866 9.11925C34.1548 8.74993 34.8054 8.56527 35.5384 8.56527C36.2741 8.56527 36.9247 8.74993 37.4901 9.11925C38.0582 9.48573 38.5043 10.0056 38.8281 10.6789C39.152 11.3494 39.3139 12.1391 39.3139 13.0482ZM38.5384 13.0482C38.5384 12.267 38.4077 11.5979 38.1463 11.0411C37.8849 10.4815 37.5284 10.0539 37.0767 9.75846C36.625 9.463 36.1122 9.31527 35.5384 9.31527C34.9673 9.31527 34.456 9.463 34.0043 9.75846C33.5526 10.0539 33.1946 10.48 32.9304 11.0369C32.669 11.5937 32.5384 12.2641 32.5384 13.0482C32.5384 13.8295 32.669 14.4985 32.9304 15.0553C33.1918 15.6121 33.5483 16.0397 34 16.338C34.4517 16.6335 34.9645 16.7812 35.5384 16.7812C36.1122 16.7812 36.625 16.6335 37.0767 16.338C37.5313 16.0425 37.8892 15.6164 38.1506 15.0596C38.4119 14.4999 38.5412 13.8295 38.5384 13.0482ZM47.2795 11.4119C47.1999 11.1221 47.0835 10.8508 46.93 10.5979C46.7766 10.3423 46.5863 10.1193 46.359 9.92891C46.1346 9.73573 45.8746 9.58516 45.5792 9.47721C45.2866 9.36925 44.9599 9.31527 44.5991 9.31527C44.0309 9.31527 43.5224 9.463 43.0735 9.75846C42.6246 10.0539 42.2695 10.48 42.0082 11.0369C41.7496 11.5937 41.6204 12.2641 41.6204 13.0482C41.6204 13.8295 41.7511 14.4985 42.0124 15.0553C42.2738 15.6121 42.6317 16.0397 43.0863 16.338C43.5437 16.6335 44.0664 16.7812 44.6545 16.7812C45.1914 16.7812 45.6658 16.6619 46.0778 16.4232C46.4897 16.1846 46.8107 15.8437 47.0408 15.4005C47.2738 14.9573 47.3874 14.4303 47.3817 13.8195L47.6374 13.9005H44.842V13.1846H48.1573V13.9005C48.1573 14.6448 48.0067 15.2883 47.7056 15.8309C47.4045 16.3735 46.9911 16.7925 46.4656 17.088C45.94 17.3835 45.3363 17.5312 44.6545 17.5312C43.8931 17.5312 43.2269 17.3479 42.6559 16.9815C42.0849 16.6121 41.6403 16.0923 41.3221 15.4218C41.0067 14.7485 40.8491 13.9573 40.8491 13.0482C40.8491 12.3636 40.94 11.7457 41.1218 11.1945C41.3036 10.6434 41.5607 10.1718 41.8931 9.77976C42.2283 9.38772 42.6246 9.088 43.082 8.88062C43.5394 8.67039 44.0451 8.56527 44.5991 8.56527C45.0763 8.56527 45.5124 8.64056 45.9073 8.79113C46.305 8.94169 46.6545 9.14908 46.9556 9.41329C47.2596 9.67465 47.5082 9.97721 47.7013 10.321C47.8974 10.6619 48.0295 11.0255 48.0977 11.4119H47.2795Z" fill="white"/>
<defs>
<filter id="filter0_ddi_73_348" x="3.96282" y="1.65465" width="22.13" height="22.1305" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.280325"/>
<feGaussianBlur stdDeviation="2.33847"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.843137 0 0 0 0 0.886275 0 0 0 0 0.92549 0 0 0 0.34 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_73_348"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.280325"/>
<feGaussianBlur stdDeviation="2.78421"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_73_348" result="effect2_dropShadow_73_348"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_73_348" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.0253"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_73_348"/>
</filter>
<clipPath id="paint4_diamond_73_348_clip_path"><rect x="8.7041" y="12.4396" width="8.94287" height="8.94287" rx="1.99585" transform="rotate(-45 8.7041 12.4396)"/></clipPath><clipPath id="paint5_diamond_73_348_clip_path"><rect x="8.7041" y="12.4396" width="8.94287" height="8.94287" rx="1.99585" transform="rotate(-45 8.7041 12.4396)"/></clipPath><filter id="filter1_ddi_73_348" x="5.99603" y="3.6887" width="18.0626" height="18.0621" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.280325"/>
<feGaussianBlur stdDeviation="2.33847"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.827451 0 0 0 0 0.87451 0 0 0 0 0.913725 0 0 0 0.39 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_73_348"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.280325"/>
<feGaussianBlur stdDeviation="2.78421"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_73_348" result="effect2_dropShadow_73_348"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_73_348" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.0253"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_73_348"/>
</filter>
<clipPath id="paint7_diamond_73_348_clip_path"><rect x="11.1514" y="12.4395" width="5.48149" height="5.48149" rx="0.997926" transform="rotate(-45 11.1514 12.4395)"/></clipPath><clipPath id="paint8_diamond_73_348_clip_path"><rect x="11.1514" y="12.4395" width="5.48149" height="5.48149" rx="0.997926" transform="rotate(-45 11.1514 12.4395)"/></clipPath><linearGradient id="paint0_linear_73_348" x1="11.0714" y1="60.873" x2="0.0882123" y2="60.873" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint1_linear_73_348" x1="25" y1="60.873" x2="10.2267" y2="60.873" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_73_348" x1="11.7979" y1="23.2909" x2="11.7979" y2="28.0354" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_73_348" x1="11.7979" y1="12.4397" x2="11.7979" y2="25.3062" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_diamond_73_348" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop offset="0.421875" stop-color="#A349EF"/>
<stop offset="1" stop-color="#FF65E6"/>
</linearGradient>
<linearGradient id="paint5_diamond_73_348" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9FEA"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_73_348" x1="17.308" y1="17.2735" x2="8.75304" y2="20.3185" gradientUnits="userSpaceOnUse">
<stop stop-color="#7A96AC"/>
<stop offset="0.18" stop-color="#EAEFF3"/>
<stop offset="0.315" stop-color="#C2D4E1"/>
<stop offset="0.491919" stop-color="white"/>
<stop offset="0.615" stop-color="#D4DEE5"/>
<stop offset="0.785" stop-color="#ABBDC8"/>
<stop offset="0.955" stop-color="#BCCAD7"/>
</linearGradient>
<linearGradient id="paint7_diamond_73_348" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop offset="0.421875" stop-color="#A349EF"/>
<stop offset="1" stop-color="#FF65E6"/>
</linearGradient>
<linearGradient id="paint8_diamond_73_348" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9FEA"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_73_348" x1="11.2884" y1="12.5537" x2="16.3359" y2="18.1493" gradientUnits="userSpaceOnUse">
<stop stop-color="#7A96AC"/>
<stop offset="0.18" stop-color="#EAEFF3"/>
<stop offset="0.315" stop-color="#C2D4E1"/>
<stop offset="0.491919" stop-color="white"/>
<stop offset="0.615" stop-color="#D4DEE5"/>
<stop offset="0.785" stop-color="#ABBDC8"/>
<stop offset="0.955" stop-color="#BCCAD7"/>
</linearGradient>
</defs>
</svg>
