import {
  Chain,
  ChainName,
  TokenSymbol,
  supported<PERSON>hai<PERSON><PERSON>tom,
} from './store/bridgeAtom';
import IconVLY from './assets/chains/vly.svg';
import IconSolana from './assets/chains/solana.svg';

export const SUPPORTED_CHAINS: Chain[] = [
  {
    id: 'icp',
    name: ChainName.ICP,
    icon: IconVLY,
    tokenSymbol: TokenSymbol.vUSD,
    decimals: 6,
  },
  {
    id: 'solana',
    name: ChainName.SOLANA,
    icon: IconSolana,
    tokenSymbol: TokenSymbol.USDC,
    decimals: 6,
  },
];
// TODO：临时改用测试环境，上线记得改回来
// test api
export const POS_SCAN_UNI_URL =
  'https://app-stage.uniwebpay.com/uni-service/api';

// prod api
// export const POS_SCAN_UNI_URL =
//   'https://uni-service.uniwebpay.com/uni-service/api';

export const DefaultDecimals = 6;
