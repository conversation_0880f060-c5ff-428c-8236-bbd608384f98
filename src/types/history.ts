export type HistoryResponse = {
  code: number;
  data: HistoryData;
  error: null;
  [property: string]: any;
};

export type HistoryData = {
  meta: HistoryMeta;
  transactions: HistoryTransaction[];
  [property: string]: any;
};

export type HistoryMeta = {
  account: string;
  limit: number;
  skip: number;
  token: string;
  total: number;
  [property: string]: any;
};

export type HistoryTransaction = {
  amount: string;
  datetime: string;
  fee: string;
  from: string;
  index: number;
  kind: string;
  spender: string;
  timestamp: number;
  to: string;
  token: string;
  token_name: string;
  [property: string]: any;
};
