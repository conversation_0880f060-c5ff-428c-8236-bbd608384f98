declare module '@sumsub/react-native-mobilesdk-module' {
  interface StatusEvent {
    prevStatus: string;
    newStatus: string;
  }

  interface LogEvent {
    message: string;
  }

  interface SNSMobileSDKType {
    init: (
      token: string,
      tokenExpirationHandler: () => Promise<string>,
    ) => SNSBuilder;
  }

  interface SNSBuilder {
    withHandlers: (handlers: {
      onStatusChanged?: (event: StatusEvent) => void;
      onLog?: (event: LogEvent) => void;
    }) => SNSBuilder;
    withDebug: (enabled: boolean) => SNSBuilder;
    withLocale: (locale: string) => SNSBuilder;
    build: () => SNSInstance;
  }

  interface SNSInstance {
    launch: () => Promise<any>;
  }

  const SNSMobileSDK: SNSMobileSDKType;
  export default SNSMobileSDK;
}
