export interface AuthSig {
  identityId: string;
  message: string;
  signature: string;
  expiresAt?: number;
}

export interface UserInfo {
  userName: string;
  displayName: string;
  passkeyName: string;
  principalId: Array<string>;
  publicKey: string;
  solanaAddress: Array<string>;
  createTime: number;
  modifyTime: number;
  expiration: number;
}

export interface UserProfile {
  createdAt: string;
  id: string;
  inviteCode: string;
  /**
   * 600为弱提示限额
   * 1000为强提示限额
   */
  limit: number;
  principal_id: string;
  updatedAt: string;
  username: string;
  /**
   * 0 未验证
   * 1 验证中
   * 2 验证通过
   * 3 验证失败
   */
  verified: 0 | 1 | 2 | 3;
  firstName?: string;
  lastName?: string;
}
