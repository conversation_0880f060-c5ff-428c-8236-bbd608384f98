{"send": "Send", "send1": "Send Failed", "receive": "Receive", "receiveToken": "Receive {{symbol}}", "sendToken": "Send {{symbol}}", "history": "History", "airdrop": "Airdrop", "airdropDesc": "Add card to get airdrop.", "loginButtonTitle": "Login with <PERSON><PERSON>", "loginTitle": "<PERSON><PERSON>", "registerWith": "Register with", "termsHint": "By proceeding you agree to Ontapay ", "terms": "Terms", "ofUse": " of Use", "signUpButtonTitle": "Sign up anonymously", "switchWallet": "Switch Wallet", "registerTitle": "Register with card", "registerFastTitle": "Register", "cardNumber": "CARD NUMBER", "empiryDate": "EMPIRY DATE", "cvvCode": "CVV CODE", "cardDesc": "Your security is our top priority. We are certified to the Payment Card Industry Data Security Standard (PCI DSS), ensuring the highest level of security for your sensitive information.", "addCard": "Add Card", "confirmTitle": "Confirm", "walletName": "Wallet Name", "creatWallet": "Create Wallet", "finishRegister": "Finish Register", "registerWithCard": "Register With Card", "cardList": "Card List", "addNewCard": "Add a new card", "addNewCardHint": "Debit and credit card supported", "noCardHint": "You do not have card at the moment", "noCameraHint": "Camera not available or not permitted", "scanHint": "Scan QR Code to pay", "unbindCard": "Unbind Card", "unbindCardHint": "Unbinding the card will also remove the card wallet. Do you want to continue?", "balance": "Balance", "fees": "Fees", "payButtonTitle": "PAY", "amount": "Amount", "pending": "Pending", "confirmed": "Confirmed", "invalidCard": "Invalid card", "invalidCardHint": "Please check and try again.", "copied": "<PERSON>pied", "time": "Time", "orderId": "Order ID", "from": "From", "to": "To", "mngCards": "Manage cards", "mngWallets": "Manage wallets", "totalAccounts": "{{total}} accounts", "totalCards": "{{total}} cards", "logoutTitle": "Logout", "payTitle": "Pay", "payWithNFCTitle": "NFC", "paymentTitle": "Payment", "walletDetails": "Wallet details", "addressTitle": "Address", "sourceTitle": "Source", "commonTitle": "Common", "cardTitle": "Card", "sinceTitle": "Since", "passkeyTitle": "Passkey", "switchToken": "Switch Token", "unsupportedQRCode": "This QR code is not supported.", "QRCodeTile": "QR Code", "maxTitle": "MAX", "nextTitle": "NEXT", "receiveAddressHint": "Recipient's solana address", "icrc1ReceiveAddressHint": "Recipient's address", "confirmSend": "Confirm Send", "insufficientBalance": "Insufficient balance", "invalidSolanaAddress": "Invalid solana address", "invalidReceiveAddress": "Invalid recipient's address", "txBroadcast": "Transaction has been broadcast", "txHistory": "Transaction History", "copyTitle": "Copy", "shareTitle": "Share", "noNotification": "No notification", "notificationTitle": "Notification", "qrcodeHint": "Only supports Solana assets on Solana blockchain", "icpQrcodeHint": "Only supports ICP assets", "walletAddress": "Wallet Address", "noAccountHint": "Don't have an account.", "aboutUs": "About Us", "owTitle": "Official Website", "miningStage1Title": "Stage 1: Limited Release", "miningStage1Content": "Grab Your POS Terminals now—SOL airdrops, double UPN Token rewards await!", "miningStage2Title": "Stage 2: Southeast Asia Expansion", "miningStage2Content": "Over 1 Million POS Terminals on our nodes already! — join the mining revolution with regional airdrops and unstoppable growth!", "miningStage3Title": "Stage 3: Going Global", "miningStage3Content": "Our POS Terminals are launching worldwide with a delocal strategy— big surprises and a mining revolution awaits!", "orderPaymentStatusHint": "The current order has been paid.", "payFailed": "Payment failed. Please try again later.", "noData": "No data", "signUpWithPasskey": "Sign up", "restoreWithPasskey": "Rest<PERSON>", "loginHint": "You can log in to UpNetwork using a passkey. If it's your first time using UpNetwork and you haven't registered yet, please sign up first.", "tapToPay": "Tap to pay", "notSupportNFC": "NFC is not supported on this device.", "nfcDisabled": "Please enable NFC in settings.", "kyc": "KYC Verification", "kycDesc": "Complete your identity verification", "invite": "Invite", "Invite Your Friend": "Invite Your Friend", "And get cash rewards!": "And get cash rewards!", "Invite Friends": "Invite Friends", "verifyIdentity": "Verify Identity", "limitTip": "Due to large transaction, you need to verify your identity", "Top-up": "Top-up", "getAuthTokenFailed": "Get auth token failed", "payWithScan": "Pay via QR Code", "payWithScanDesc": "Pay effortlessly with crypto anywhere", "iHaveOne": "I already have one", "setupPasskeyTitle": "Create a passkey", "setupPasskeyDesc": "Log in quickly, keep your account safe, and use it on various devices. Passkeys are a safer option than passwords stored on your device.", "setupPasskeyButton": "Setup passkey", "setupPasskeyTip": "You can manage your passkeys in settings", "Swap": "<PERSON><PERSON><PERSON>"}