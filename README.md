UpNetwork APP Project

## react-native-sqlite-storage

```
node_modules/react-native-sqlite-storage/react-native.config.js

module.exports = {
	dependency: {
		platforms: {
			ios: {
				// project: './platforms/ios/SQLite.xcodeproj'
			},
			android: {
				sourceDir: './platforms/android'
			},
			windows: {
				sourceDir: './platforms/windows',
				solutionFile: 'SQLitePlugin.sln',
				projects: [
					{
						projectFile: 'SQLitePlugin/SQLitePlugin.vcxproj',
						directDependency: true,
					}
				],
			}
		}
	}
}


```

## SPLASH

```
npx react-native-bootsplash generate ./src/assets/images/OntapayLogo.png \
  --platforms=android,ios \
  --background=222222 \
  --logo-width=120 \
  --assets-output=./src/assets/splash
```

## APP ICON

```
npx rn-ml appicon -s ./src/assets/appicon/app_icon.png
```

### IOS Release

```
npm run ios -- --mode="Release"
```

### Android Release

```
keytool -genkey -v -keystore ~/up-network-keystore-dev.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upnetwork-dev

keytool -list -v -keystore ~/up-network-keystore-dev.jks  -alias upnetwork-dev

keytool -exportcert -alias upnetwork-dev -keystore ~/up-network-keystore-dev.jks -storepass <password> | openssl sha256 -binary | openssl base64


 keytool -exportcert -alias upnetwork -keystore ~/up-network-keystore.jks -storepass <password> | openssl sha256 -binary | openssl base64
```

### clean config cache

```
npm uninstall react-native-config
cd ios
pod install
cd ..
npm install react-native-config
cd ios
pod install
cd ..
```
