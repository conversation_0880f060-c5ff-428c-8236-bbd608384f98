/**
 * @format
 */
import 'react-native-url-polyfill/auto';
import 'react-native-get-random-values';

import { <PERSON><PERSON><PERSON> } from 'buffer';
import { AppRegistry } from 'react-native';
import App from './src/App';
import { name as appName } from './app.json';
import { TextDecoder, TextEncoder } from 'text-encoding';

global.Buffer = Buffer;

if (typeof global.TextDecoder === 'undefined') {
    global.TextDecoder = TextDecoder;
}

// Polyfill TextEncoder if needed
if (typeof global.TextEncoder === 'undefined') {
    global.TextEncoder = TextEncoder;
}

AppRegistry.registerComponent(appName, () => App);
