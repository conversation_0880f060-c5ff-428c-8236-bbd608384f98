module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  extends: [
    'airbnb-typescript',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:@typescript-eslint/recommended',
  ],
  plugins: ['react', 'react-hooks', '@typescript-eslint'],
  settings: {
    react: {
      version: 'detect', // Automatically detect the react version
      pragma: 'React', // For React 17 and earlier (doesn't matter much now)
    },
  },
  rules: {
    'react/jsx-runtime': 'error', // Ensure JSX runtime is being used correctly
    'react/react-in-jsx-scope': 'off', // Disable the React scope rule (since React 17 JSX Transform doesn't require it)
  },
};
