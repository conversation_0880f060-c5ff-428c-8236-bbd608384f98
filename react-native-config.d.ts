declare module 'react-native-config' {
  export interface NativeConfig {
    APP_MODE: string;
    BACKEND_HOST: String;
    NODEJS_BACKEND_HOST: string;
    APP_CANISTER_HOST: string;
    APP_CANISTER_ID: string;
    SOLANA_DEVNET_RPC: string;
    SOLANA_MAINNET_RPC: string;
    SOLANA_SCAN: string;
    UP_HOST: string;
    INVITE_LINK: string;
    INVITE_HOST: string;
    NODEJS_BACKEND_HOST: string;
    VITE_APP_BRIDGE_SOLANA_NETWORK: string;
    VITE_APP_SOLANA_PROGRAM: string;
    VITE_APP_BRIDGE_SOLANA_USDC_MINT: string;
    VITE_APP_BRIDGE_SERVICE: string;
    VITE_APP_CANISTER_HOST: string;
    VITE_APP_BRIDGE_ICP_VUSD: string;
    VITE_APP_BRIDGE_ICP_POOL: string;
    VITE_APP_EXPLORER_SERVICE: string;
  }

  export const Config: NativeConfig;
  export default Config;
}
