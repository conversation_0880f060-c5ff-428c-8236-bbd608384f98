{"name": "one.upnetwork.wallet", "version": "1.0.5", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@dfinity/agent": "^2.1.3", "@dfinity/candid": "^2.1.3", "@dfinity/identity": "^2.1.3", "@dfinity/ledger-icrc": "^2.6.2", "@dfinity/principal": "^2.1.3", "@dfinity/utils": "^2.6.0", "@os-team/i18next-react-native-language-detector": "^1.1.3", "@project-serum/anchor": "^0.26.0", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/native": "^7.0.0", "@react-navigation/native-stack": "^7.0.0", "@solana/spl-memo": "^0.2.5", "@solana/spl-token": "^0.4.9", "@solana/web3.js": "^1.95.5", "@sumsub/react-native-mobilesdk-module": "^1.35.1", "@types/isomorphic-fetch": "^0.0.39", "assert": "^2.1.0", "bignumber.js": "^9.1.2", "buffer": "^6.0.3", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "frames-react-native": "^1.1.9", "i18next": "^24.0.5", "isomorphic-fetch": "^3.0.0", "jotai": "^2.12.4", "lottie-react-native": "^7.2.2", "react": "18.3.1", "react-i18next": "^15.1.3", "react-native": "0.76.1", "react-native-bootsplash": "^6.3.2", "react-native-config": "^1.5.5", "react-native-device-info": "^14.0.2", "react-native-gesture-handler": "2.25.0", "react-native-get-random-values": "^1.11.0", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.3.0", "react-native-markdown-display": "^7.0.2", "react-native-mmkv": "^2.12.2", "react-native-nfc-manager": "^3.16.1", "react-native-onesignal": "^5.2.8", "react-native-pager-view": "^6.8.0", "react-native-passkey": "^3.1.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.0.0", "react-native-share": "^12.0.3", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "^15.11.2", "react-native-tab-view": "^4.1.0", "react-native-toast-message": "^2.2.1", "react-native-url-polyfill": "^2.0.0", "react-native-view-shot": "^4.0.3", "react-native-vision-camera": "^4.6.3", "react-qr-code": "^2.0.15", "text-encoding": "^0.7.0", "twrnc": "^4.6.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.0", "@react-native-community/cli-platform-android": "15.0.0", "@react-native-community/cli-platform-ios": "15.0.0", "@react-native/babel-preset": "0.76.1", "@react-native/eslint-config": "0.76.1", "@react-native/metro-config": "0.76.1", "@react-native/typescript-config": "0.76.1", "@types/crypto-js": "^4.2.2", "@types/react": "^18.2.6", "@types/react-native-sqlite-storage": "^6.0.5", "@types/react-test-renderer": "^18.0.0", "@types/text-encoding": "^0.0.39", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}