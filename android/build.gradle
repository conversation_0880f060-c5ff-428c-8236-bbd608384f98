buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
    }
    repositories {
        maven {
            url 'https://maven.aliyun.com/repository/public/'
        }
        maven {
            url 'https://maven.aliyun.com/repository/central/'
        }
        maven {
            url 'https://maven.aliyun.com/repository/gradle-plugin/'
        }
        maven {
            url 'https://maven.aliyun.com/repository/google/'
        }
        maven { url "https://maven.sumsub.com/repository/maven-public/" }
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.9.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.3.15'
    }
}

apply plugin: "com.facebook.react.rootproject"


allprojects {
    repositories {
        maven {
            url 'https://maven.aliyun.com/repository/public/'
        }
        maven {
            url 'https://maven.aliyun.com/repository/central/'
        }
        maven {
            url 'https://maven.aliyun.com/repository/gradle-plugin/'
        }
        maven {
            url 'https://maven.aliyun.com/repository/google/'
        }
        maven { url "https://maven.sumsub.com/repository/maven-public/" }
        mavenCentral()
        maven { url "https://dl.google.com/dl/android/maven2" }
    }
}

